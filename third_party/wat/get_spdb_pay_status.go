package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"marketplace_service/pkg/utils"
)

type GetSpdbPayStatusResp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data bool   `json:"data" form:"data"`
}

func GetSpdbPayStatus(ctx context.Context, resaleOrderId string) (bool, error) {
	rsp := &GetSpdbPayStatusResp{}
	req, err := request.Wat()
	if err != nil {
		return false, err
	}
	err = req.Call(
		ctx,
		"open/wallet/v1/spdb_pay_status",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"resale_order_id": resaleOrderId}),
		utilRequest.WithMethodGet(),
	)
	if err != nil {
		return false, err
	}

	if rsp.Code != 0 {
		return false, response.Fail.SetMsg(rsp.Desc)
	}
	log.Ctx(ctx).Infof("GetSpdbPayStatus，resaleOrderId:%+v，返回数据：%+v", resaleOrderId, utils.Obj2JsonStr(rsp))
	return rsp.Data, err
}
