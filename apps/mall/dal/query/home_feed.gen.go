// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newHomeFeed(db *gorm.DB, opts ...gen.DOOption) homeFeed {
	_homeFeed := homeFeed{}

	_homeFeed.homeFeedDo.UseDB(db, opts...)
	_homeFeed.homeFeedDo.UseModel(&model.HomeFeed{})

	tableName := _homeFeed.homeFeedDo.TableName()
	_homeFeed.ALL = field.NewAsterisk(tableName)
	_homeFeed.ID = field.NewInt64(tableName, "id")
	_homeFeed.Status = field.NewInt32(tableName, "status")
	_homeFeed.ItemMallStatus = field.NewInt32(tableName, "item_mall_status")
	_homeFeed.ItemResaleStatus = field.NewInt32(tableName, "item_resale_status")
	_homeFeed.ItemID = field.NewString(tableName, "item_id")
	_homeFeed.SkuID = field.NewString(tableName, "sku_id")
	_homeFeed.IPID = field.NewString(tableName, "ip_id")
	_homeFeed.ItemName = field.NewString(tableName, "item_name")
	_homeFeed.ItemSpecs = field.NewString(tableName, "item_specs")
	_homeFeed.ItemIconURL = field.NewString(tableName, "item_icon_url")
	_homeFeed.LastTradeTime = field.NewTime(tableName, "last_trade_time")
	_homeFeed.ResaleCount = field.NewInt32(tableName, "resale_count")
	_homeFeed.SalePrice = field.NewInt64(tableName, "sale_price")
	_homeFeed.Priority = field.NewInt32(tableName, "priority")
	_homeFeed.SalesVolume = field.NewInt32(tableName, "sales_volume")
	_homeFeed.CreatedAt = field.NewTime(tableName, "created_at")
	_homeFeed.UpdatedAt = field.NewTime(tableName, "updated_at")
	_homeFeed.IsDel = field.NewField(tableName, "is_del")
	_homeFeed.HomeFeedStats = homeFeedHasOneHomeFeedStats{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("HomeFeedStats", "model.HomeFeedStats"),
	}

	_homeFeed.fillFieldMap()

	return _homeFeed
}

// homeFeed 商城首页表
type homeFeed struct {
	homeFeedDo

	ALL              field.Asterisk
	ID               field.Int64  // 主键
	Status           field.Int32  // 状态
	ItemMallStatus   field.Int32  // 商品直购状态
	ItemResaleStatus field.Int32  // 商品转卖状态
	ItemID           field.String // 商品ID
	SkuID            field.String // 商品 sku_id
	IPID             field.String // 商品 ip_id
	ItemName         field.String // 商品名称
	ItemSpecs        field.String // 商品属性
	ItemIconURL      field.String // 商品主图
	LastTradeTime    field.Time   // 最后交易时间
	ResaleCount      field.Int32  // 转卖数量
	SalePrice        field.Int64  // 售价（分）
	Priority         field.Int32  // 优先级
	SalesVolume      field.Int32  // 销量（直购+转售）
	CreatedAt        field.Time   // 创建时间
	UpdatedAt        field.Time   // 更新时间
	IsDel            field.Field  // 是否删除【0->未删除; 1->删除】
	HomeFeedStats    homeFeedHasOneHomeFeedStats

	fieldMap map[string]field.Expr
}

func (h homeFeed) Table(newTableName string) *homeFeed {
	h.homeFeedDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h homeFeed) As(alias string) *homeFeed {
	h.homeFeedDo.DO = *(h.homeFeedDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *homeFeed) updateTableName(table string) *homeFeed {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewInt64(table, "id")
	h.Status = field.NewInt32(table, "status")
	h.ItemMallStatus = field.NewInt32(table, "item_mall_status")
	h.ItemResaleStatus = field.NewInt32(table, "item_resale_status")
	h.ItemID = field.NewString(table, "item_id")
	h.SkuID = field.NewString(table, "sku_id")
	h.IPID = field.NewString(table, "ip_id")
	h.ItemName = field.NewString(table, "item_name")
	h.ItemSpecs = field.NewString(table, "item_specs")
	h.ItemIconURL = field.NewString(table, "item_icon_url")
	h.LastTradeTime = field.NewTime(table, "last_trade_time")
	h.ResaleCount = field.NewInt32(table, "resale_count")
	h.SalePrice = field.NewInt64(table, "sale_price")
	h.Priority = field.NewInt32(table, "priority")
	h.SalesVolume = field.NewInt32(table, "sales_volume")
	h.CreatedAt = field.NewTime(table, "created_at")
	h.UpdatedAt = field.NewTime(table, "updated_at")
	h.IsDel = field.NewField(table, "is_del")

	h.fillFieldMap()

	return h
}

func (h *homeFeed) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *homeFeed) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 19)
	h.fieldMap["id"] = h.ID
	h.fieldMap["status"] = h.Status
	h.fieldMap["item_mall_status"] = h.ItemMallStatus
	h.fieldMap["item_resale_status"] = h.ItemResaleStatus
	h.fieldMap["item_id"] = h.ItemID
	h.fieldMap["sku_id"] = h.SkuID
	h.fieldMap["ip_id"] = h.IPID
	h.fieldMap["item_name"] = h.ItemName
	h.fieldMap["item_specs"] = h.ItemSpecs
	h.fieldMap["item_icon_url"] = h.ItemIconURL
	h.fieldMap["last_trade_time"] = h.LastTradeTime
	h.fieldMap["resale_count"] = h.ResaleCount
	h.fieldMap["sale_price"] = h.SalePrice
	h.fieldMap["priority"] = h.Priority
	h.fieldMap["sales_volume"] = h.SalesVolume
	h.fieldMap["created_at"] = h.CreatedAt
	h.fieldMap["updated_at"] = h.UpdatedAt
	h.fieldMap["is_del"] = h.IsDel

}

func (h homeFeed) clone(db *gorm.DB) homeFeed {
	h.homeFeedDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h homeFeed) replaceDB(db *gorm.DB) homeFeed {
	h.homeFeedDo.ReplaceDB(db)
	return h
}

type homeFeedHasOneHomeFeedStats struct {
	db *gorm.DB

	field.RelationField
}

func (a homeFeedHasOneHomeFeedStats) Where(conds ...field.Expr) *homeFeedHasOneHomeFeedStats {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a homeFeedHasOneHomeFeedStats) WithContext(ctx context.Context) *homeFeedHasOneHomeFeedStats {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a homeFeedHasOneHomeFeedStats) Session(session *gorm.Session) *homeFeedHasOneHomeFeedStats {
	a.db = a.db.Session(session)
	return &a
}

func (a homeFeedHasOneHomeFeedStats) Model(m *model.HomeFeed) *homeFeedHasOneHomeFeedStatsTx {
	return &homeFeedHasOneHomeFeedStatsTx{a.db.Model(m).Association(a.Name())}
}

type homeFeedHasOneHomeFeedStatsTx struct{ tx *gorm.Association }

func (a homeFeedHasOneHomeFeedStatsTx) Find() (result *model.HomeFeedStats, err error) {
	return result, a.tx.Find(&result)
}

func (a homeFeedHasOneHomeFeedStatsTx) Append(values ...*model.HomeFeedStats) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a homeFeedHasOneHomeFeedStatsTx) Replace(values ...*model.HomeFeedStats) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a homeFeedHasOneHomeFeedStatsTx) Delete(values ...*model.HomeFeedStats) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a homeFeedHasOneHomeFeedStatsTx) Clear() error {
	return a.tx.Clear()
}

func (a homeFeedHasOneHomeFeedStatsTx) Count() int64 {
	return a.tx.Count()
}

type homeFeedDo struct{ gen.DO }

type IHomeFeedDo interface {
	gen.SubQuery
	Debug() IHomeFeedDo
	WithContext(ctx context.Context) IHomeFeedDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IHomeFeedDo
	WriteDB() IHomeFeedDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IHomeFeedDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IHomeFeedDo
	Not(conds ...gen.Condition) IHomeFeedDo
	Or(conds ...gen.Condition) IHomeFeedDo
	Select(conds ...field.Expr) IHomeFeedDo
	Where(conds ...gen.Condition) IHomeFeedDo
	Order(conds ...field.Expr) IHomeFeedDo
	Distinct(cols ...field.Expr) IHomeFeedDo
	Omit(cols ...field.Expr) IHomeFeedDo
	Join(table schema.Tabler, on ...field.Expr) IHomeFeedDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IHomeFeedDo
	RightJoin(table schema.Tabler, on ...field.Expr) IHomeFeedDo
	Group(cols ...field.Expr) IHomeFeedDo
	Having(conds ...gen.Condition) IHomeFeedDo
	Limit(limit int) IHomeFeedDo
	Offset(offset int) IHomeFeedDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IHomeFeedDo
	Unscoped() IHomeFeedDo
	Create(values ...*model.HomeFeed) error
	CreateInBatches(values []*model.HomeFeed, batchSize int) error
	Save(values ...*model.HomeFeed) error
	First() (*model.HomeFeed, error)
	Take() (*model.HomeFeed, error)
	Last() (*model.HomeFeed, error)
	Find() ([]*model.HomeFeed, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.HomeFeed, err error)
	FindInBatches(result *[]*model.HomeFeed, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.HomeFeed) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IHomeFeedDo
	Assign(attrs ...field.AssignExpr) IHomeFeedDo
	Joins(fields ...field.RelationField) IHomeFeedDo
	Preload(fields ...field.RelationField) IHomeFeedDo
	FirstOrInit() (*model.HomeFeed, error)
	FirstOrCreate() (*model.HomeFeed, error)
	FindByPage(offset int, limit int) (result []*model.HomeFeed, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IHomeFeedDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (h homeFeedDo) Debug() IHomeFeedDo {
	return h.withDO(h.DO.Debug())
}

func (h homeFeedDo) WithContext(ctx context.Context) IHomeFeedDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h homeFeedDo) ReadDB() IHomeFeedDo {
	return h.Clauses(dbresolver.Read)
}

func (h homeFeedDo) WriteDB() IHomeFeedDo {
	return h.Clauses(dbresolver.Write)
}

func (h homeFeedDo) Session(config *gorm.Session) IHomeFeedDo {
	return h.withDO(h.DO.Session(config))
}

func (h homeFeedDo) Clauses(conds ...clause.Expression) IHomeFeedDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h homeFeedDo) Returning(value interface{}, columns ...string) IHomeFeedDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h homeFeedDo) Not(conds ...gen.Condition) IHomeFeedDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h homeFeedDo) Or(conds ...gen.Condition) IHomeFeedDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h homeFeedDo) Select(conds ...field.Expr) IHomeFeedDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h homeFeedDo) Where(conds ...gen.Condition) IHomeFeedDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h homeFeedDo) Order(conds ...field.Expr) IHomeFeedDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h homeFeedDo) Distinct(cols ...field.Expr) IHomeFeedDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h homeFeedDo) Omit(cols ...field.Expr) IHomeFeedDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h homeFeedDo) Join(table schema.Tabler, on ...field.Expr) IHomeFeedDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h homeFeedDo) LeftJoin(table schema.Tabler, on ...field.Expr) IHomeFeedDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h homeFeedDo) RightJoin(table schema.Tabler, on ...field.Expr) IHomeFeedDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h homeFeedDo) Group(cols ...field.Expr) IHomeFeedDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h homeFeedDo) Having(conds ...gen.Condition) IHomeFeedDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h homeFeedDo) Limit(limit int) IHomeFeedDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h homeFeedDo) Offset(offset int) IHomeFeedDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h homeFeedDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IHomeFeedDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h homeFeedDo) Unscoped() IHomeFeedDo {
	return h.withDO(h.DO.Unscoped())
}

func (h homeFeedDo) Create(values ...*model.HomeFeed) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h homeFeedDo) CreateInBatches(values []*model.HomeFeed, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h homeFeedDo) Save(values ...*model.HomeFeed) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h homeFeedDo) First() (*model.HomeFeed, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeed), nil
	}
}

func (h homeFeedDo) Take() (*model.HomeFeed, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeed), nil
	}
}

func (h homeFeedDo) Last() (*model.HomeFeed, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeed), nil
	}
}

func (h homeFeedDo) Find() ([]*model.HomeFeed, error) {
	result, err := h.DO.Find()
	return result.([]*model.HomeFeed), err
}

func (h homeFeedDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.HomeFeed, err error) {
	buf := make([]*model.HomeFeed, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h homeFeedDo) FindInBatches(result *[]*model.HomeFeed, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h homeFeedDo) Attrs(attrs ...field.AssignExpr) IHomeFeedDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h homeFeedDo) Assign(attrs ...field.AssignExpr) IHomeFeedDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h homeFeedDo) Joins(fields ...field.RelationField) IHomeFeedDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h homeFeedDo) Preload(fields ...field.RelationField) IHomeFeedDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h homeFeedDo) FirstOrInit() (*model.HomeFeed, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeed), nil
	}
}

func (h homeFeedDo) FirstOrCreate() (*model.HomeFeed, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeed), nil
	}
}

func (h homeFeedDo) FindByPage(offset int, limit int) (result []*model.HomeFeed, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h homeFeedDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h homeFeedDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h homeFeedDo) Delete(models ...*model.HomeFeed) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *homeFeedDo) withDO(do gen.Dao) *homeFeedDo {
	h.DO = *do.(*gen.DO)
	return h
}
