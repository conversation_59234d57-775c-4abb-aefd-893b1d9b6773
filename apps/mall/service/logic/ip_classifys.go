package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/global"
	"marketplace_service/pkg/cache"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/tmt"
)

// GetIpInfo 获取ip详情
func GetIpInfo(ctx context.Context, id string) (*tmt.GetIpInfo, error) {
	var ipInfo *tmt.GetIpInfo

	cacheKey := constant.GetIpInfoKey(id)
	err := cache.GetFromCache(ctx, cacheKey, &ipInfo)
	if errors.Is(err, redis.Nil) {
		ipInfo, err = GetIpInfoByRemote(ctx, id)
		if err != nil {
			log.Ctx(ctx).Errorf("redis获取ip信息失败 err:%+v", err)
			return nil, err
		}
	} else if err != nil {
		log.Ctx(ctx).Errorf("redis获取ip信息失败 err:%+v", err)
		return nil, err
	}
	return ipInfo, nil
}

// GetIpInfoByRemote 获取ip详情(不使用缓存,强制刷新缓存)
func GetIpInfoByRemote(ctx context.Context, id string) (*tmt.GetIpInfo, error) {
	var ipInfo *tmt.GetIpInfo

	ipInfos, err := tmt.GetIpInfos(ctx, []string{id})
	if err != nil {
		log.Ctx(ctx).Errorf("查询ip错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询ip错误")
	}
	if len(ipInfos) > 0 {
		ipInfo = ipInfos[0]
		cacheKey := constant.GetIpInfoKey(id)
		if err := cache.SetToCache(ctx, cacheKey, ipInfo, constant.IpInfoTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置ip详情缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置ip详情缓存成功 userId:%v", id)
	}
	return ipInfo, err
}

// GetIpInfos 获取ip详情列表
func GetIpInfos(ctx context.Context, ids ...string) ([]*tmt.GetIpInfo, error) {
	result := make([]*tmt.GetIpInfo, 0)
	var queryUserIds []string
	var userIdRedisKey []string
	for _, id := range ids {
		userIdRedisKey = append(userIdRedisKey, constant.GetIpInfoKey(id))
	}
	ipInfoStrList, _ := global.REDIS.MGet(ctx, userIdRedisKey...).Result()

	if ipInfoStrList != nil && len(ipInfoStrList) > 0 {
		for k, userStr := range ipInfoStrList {
			if userStr != nil && userStr != "" {
				ipInfo := &tmt.GetIpInfo{}
				err := utils.MsgpackUnmarshal([]byte(userStr.(string)), ipInfo)
				if err != nil {
					log.Ctx(ctx).Errorf("redisip详情转换结构体失败 err:%+v", err)
				} else {
					result = append(result, ipInfo)
				}
			} else {
				queryUserIds = append(queryUserIds, ids[k])
			}
		}
	}

	if len(queryUserIds) > 0 {
		ipInfos, err := tmt.GetIpInfos(ctx, queryUserIds)
		if err != nil {
			log.Ctx(ctx).Errorf("查询ip列表错误 err:%+v", err)
			return nil, response.ParamErr.SetMsg("查询ip列表错误")
		}

		for _, ipInfo := range ipInfos {
			result = append(result, ipInfo)
			cacheKey := constant.GetIpInfoKey(ipInfo.Id)
			if err := cache.SetToCache(ctx, cacheKey, ipInfo, constant.IpInfoTTL); err != nil {
				log.Ctx(ctx).Errorf("redis设置ip详情缓存失败 err:%+v", err)
			}
			log.Ctx(ctx).Infof("redis设置ip详情缓存成功 userId:%v", ipInfo.Id)
		}
	}
	return result, nil
}

// GetIpInfoMap 获取ip详情切片
func GetIpInfoMap(ctx context.Context, ids ...string) (map[string]*tmt.GetIpInfo, error) {
	ipInfos, err := GetIpInfos(ctx, ids...)
	if err != nil {
		return nil, err
	}
	ipInfoMap := make(map[string]*tmt.GetIpInfo, len(ipInfos))
	for _, ipInfo := range ipInfos {
		ipInfoMap[ipInfo.Id] = ipInfo
	}
	return ipInfoMap, nil
}

// GetIpInfosByRemote 获取ip详情列表(不使用缓存,强制刷新缓存)
func GetIpInfosByRemote(ctx context.Context, ids ...string) ([]*tmt.GetIpInfo, error) {
	result := make([]*tmt.GetIpInfo, 0)
	ipInfos, err := tmt.GetIpInfos(ctx, ids)
	if err != nil {
		log.Ctx(ctx).Errorf("查询remoteip列表错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询remoteip列表错误")
	}

	for _, ipInfo := range ipInfos {
		result = append(result, ipInfo)
		cacheKey := constant.GetIpInfoKey(ipInfo.Id)
		if err := cache.SetToCache(ctx, cacheKey, ipInfo, constant.IpInfoTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置ip详情缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置ip详情缓存成功 userId:%v", ipInfo.Id)
	}
	return result, nil
}

// GetIpInfosByRemoteMap 获取ip详情切片(不使用缓存,强制刷新缓存)
func GetIpInfosByRemoteMap(ctx context.Context, ids ...string) (map[string]*tmt.GetIpInfo, error) {
	ipInfos, err := GetIpInfosByRemote(ctx, ids...)
	if err != nil {
		return nil, err
	}
	ipInfoMap := make(map[string]*tmt.GetIpInfo, len(ipInfos))
	for _, ipInfo := range ipInfos {
		ipInfoMap[ipInfo.Id] = ipInfo
	}
	return ipInfoMap, nil
}
