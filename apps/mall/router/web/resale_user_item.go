package web

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/web"
)

func ResaleUserItem(router *gin.RouterGroup) {
	group := router.Group("/resale_user_item")
	{
		// 查询转卖商品持仓商品列表
		group.GET("/item_list", web.GetWebResaleUserItemListByItem)
		// 获取某个转卖商品的持仓列表
		group.GET("/user_item_list", web.GetWebResaleUserItemListByUserItem)
		// 取消出售
		group.POST("/cancel", web.CancelResaleUserItemFromWeb)
		// 获取持仓商品交易信息
		group.GET("/trade_info", web.GetWebResaleUserItemTradeInfo)
	}
}
