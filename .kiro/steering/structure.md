# 项目结构

## 目录组织

```
marketplace_service/
├── apps/                    # 应用模块
│   ├── init_router.go      # 路由初始化
│   └── mall/               # 商城业务模块
│       ├── api/            # 按客户端类型分组的 HTTP 处理器
│       │   ├── admin/      # 管理端接口
│       │   ├── web/        # 客户端网页接口
│       │   └── open/       # 公开 API 接口
│       ├── constant/       # 业务常量和 Redis 键
│       ├── consume/        # Kafka 消息消费者
│       ├── dal/            # 数据访问层
│       │   ├── model/      # 生成的 GORM 模型 (.gen.go)
│       │   └── query/      # 生成的类型安全查询 (.gen.go)
│       ├── define/         # 请求/响应结构体和枚举
│       ├── repo/           # 仓储层（数据访问）
│       ├── service/        # 业务逻辑层
│       │   ├── logic/      # 核心业务逻辑
│       │   ├── locker/     # 分布式锁
│       │   └── warn/       # 警告/通知逻辑
│       └── router/         # 路由定义
├── cmd/                    # 应用入口点
│   ├── main.go            # 主服务入口
│   └── codegen/           # 代码生成工具
├── configs/               # 环境配置
├── docs/                  # 生成的 Swagger 文档
├── global/                # 全局配置和工具
├── pkg/                   # 共享包
│   ├── cache/             # Redis 工具
│   ├── middleware/        # HTTP 中间件
│   ├── pagination/        # 分页助手
│   ├── search/            # 搜索查询构建器
│   └── utils/             # 通用工具
├── third_party/           # 外部服务集成
└── test/                  # 测试文件
```

## 架构模式

### 分层架构
- **API 层**: `apps/*/api/` 中的 HTTP 处理器
- **服务层**: `apps/*/service/` 中的业务逻辑
- **仓储层**: `apps/*/repo/` 中的数据访问
- **模型层**: `apps/*/dal/model/` 中的数据库模型

### 客户端分离
- **admin/**: 后台管理操作
- **web/**: 面向客户的功能
- **open/**: 公开 API 端点

### 代码生成
- 模型和查询在 `dal/` 中自动生成，带有 `.gen.go` 后缀
- 手动业务逻辑在不带 `.gen.go` 的单独文件中

### 命名约定
- 包名: 小写，描述性
- 结构体名: PascalCase 带后缀 (Req/Resp/Data)
- 文件名: snake_case 匹配主要结构体/函数
- 生成文件: `*.gen.go` 后缀

### 请求/响应模式
- 请求结构体: `*Req` 后缀带验证标签
- 响应结构体: `*Resp` 后缀
- 数据传输对象: `*Data` 后缀用于嵌套结构