package service

import (
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/pkg/utils/snowflakeutl"
	"strings"
)

// GetConfigList 获取配置列表
func (s *Service) GetConfigList(req *define.GetConfigListReq) (*define.GetConfigListResp, error) {
	var err error
	resp := define.GetConfigListResp{}
	configKeys := strings.Split(req.ConfigKeys, ",")
	list := make([]*model.CommonConfig, 0)
	r := repo.Query(s.ctx).CommonConfig
	list, err = repo.NewCommonConfigRepo(r.WithContext(s.ctx)).SelectList(search.NewWrapper().Where(r.ConfigKey.In(configKeys...)))
	if err != nil {
		return nil, err
	}
	for _, item := range list {
		resp.ConfigList = append(resp.ConfigList, &define.Config{
			ConfigKey: item.ConfigKey,
			ID:        item.ID,
			Remark:    item.Remark,
			Value:     item.Value,
		})
	}
	return &resp, nil
}

// EditConfig 修改配置
func (s *Service) EditConfig(req *define.EditConfigReq) (*define.EditConfigResp, error) {
	r := repo.Query(s.ctx).CommonConfig
	config, err := repo.NewCommonConfigRepo(r.WithContext(s.ctx)).SelectOne(search.NewWrapper().Where(r.ConfigKey.Eq(req.ConfigKey)))
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	var valueStr string
	switch v := req.Value.(type) {
	case string:
		valueStr = v
	default:
		valueStr = utils.Obj2JsonStr(req.Value)
	}
	if config == nil {
		config = &model.CommonConfig{
			ID:        snowflakeutl.GenerateID(),
			ConfigKey: req.ConfigKey,
			Value:     valueStr,
			CreatedBy: s.GetAdminId(),
			UpdatedBy: s.GetAdminId(),
		}
		if err = repo.NewCommonConfigRepo(r.WithContext(s.ctx)).Save(config); err != nil {
			return nil, err
		}
	} else {
		config.Value = valueStr
		config.UpdatedBy = s.GetAdminId()
		if err = repo.NewCommonConfigRepo(r.WithContext(s.ctx)).UpdateById(config); err != nil {
			return nil, err
		}
	}
	// 判断是否设置转卖标准配置
	if req.ConfigKey == constant.ResaleStandardConfigKey {
		// 解析配置
		var resaleStandardConfig define.ResaleStandardConfig
		if err := json.Unmarshal([]byte(valueStr), &resaleStandardConfig); err != nil {
			return nil, errors.Wrap(err, "解析转卖标准配置失败")
		}
		if resaleStandardConfig.ResaleStatus == enums.ResaleItemResaleStatusOpen.Val() {
			err := logic.HandleResaleStandardConfigOpen(s.ctx)
			if err != nil {
				log.Ctx(s.ctx).Errorf("处理转卖标准配置失败, err:%+v", err)
				return nil, errors.Wrap(err, "处理转卖标准配置失败")
			}
		}
		if resaleStandardConfig.ResaleStatus == enums.ResaleItemResaleStatusClose.Val() {
			err := logic.HandleResaleStandardConfigClose(s.ctx, s.GetAdminId())
			if err != nil {
				log.Ctx(s.ctx).Errorf("处理转卖标准配置失败, err:%+v", err)
				return nil, errors.Wrap(err, "处理转卖标准配置失败")
			}
		}
	}
	return &define.EditConfigResp{
		ID: config.ID,
	}, nil
}

// GetConfigDetail 获取配置详情
func (s *Service) GetConfigDetail(req *define.GetConfigDetailReq) (*define.GetConfigDetailResp, error) {
	cc := repo.GetQuery().CommonConfig
	qw := search.NewWrapper()
	if req.ID > 0 {
		qw.Where(cc.ID.Eq(req.ID))
	} else {
		qw.Where(cc.ConfigKey.Eq(req.ConfigKey))
	}
	config, err := repo.NewCommonConfigRepo(cc.WithContext(s.ctx)).SelectOne(qw)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, errors.Wrap(err, "查询配置详情失败")
	}
	if config == nil {
		return nil, errors.New("配置不存在")
	}
	return &define.GetConfigDetailResp{
		ID:        config.ID,
		ConfigKey: config.ConfigKey,
		Remark:    config.Remark,
		Value:     config.Value,
	}, nil
}
