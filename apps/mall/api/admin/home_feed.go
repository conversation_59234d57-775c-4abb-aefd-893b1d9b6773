package admin

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// RefreshHomeFeed 刷新首页商品
// @Summary 刷新首页商品
// @Description 管理端刷新首页商品
// @Tags 管理端-首页管理
// @x-apifox-folder "管理端/首页管理"
// @Param data body define.RefreshHomeFeedReq true "商品信息"
// @Success 200 {object} response.Data{data=define.RefreshHomeFeedResp}
// @Router /admin/v1/home_feed/refresh [post]
// @Security Bearer
// @produce  json
func RefreshHomeFeed(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.RefreshHomeFeedReq{}, s.RefreshHomeFeed)
}
