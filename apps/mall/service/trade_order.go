package service

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/pkg/errors"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/tmt"
	"marketplace_service/third_party/wae"
	"marketplace_service/third_party/wat"
	"time"
)

// GetAdminTradeOrderList 管理端订单列表
func (s *Service) GetAdminTradeOrderList(req *define.GetAdminTradeOrderListReq) (*define.GetAdminTradeOrderListResp, error) {
	to := repo.GetQuery().TradeOrder
	tradeOrders, count, err := repo.NewTradeOrderRepo(to.WithContext(s.ctx).Preload(to.TradeOrderItem).Order(to.CreatedAt.Desc())).QuickSelectPage(req)
	if err != nil {
		return nil, errors.Wrap(err, "查询商品列表失败")
	}
	if len(tradeOrders) == 0 {
		return &define.GetAdminTradeOrderListResp{
			List:  []*define.GetAdminTradeOrderData{},
			Total: count,
		}, nil
	}
	// 提取商品ID
	var mallItemIDs []int64
	var userIDs []string
	for _, tradeOrder := range tradeOrders {
		userIDs = append(userIDs, tradeOrder.UserID)
		for _, tradeOrderItem := range tradeOrder.TradeOrderItem {
			mallItemIDs = append(mallItemIDs, tradeOrderItem.MallItemID)
		}
	}

	mi := repo.GetQuery().MallItem
	miWarp := search.NewWrapper().Where(mi.ID.In(mallItemIDs...))
	mallItems, err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).SelectList(miWarp)
	if err != nil {
		return nil, err
	}
	itemIds := make([]string, len(mallItems))
	ipIds := make([]string, len(mallItems))
	categoryIds := make([]string, len(mallItems))
	trademarkIds := make([]string, len(mallItems))
	mallItemInfoMap := make(map[int64]*model.MallItem, len(mallItems))
	for i, item := range mallItems {
		itemIds[i] = item.ItemID
		ipIds[i] = item.IPID
		categoryIds[i] = item.CategoryID
		trademarkIds[i] = item.TrademarkID
		mallItemInfoMap[item.ID] = item
	}
	ipInfoMap, _ := logic.GetIpInfoMap(s.ctx, ipIds...)
	categoryInfoMap, _ := logic.GetItemClassifyInfoMap(s.ctx, categoryIds...)
	trademarkInfoMap, _ := logic.GetTrademarksInfoMap(s.ctx, trademarkIds...)
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		return nil, err
	}

	tradeOrderListData := make([]*define.GetAdminTradeOrderData, 0, len(tradeOrders))
	for _, tradeOrder := range tradeOrders {
		tradeOrderItemDatas := make([]*define.GetAdminTradeOrderItemData, 0, len(tradeOrder.TradeOrderItem))
		for _, tradeOrderItem := range tradeOrder.TradeOrderItem {
			mallItemInfo := mallItemInfoMap[tradeOrderItem.MallItemID]
			tradeOrderItemData := &define.GetAdminTradeOrderItemData{
				MallItemID:     tradeOrderItem.MallItemID,
				ItemID:         tradeOrderItem.ItemID,
				SkuID:          tradeOrderItem.SkuID,
				ItemIconURL:    tradeOrderItem.ItemIconURL,
				ItemName:       tradeOrderItem.ItemName,
				ItemSpecs:      tradeOrderItem.ItemSpecs,
				SalePrice:      tradeOrderItem.SalePrice,
				DiscountAmount: tradeOrderItem.DiscountAmount,
				Quantity:       tradeOrderItem.Quantity,
				FreightAmount:  tradeOrder.FreightAmount,
			}
			if mallItemInfo != nil {
				tradeOrderItemData.TrademarkID = mallItemInfo.TrademarkID
				tradeOrderItemData.CategoryID = mallItemInfo.CategoryID
				tradeOrderItemData.IPID = mallItemInfo.IPID
			}
			if ipInfo, ok := ipInfoMap[mallItemInfo.IPID]; ok {
				tradeOrderItemData.IPName = ipInfo.Name
			}
			if categoryInfo, ok := categoryInfoMap[mallItemInfo.CategoryID]; ok {
				tradeOrderItemData.CategoryName = categoryInfo.Name
			}
			if trademarkInfo, ok := trademarkInfoMap[mallItemInfo.TrademarkID]; ok {
				tradeOrderItemData.TrademarkName = trademarkInfo.Name
			}
			tradeOrderItemDatas = append(tradeOrderItemDatas, tradeOrderItemData)
		}
		tradeOrderData := &define.GetAdminTradeOrderData{
			ID:            tradeOrder.ID,
			OrderStatus:   tradeOrder.OrderStatus,
			UserId:        tradeOrder.UserID,
			TotalAmount:   tradeOrder.TotalAmount,
			PayAmount:     tradeOrder.PayAmount,
			FreightAmount: tradeOrder.FreightAmount,
			CreatedAt:     tradeOrder.CreatedAt,
			OrderItems:    tradeOrderItemDatas,
		}
		if userDetail, ok := userDetailMap[tradeOrder.UserID]; ok {
			tradeOrderData.UserNickname = userDetail.Nickname
		}
		tradeOrderListData = append(tradeOrderListData, tradeOrderData)
	}
	return &define.GetAdminTradeOrderListResp{
		List:  tradeOrderListData,
		Total: count,
	}, nil
}

// GetAdminTradeOrderDetail 管理端订单详情
func (s *Service) GetAdminTradeOrderDetail(req *define.GetAdminTradeOrderDetailReq) (*define.GetAdminTradeOrderDetailResp, error) {
	to := repo.GetQuery().TradeOrder
	tradeOrder, err := repo.NewTradeOrderRepo(to.WithContext(s.ctx).Preload(to.TradeOrderItem)).SelectOne(search.NewWrapper().Where(to.ID.Eq(req.ID)))
	if err != nil {
		return nil, errors.Wrap(err, "查询订单详情失败")
	}
	if tradeOrder == nil {
		return nil, errors.New("订单不存在")
	}
	itemIDs := make([]string, 0, len(tradeOrder.TradeOrderItem))
	mallItemIDs := make([]int64, 0, len(tradeOrder.TradeOrderItem))
	for _, tradeOrderItem := range tradeOrder.TradeOrderItem {
		itemIDs = append(itemIDs, tradeOrderItem.ItemID)
		mallItemIDs = append(mallItemIDs, tradeOrderItem.MallItemID)
	}
	userDetail, err := logic.GetUserDetail(s.ctx, tradeOrder.UserID)
	if err != nil {
		return nil, err
	}
	itemInfoMap, err := logic.GetItemInfoMap(s.ctx, itemIDs...)
	if err != nil {
		return nil, err
	}
	mi := repo.GetQuery().MallItem
	miWarp := search.NewWrapper().Where(mi.ID.In(mallItemIDs...))
	mallItems, err := repo.NewMallItemRepo(mi.WithContext(s.ctx)).SelectList(miWarp)
	if err != nil {
		return nil, err
	}
	mallItemInfoMap := make(map[int64]*model.MallItem, len(mallItems))
	for _, item := range mallItems {
		mallItemInfoMap[item.ID] = item
	}
	tradeOrderItemDatas := make([]*define.GetAdminTradeOrderItemData, 0, len(tradeOrder.TradeOrderItem))
	for _, tradeOrderItem := range tradeOrder.TradeOrderItem {
		itemInfo := itemInfoMap[tradeOrderItem.ItemID]
		mallItemInfo := mallItemInfoMap[tradeOrderItem.MallItemID]
		tradeOrderItemData := &define.GetAdminTradeOrderItemData{
			MallItemID:     tradeOrderItem.MallItemID,
			ItemID:         tradeOrderItem.ItemID,
			SkuID:          tradeOrderItem.SkuID,
			ItemIconURL:    tradeOrderItem.ItemIconURL,
			ItemName:       tradeOrderItem.ItemName,
			ItemSpecs:      tradeOrderItem.ItemSpecs,
			SalePrice:      tradeOrderItem.SalePrice,
			DiscountAmount: tradeOrderItem.DiscountAmount,
			Quantity:       tradeOrderItem.Quantity,
			FreightAmount:  tradeOrder.FreightAmount,
		}
		if mallItemInfo != nil {
			tradeOrderItemData.TrademarkID = mallItemInfo.TrademarkID
			tradeOrderItemData.CategoryID = mallItemInfo.CategoryID
			tradeOrderItemData.IPID = mallItemInfo.IPID
		}
		if itemInfo != nil {
			for _, ipInfo := range itemInfo.IpInfo {
				if ipInfo.ID == tradeOrderItemData.IPID {
					tradeOrderItemData.IPName = ipInfo.Name
					break
				}
			}
			for _, itemClassifyInfo := range itemInfo.ItemClassifyInfo {
				if itemClassifyInfo.ID == tradeOrderItemData.CategoryID {
					tradeOrderItemData.CategoryName = itemClassifyInfo.Name
					break
				}
			}
			for _, trademarkInfo := range itemInfo.TrademarkInfo {
				if trademarkInfo.ID == tradeOrderItemData.TrademarkID {
					tradeOrderItemData.TrademarkName = trademarkInfo.Name
					break
				}
			}
		}
		tradeOrderItemDatas = append(tradeOrderItemDatas, tradeOrderItemData)
	}
	freightInfos := make([]*define.GetAdminTradeOrderDetailFreight, 0)
	if tradeOrder.OrderStatus == enums.OrderStatusDelivered.Val() || tradeOrder.OrderStatus == enums.OrderStatusCompleted.Val() {
		tof := repo.GetQuery().TradeOrderFreight
		tradeOrderFreights, err := repo.NewTradeOrderFreightRepo(tof.WithContext(s.ctx)).SelectList(search.NewWrapper().Where(tof.OrderID.Eq(tradeOrder.ID)))
		if err != nil {
			return nil, errors.Wrap(err, "查询订单物流信息失败")
		}
		for _, tradeOrderFreight := range tradeOrderFreights {
			freightInfo := &define.GetAdminTradeOrderDetailFreight{
				OrderItemID:     tradeOrderFreight.OrderItemID,
				DeliveryNumber:  tradeOrderFreight.DeliveryNumber,
				DeliveryCompany: tradeOrderFreight.DeliveryCompany,
				DeliveredAt:     tradeOrderFreight.DeliveredAt,
				Records:         tradeOrderFreight.Records,
			}
			freightInfos = append(freightInfos, freightInfo)
		}
	}
	tradeOrderData := &define.GetAdminTradeOrderDetailResp{
		ID:                 tradeOrder.ID,
		OrderType:          tradeOrder.OrderType,
		UserID:             tradeOrder.UserID,
		UserNickname:       userDetail.Nickname,
		OrderStatus:        tradeOrder.OrderStatus,
		PaymentStatus:      tradeOrder.PaymentStatus,
		ShippingStatus:     tradeOrder.ShippingStatus,
		TotalAmount:        tradeOrder.TotalAmount,
		PayAmount:          tradeOrder.PayAmount,
		FreightAmount:      tradeOrder.FreightAmount,
		DiscountAmount:     tradeOrder.DiscountAmount,
		ConsigneeName:      tradeOrder.ConsigneeName,
		ConsigneePhone:     utils.PhoneMix(tradeOrder.ConsigneePhone),
		ConsigneeAddress:   tradeOrder.ConsigneeAddress,
		CreatedAt:          tradeOrder.CreatedAt,
		PaymentTime:        tradeOrder.PaymentTime,
		PaymentMethod:      tradeOrder.PaymentMethod,
		PayOrderID:         tradeOrder.PayOrderID,
		DeliveredAt:        tradeOrder.DeliveredAt,
		SupplyChainOrderId: tradeOrder.SupplyChainOrderID,
		CancelAt:           tradeOrder.CancelAt,
		FinishedAt:         tradeOrder.FinishedAt,
		UserRemark:         tradeOrder.UserRemark,
		CancelType:         tradeOrder.CancelType,
		Terminal:           tradeOrder.Terminal,
		OrderItems:         tradeOrderItemDatas,
		FreightInfos:       freightInfos,
	}
	return tradeOrderData, nil
}

// GetTradeOrderDetail 获取订单详情
func (s *Service) GetTradeOrderDetail(req *define.GetTradeOrderDetailReq) (*define.GetTradeOrderDetailResp, error) {
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.ID.Eq(req.ID), schema.UserID.Eq(req.UserId))
	tradeOrder, err := repo.NewTradeOrderRepo(schema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询订单详情失败")
	}
	return &define.GetTradeOrderDetailResp{
		PayAmount:     tradeOrder.PayAmount,
		FreightAmount: tradeOrder.FreightAmount,
		PaymentStatus: tradeOrder.PaymentStatus,
	}, nil
}

// TradeOrderPaySuccess 订单支付成功回调
func (s *Service) TradeOrderPaySuccess(req *define.TradeOrderPaySuccessReq) (*define.TradeOrderPaySuccessResp, error) {
	log.Ctx(s.ctx).Infof("订单支付成功回调, req:%+v", utils.Obj2JsonStr(req))

	to := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(to.ID.Eq(req.ID))
	tradeOrder, err := repo.NewTradeOrderRepo(to.WithContext(s.ctx).Preload(to.TradeOrderItem)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询订单详情失败")
	}
	var handErr error
	switch {
	case logic.IsDuplicatePayment(tradeOrder, req):
		// 重复支付退款
		handErr = logic.HandleDuplicatePayment(s.ctx, req, tradeOrder)
	case logic.IsOrderClosed(tradeOrder):
		// 订单已关闭退款
		handErr = logic.HandleOrderClosed(s.ctx, req, tradeOrder)
	case logic.IsOrderUnpaid(tradeOrder):
		// 正常支付
		handErr = logic.HandleNormalPayment(s.ctx, req, tradeOrder)
	default:
		return nil, errors.New("未知订单状态")
	}
	if handErr != nil {
		return nil, errors.Wrap(handErr, "处理订单支付成功回调失败")
	}
	return &define.TradeOrderPaySuccessResp{}, nil
}

// TradeOrderRefund 订单支付成功回调
func (s *Service) TradeOrderRefund(req *define.TradeOrderRefundReq) (*define.TradeOrderRefundResp, error) {
	log.Ctx(s.ctx).Infof("订单发起退款, req:%+v", utils.Obj2JsonStr(req))
	refundRes, err := wat.Refund(s.ctx, &wat.RefundForm{
		RechargeOrderId: req.RechargeOrderID,
		Amount:          req.Amount,
	})
	if err != nil {
		return nil, errors.Wrap(err, "订单发起退款失败")
	}
	switch refundRes.Status {
	case wat.RefundSuccess:
		log.Ctx(s.ctx).Errorf("订单发起退款成功, refundRes:%+v", utils.Obj2JsonStr(refundRes))
		break
	case wat.RefundFail:
		log.Ctx(s.ctx).Errorf("订单发起退款失败, refundRes:%+v", utils.Obj2JsonStr(refundRes))
		break
	case wat.RefundUnknown:
		log.Ctx(s.ctx).Errorf("订单发起退款超时, refundRes:%+v", utils.Obj2JsonStr(refundRes))
		break
	}
	return &define.TradeOrderRefundResp{
		Amount: refundRes.RefundAmount,
	}, nil
}

// GetWebTradeOrderList 获取订单列表
func (s *Service) GetWebTradeOrderList(req *define.GetWebTradeOrderListReq) (*define.GetWebTradeOrderListResp, error) {
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.UserID.Eq(s.GetUserId()), schema.IsUserDel.Eq(enums.IsUserDelNo.Val())).OrderBy(schema.ID.Desc())
	if req.OrderStatus != nil {
		wrapper = wrapper.Where(schema.OrderStatus.Eq(*req.OrderStatus))
	}
	tradeOrderList, total, err := repo.NewTradeOrderRepo(schema.WithContext(s.ctx)).SelectPage(wrapper, req)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取订单列表失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	var ids []int64
	for _, tradeOrder := range tradeOrderList {
		ids = append(ids, tradeOrder.ID)
	}
	tradeOrderItemSchema := repo.GetQuery().TradeOrderItem
	tradeOrderItemList, err := repo.NewTradeOrderItemRepo(tradeOrderItemSchema.WithContext(s.ctx)).SelectList(search.NewWrapper().Where(tradeOrderItemSchema.OrderID.In(ids...)))
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取订单列表,订单详情列表失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	tradeOrderItemMap := make(map[int64][]*model.TradeOrderItem)
	for _, tradeOrderItem := range tradeOrderItemList {
		tradeOrderItemMap[tradeOrderItem.OrderID] = append(tradeOrderItemMap[tradeOrderItem.OrderID], tradeOrderItem)
	}
	tradeOrderListData := make([]*define.GetWebTradeOrderData, 0, len(tradeOrderList))
	ids = make([]int64, 0, len(tradeOrderList))
	for _, tradeOrder := range tradeOrderList {
		ids = append(ids, tradeOrder.ID)
		data := &define.GetWebTradeOrderData{
			ID:             tradeOrder.ID,
			OrderStatus:    tradeOrder.OrderStatus,
			PayAmount:      tradeOrder.PayAmount,
			PaymentStatus:  tradeOrder.PaymentStatus,
			ShippingStatus: tradeOrder.ShippingStatus,
			TotalAmount:    tradeOrder.TotalAmount,
		}
		totalPurchaseAmount, ok := tradeOrderItemMap[tradeOrder.ID]
		if ok {
			itemData := make([]*define.GetWebTradeOrderItemData, 0, len(totalPurchaseAmount))
			for _, item := range totalPurchaseAmount {
				itemData = append(itemData, &define.GetWebTradeOrderItemData{
					MallItemID:  item.MallItemID,
					ItemID:      item.ItemID,
					ItemIconURL: item.ItemIconURL,
					ItemName:    item.ItemName,
					SalePrice:   item.SalePrice,
					Quantity:    item.Quantity,
					ItemSpecs:   item.ItemSpecs,
				})
			}
			data.OrderItems = itemData
		}
		data.CountdownSecond = logic.SetCountdownSecond(tradeOrder)
		tradeOrderListData = append(tradeOrderListData, data)
	}
	freightMap := logic.GetTradeOrderFreight(s.ctx, ids)
	for _, item := range tradeOrderListData {
		freightArray, ok := freightMap[item.ID]
		if ok {
			data := make([]*define.GetWebTradeOrderDetailFreight, 0)
			for _, item := range freightArray {
				data = append(data, &define.GetWebTradeOrderDetailFreight{
					DeliveryCompany: item.DeliveryCompany,
					DeliveryNumber:  item.DeliveryNumber,
				})
			}
			item.FreightInfo = data
		}
	}
	return &define.GetWebTradeOrderListResp{
		List:  tradeOrderListData,
		Total: total,
	}, nil
}

// GetWebTradeOrderDetail 获取订单详情
func (s *Service) GetWebTradeOrderDetail(req *define.GetWebTradeOrderDetailReq) (*define.GetWebTradeOrderDetailResp, error) {
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.ID.Eq(req.ID), schema.UserID.Eq(s.GetUserId()))
	tradeOrder, err := repo.NewTradeOrderRepo(schema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单详情失败, req:%+v, err:%+v,", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	if tradeOrder.IsUserDel == enums.IsUserDelYes.Val() {
		return nil, define.MS200015Err
	}
	tradeOrderItemSchema := repo.GetQuery().TradeOrderItem
	tradeOrderItemList, err := repo.NewTradeOrderItemRepo(tradeOrderItemSchema.WithContext(s.ctx)).SelectList(search.NewWrapper().Where(tradeOrderItemSchema.OrderID.Eq(tradeOrder.ID)))
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单列表，订单详情列表失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	resp := &define.GetWebTradeOrderDetailResp{
		ID:               tradeOrder.ID,
		OrderStatus:      tradeOrder.OrderStatus,
		PaymentStatus:    tradeOrder.PaymentStatus,
		ShippingStatus:   tradeOrder.ShippingStatus,
		TotalAmount:      tradeOrder.TotalAmount,
		PayAmount:        tradeOrder.PayAmount,
		FreightAmount:    tradeOrder.FreightAmount,
		DiscountAmount:   tradeOrder.DiscountAmount,
		ConsigneeName:    tradeOrder.ConsigneeName,
		ConsigneePhone:   utils.PhoneMix(tradeOrder.ConsigneePhone),
		ConsigneeAddress: tradeOrder.ConsigneeAddress,
		CreatedAt:        tradeOrder.CreatedAt,
		PaymentTime:      tradeOrder.PaymentTime,
		PaymentMethod:    tradeOrder.PaymentMethod,
		UserRemark:       tradeOrder.UserRemark,
		CancelType:       tradeOrder.CancelType,
	}
	for _, tradeOrderItem := range tradeOrderItemList {
		resp.OrderItems = append(resp.OrderItems, &define.GetWebTradeOrderItemData{
			MallItemID:  tradeOrderItem.MallItemID,
			ItemID:      tradeOrderItem.ItemID,
			ItemIconURL: tradeOrderItem.ItemIconURL,
			ItemName:    tradeOrderItem.ItemName,
			SalePrice:   tradeOrderItem.SalePrice,
			Quantity:    tradeOrderItem.Quantity,
			ItemSpecs:   tradeOrderItem.ItemSpecs,
		})
	}
	if tradeOrder.OrderStatus == enums.OrderStatusDelivered.Val() || tradeOrder.OrderStatus == enums.OrderStatusCompleted.Val() {
		tradeOrderFreightSchema := repo.GetQuery().TradeOrderFreight
		freightList, err := repo.NewTradeOrderFreightRepo(tradeOrderFreightSchema.WithContext(s.ctx)).
			SelectList(search.NewWrapper().Where(tradeOrderFreightSchema.OrderID.Eq(tradeOrder.ID)))
		if err != nil {
			log.Ctx(s.ctx).Errorf("查询物流信息失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
		} else {
			freights := make([]*define.GetWebTradeOrderDetailFreight, 0)
			for _, freight := range freightList {
				freights = append(freights, &define.GetWebTradeOrderDetailFreight{
					DeliveryNumber:  freight.DeliveryNumber,
					DeliveryCompany: freight.DeliveryCompany,
				})
			}
			resp.FreightInfo = freights
		}
	}
	resp.CountdownSecond = logic.SetCountdownSecond(tradeOrder)
	resp.FinishedSecond = logic.SetFinishedSecond(tradeOrder)
	return resp, nil
}

// GetWebTradeOrderStatusStat 查询直购订单状态统计
func (s *Service) GetWebTradeOrderStatusStat(_ *define.GetWebTradeOrderStatusStatReq) (*define.GetWebTradeOrderStatusStatResp, error) {
	schema := repo.GetQuery().TradeOrder
	list, err := repo.NewTradeOrderRepo(schema.WithContext(s.ctx)).GetWebTradeOrderStatusStat(s.GetUserId())
	if err != nil {
		return nil, errors.Wrap(err, "查询订单状态统计失败")
	}

	statusCountMap := make(map[int32]int64, len(list))
	for _, item := range list {
		statusCountMap[item.OrderStatus] = item.Count
	}

	result := make([]*define.GetWebTradeOrderStatusStatData, 0, len(enums.OrderStatusForUserList))
	for _, status := range enums.OrderStatusForUserList {
		if count, ok := statusCountMap[status]; ok {
			result = append(result, &define.GetWebTradeOrderStatusStatData{
				OrderStatus: status,
				Count:       count,
			})
		} else {
			result = append(result, &define.GetWebTradeOrderStatusStatData{
				OrderStatus: status,
				Count:       0,
			})
		}
	}
	return &define.GetWebTradeOrderStatusStatResp{
		List: result,
	}, nil
}

// CancelTradeOrder 取消订单
func (s *Service) CancelTradeOrder(req *define.CancelTradeOrderReq) (*define.CancelTradeOrderResp, error) {
	log.Ctx(s.ctx).Infof("取消订单, req:%+v", utils.Obj2JsonStr(req))
	userId := s.GetUserId()
	err := logic.CancelTradeOrder(s.ctx, req.ID, enums.CancelTypeUser.Val(), &userId)
	if err != nil {
		log.Ctx(s.ctx).Errorf("取消订单失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	to := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(to.ID.Eq(req.ID))
	tradeOrder, err := repo.NewTradeOrderRepo(to.WithContext(s.ctx).Preload(to.TradeOrderItem)).SelectOne(wrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("取消订单,查询订单失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
	}
	// 获取订单项
	err = repo.ExecGenTx(s.ctx, func(tx context.Context) error {
		if err := logic.RollbackMallItemStock(tx, tradeOrder); err != nil {
			return errors.Wrap(err, "回滚库存失败")
		}
		return nil
	})
	if err != nil {
		log.Ctx(s.ctx).Error("回滚库存失败", err)
		return nil, global.CommonErr
	}
	return &define.CancelTradeOrderResp{}, nil
}

// UpdateTradeOrderAddress 修改地址
func (s *Service) UpdateTradeOrderAddress(req *define.UpdateTradeOrderAddressReq) (*define.UpdateTradeOrderAddressResp, error) {
	userId := s.GetUserId()
	schema := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(schema.ID.Eq(req.ID)).Where(schema.UserID.Eq(userId)).Where(schema.OrderStatus.In(
		enums.OrderStatusUnPaid.Val(), enums.OrderStatusUnDelivered.Val()))
	tradeOrder, err := repo.NewTradeOrderRepo(schema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询订单详情失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	// 获取地址信息
	addressInfo, err := tmt.QueryAddressById(s.ctx, userId, req.AddressID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取用户地址信息失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	if tradeOrder.OrderStatus == enums.OrderStatusUnDelivered.Val() {
		// 调用供应链修改地址
		recvZip := "000000"
		if addressInfo.Code != "" {
			recvZip = addressInfo.Code
		}
		// 处理省市区
		areaParts := logic.SplitArea(addressInfo.Area)
		request := &wae.ReceiverUpdateRequest{
			CNo:          tradeOrder.SupplyChainOrderID,
			RecvName:     addressInfo.Name,
			RecvPhone:    addressInfo.MobilePhone,
			RecvZip:      recvZip,
			RecvProvince: areaParts[0],
			RecvCity:     areaParts[1],
			RecvDistrict: areaParts[2],
			RecvAddress:  areaParts[3] + addressInfo.Place,
			BuyerComment: tradeOrder.UserRemark,
		}
		update, err := wae.ReceiverUpdate(s.ctx, request)
		if err != nil || !update {
			log.Ctx(s.ctx).Errorf("修改订单收货信息失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
			return nil, global.CommonErr
		}
	}
	// 修改订单
	orderConsigneeAddress := &define.OrderConsigneeAddress{
		Area:  addressInfo.Area,
		Code:  addressInfo.Code,
		Place: addressInfo.Place,
	}
	orderConsigneeAddressJson := datatypes.JSON(utils.Obj2JsonStr(orderConsigneeAddress))
	updateTradeOrder := &model.TradeOrder{
		AddressID:        req.AddressID,
		ConsigneeName:    addressInfo.Name,
		ConsigneePhone:   addressInfo.MobilePhone,
		ConsigneeAddress: &orderConsigneeAddressJson,
	}
	updateWrapper := search.NewWrapper().Where(schema.ID.Eq(req.ID))
	err = repo.NewTradeOrderRepo(schema.WithContext(s.ctx)).Update(updateTradeOrder, updateWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("修改订单失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	return &define.UpdateTradeOrderAddressResp{}, nil
}

// DelTradeOrder 删除订单
func (s *Service) DelTradeOrder(req *define.DelTradeOrderReq) (*define.DelTradeOrderResp, error) {
	log.Ctx(s.ctx).Infof("删除订单, req:%+v", utils.Obj2JsonStr(req))
	userId := s.GetUserId()
	err := logic.DelTradeOrder(s.ctx, req.ID, &userId)
	if err != nil {
		log.Ctx(s.ctx).Errorf("删除订单失败, req:%+v, err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	return &define.DelTradeOrderResp{}, nil
}

// ConfirmTradeOrder 确定收货
func (s *Service) ConfirmTradeOrder(req *define.ConfirmTradeOrderReq) (*define.ConfirmTradeOrderResp, error) {
	err := logic.ConfirmTradeOrder(s.ctx, req.ID, s.GetUserId())
	if err != nil {
		return nil, global.CommonErr
	}
	// 调用供应链确认收货
	spanContext := s.NewContextWithSpanContext(s.ctx)
	go func() {
		schema := repo.GetQuery().TradeOrder
		wrapper := search.NewWrapper().Where(schema.ID.Eq(req.ID))
		tradeOrder, err := repo.NewTradeOrderRepo(schema.WithContext(spanContext)).SelectOne(wrapper)
		if err != nil {
			log.Ctx(spanContext).Error("查询订单失败", err)
			return
		}
		request := &wae.OrderCompleteRequest{
			CNo: tradeOrder.SupplyChainOrderID,
		}
		complete, err := wae.OrderComplete(spanContext, request)
		if err != nil && complete {
			log.Ctx(spanContext).Error("订单完成失败", err)
		}
	}()
	return &define.ConfirmTradeOrderResp{}, nil
}

// TradeOrderTimeoutClose 关闭超时订单
func (s *Service) TradeOrderTimeoutClose(req *define.TradeOrderTimeoutCloseReq) (any, error) {
	spanContext := s.NewContextWithSpanContext(s.ctx)
	to := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().
		Where(to.OrderStatus.Eq(enums.OrderStatusUnPaid.Val())).
		Where(to.CreatedAt.Lte(time.Now().Add(-time.Minute * 10)))
	tradeOrders, err := repo.NewTradeOrderRepo(to.WithContext(spanContext).Limit(200)).SelectList(wrapper)
	if err != nil {
		log.Ctx(spanContext).Error("查询超时订单失败", err)
		return nil, errors.Wrap(err, "查询超时订单失败")
	}
	if len(tradeOrders) == 0 {
		log.Ctx(spanContext).Info("无超时订单")
		return response.Empty{}, nil
	}
	const batchSize = 20
	for i := 0; i < len(tradeOrders); i += batchSize {
		end := i + batchSize
		if end > len(tradeOrders) {
			end = len(tradeOrders)
		}
		batch := tradeOrders[i:end]
		go func(ctx context.Context, orders []*model.TradeOrder) {
			for _, order := range orders {
				if err := logic.ProcessTimeoutOrder(ctx, order); err != nil {
					log.Ctx(ctx).Errorf("处理订单 %d 超时时失败, err: %+v", order.ID, err)
				}
			}
		}(spanContext, batch)
	}
	return response.Empty{}, nil
}

// TradeOrderFailedRetryUploadToSupply 定时补偿10分钟前已支付未发送到供应链订单
func (s *Service) TradeOrderFailedRetryUploadToSupply(req *define.TradeOrderFailedRetryUploadToSupplyReq) (any, error) {
	spanContext := s.NewContextWithSpanContext(s.ctx)
	to := repo.Query(spanContext).TradeOrder
	const batchSize = 50 // 控制每次处理数量，防止内存压力过大

	// 构建查询条件
	wrapper := search.NewWrapper().
		Preload(to.TradeOrderItem).
		Where(to.OrderStatus.Eq(enums.OrderStatusUnDelivered.Val()),
			to.CreatedAt.Gte(time.Now().Add(-12*time.Hour)),
			to.SupplyChainOrderID.IsNull(),
		)
	tradeOrders, err := repo.NewTradeOrderRepo(to.WithContext(spanContext).Limit(200)).SelectList(wrapper)
	if err != nil || len(tradeOrders) == 0 {
		log.Ctx(spanContext).Warn("未找到待补偿订单")
		return response.Empty{}, nil
	}
	// 失败列表
	var failIds []int64
	for i := 0; i < len(tradeOrders); i += batchSize {
		endIndex := i + batchSize
		if endIndex > len(tradeOrders) {
			endIndex = len(tradeOrders)
		}
		batch := tradeOrders[i:endIndex]

		for _, tradeOrder := range batch {
			if err := logic.ProcessUploadOrderToSupply(spanContext, tradeOrder); err != nil {
				failIds = append(failIds, tradeOrder.ID)
				continue
			}
		}
	}
	if len(failIds) > 0 {
		// 打印失败名单
		log.Ctx(spanContext).Errorf("订单补偿失败名单: %v", failIds)
		return nil, errors.New("订单补偿失败")
	}
	return response.Empty{}, nil
}

// SupplyChainNotice 供应链消息通知
func (s *Service) SupplyChainNotice(req *define.SupplyChainNoticeReq) (*define.SupplyChainNoticeResp, error) {
	log.Ctx(s.ctx).Infof("供应链消息通知, req:%+v", utils.Obj2JsonStr(req))
	resp := &define.SupplyChainNoticeResp{
		Code: 1000,
		Desc: "SUCCESS",
	}
	var failMidList []int64
	switch req.Type {
	case enums.OrderDeliver.Val():
		params := make([]*define.UpdateTradeOrderDeliver, len(req.Data))
		err := utils.MapToStructArray(req.Data, &params)
		if err != nil {
			log.Ctx(s.ctx).Errorf("转换参数失败, err:%+v", err)
			return nil, err
		}
		failMidList = logic.UpdateTradeOrderDeliver(s.ctx, params)
		break
	case enums.UpdateFreight.Val():
		params := make([]*define.UpdateTradeOrderUpdateFreight, len(req.Data))
		err := utils.MapToStructArray(req.Data, &params)
		if err != nil {
			log.Ctx(s.ctx).Errorf("转换参数失败, err:%+v", err)
			return nil, err
		}
		failMidList = logic.UpdateTradeOrderUpdateFreight(s.ctx, params)
		break
	case enums.FreightInfo.Val():
		params := make([]*define.UpdateTradeOrderFreightInfo, len(req.Data))
		err := utils.MapToStructArray(req.Data, &params)
		if err != nil {
			log.Ctx(s.ctx).Errorf("转换参数失败, err:%+v", err)
			return nil, err
		}
		failMidList = logic.UpdateTradeOrderFreightInfo(s.ctx, params)
		break
	default:
		log.Ctx(s.ctx).Infof("无需处理的消息类型, req:%+v", utils.Obj2JsonStr(req))
	}
	if len(failMidList) != 0 {
		resp.Desc = "FAIL"
	} else {
		failMidList = []int64{}
	}
	resp.Data.Failed = failMidList
	return resp, nil
}

// TradeOrderFinish 自动收货
func (s *Service) TradeOrderFinish(req *define.TradeOrderFinishReq) (*define.TradeOrderFinishResp, error) {
	spanContext := s.NewContextWithSpanContext(s.ctx)
	err := logic.FinishTradeOrderTradeOrder(spanContext)
	if err != nil {
		log.Ctx(spanContext).Error("自动收货失败", err)
		return nil, err
	}
	return &define.TradeOrderFinishResp{}, nil
}

// GetWebTradeOrderFreightInfo 获取物流信息
func (s *Service) GetWebTradeOrderFreightInfo(req *define.GetWebTradeOrderFreightInfoReq) (*define.GetWebTradeOrderFreightInfoResp, error) {
	err := logic.CheckUserOrder(s.ctx, req.ID, s.GetUserId())
	if err != nil {
		return nil, err
	}
	freightSchema := repo.GetQuery().TradeOrderFreight
	wrapper := search.NewWrapper().Where(freightSchema.OrderID.Eq(req.ID)).Where(freightSchema.DeliveryNumber.Eq(req.DeliveryNumber))
	tradeOrderFreight, err := repo.NewTradeOrderFreightRepo(freightSchema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, errors.Wrap(err, "查询物流信息失败")
	}
	resp := &define.GetWebTradeOrderFreightInfoResp{
		DeliveryNumber:  tradeOrderFreight.DeliveryNumber,
		DeliveryCompany: tradeOrderFreight.DeliveryCompany,
		Status:          tradeOrderFreight.Status,
		Checked:         tradeOrderFreight.Checked,
		Records:         tradeOrderFreight.Records,
	}
	return resp, nil
}

// SupplyChainNoticeCCheckSign 供应链消息通知验签
func (s *Service) SupplyChainNoticeCCheckSign(req *define.SupplyChainNoticeOriginalReq) (*define.SupplyChainNoticeReq, error) {
	log.Ctx(s.ctx).Infof("供应链消息通知验签, req:%+v", utils.Obj2JsonStr(req))
	timestamp := req.Timestamp
	requestTime := time.Unix(timestamp/1000, 0)
	serverTime := time.Now()
	timeDiff := serverTime.Sub(requestTime).Abs()

	if timeDiff > 5*time.Minute {
		return nil, errors.New("时间戳无效")
	}
	params := map[string]interface{}{
		"app_key":     req.AppKey,
		"format":      req.Format,
		"timestamp":   req.Timestamp,
		"version":     req.Version,
		"biz_content": req.BizContent,
	}
	waeConfig := global.GlobalConfig.Wae
	if waeConfig == nil {
		return nil, errors.New("未配置供应链配置")
	}
	sign, err := utils.GenerateSign(params, waeConfig.AppSecret)
	if err != nil {
		return nil, errors.Wrap(err, "生成签名失败")
	}
	if sign != req.Sign {
		return nil, errors.New("签名验证失败")
	}
	noticeReq := &define.SupplyChainNoticeReq{}
	_ = utils.JsonStrToStruct(req.BizContent, noticeReq)
	return noticeReq, nil
}

// CancelAdminTradeOrder 管理员取消订单
func (s *Service) CancelAdminTradeOrder(req *define.CancelAdminTradeOrderReq) (*define.CancelAdminTradeOrderResp, error) {
	err := logic.CancelTradeOrder(s.ctx, req.ID, enums.CancelTypeWC.Val(), nil)
	if err != nil {
		log.Ctx(s.ctx).Errorf("取消订单失败, err:%+v", err)
		return nil, global.CommonErr
	}
	to := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Where(to.ID.Eq(req.ID))
	tradeOrder, err := repo.NewTradeOrderRepo(to.WithContext(s.ctx).Preload(to.TradeOrderItem)).SelectOne(wrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("取消订单,查询订单失败, err:%+v", err)
		return nil, global.CommonErr
	}
	// 获取订单项
	err = repo.ExecGenTx(s.ctx, func(tx context.Context) error {
		if err := logic.RollbackMallItemStock(tx, tradeOrder); err != nil {
			return errors.Wrap(err, "回滚库存失败")
		}
		return nil
	})
	if err != nil {
		log.Ctx(s.ctx).Error("回滚库存失败", err)
		return nil, global.CommonErr
	}
	return &define.CancelAdminTradeOrderResp{}, nil
}

// SyncAdminTradeOrderFreight 同步订单物流信息
func (s *Service) SyncAdminTradeOrderFreight(req *define.SyncAdminTradeOrderFreightReq) (*define.SyncAdminTradeOrderFreightResp, error) {
	err := logic.SyncAdminTradeOrderFreight(s.ctx, req)
	if err != nil {
		log.Ctx(s.ctx).Error("同步订单物流信息失败", err)
		return nil, response.ParamErr.SetMsg(err.Error())
	}
	return &define.SyncAdminTradeOrderFreightResp{}, nil
}

func (s *Service) GetOrderPrivateInfo(req *define.AdminGetOrderPrivateInfoReq) (*define.AdminGetOrderPrivateInfoResp, error) {
	to := repo.GetQuery().TradeOrder
	wrapper := search.NewWrapper().Select(to.ConsigneePhone).Where(to.ID.Eq(req.ID))
	tradeOrder, err := repo.NewTradeOrderRepo(to.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, errors.Wrap(err, "查询订单失败")
	}
	return &define.AdminGetOrderPrivateInfoResp{
		ConsigneePhone: tradeOrder.ConsigneePhone,
	}, nil
}
