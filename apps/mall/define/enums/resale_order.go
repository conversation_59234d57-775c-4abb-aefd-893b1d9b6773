package enums

// ResaleOrderStatus 转卖订单状态
type ResaleOrderStatus int32

func (r ResaleOrderStatus) Val() int32 {
	return int32(r)
}

const (
	// ResaleOrderStatusUnPaid 未支付
	ResaleOrderStatusUnPaid ResaleOrderStatus = 0
	// ResaleOrderStatusItemLocked 物品锁定成功
	ResaleOrderStatusItemLocked ResaleOrderStatus = 1
	// ResaleOrderStatusPaid 已支付
	ResaleOrderStatusPaid ResaleOrderStatus = 10
	// ResaleOrderStatusTransfer 转移中
	ResaleOrderStatusTransfer ResaleOrderStatus = 20
	// ResaleOrderStatusCompleted 已完成
	ResaleOrderStatusCompleted ResaleOrderStatus = 30
	// ResaleOrderStatusCanceled 已取消
	ResaleOrderStatusCanceled ResaleOrderStatus = 40
)

var ResaleOrderStatusMap = map[int32]string{
	ResaleOrderStatusUnPaid.Val():     "未支付",
	ResaleOrderStatusItemLocked.Val(): "物品锁定成功",
	ResaleOrderStatusPaid.Val():       "已支付",
	ResaleOrderStatusTransfer.Val():   "转移中",
	ResaleOrderStatusCompleted.Val():  "已完成",
	ResaleOrderStatusCanceled.Val():   "已取消",
}

var ResaleOrderStatusShowList = []int32{
	ResaleOrderStatusItemLocked.Val(),
	ResaleOrderStatusPaid.Val(),
	ResaleOrderStatusTransfer.Val(),
	ResaleOrderStatusCompleted.Val(),
	ResaleOrderStatusCanceled.Val(),
}
