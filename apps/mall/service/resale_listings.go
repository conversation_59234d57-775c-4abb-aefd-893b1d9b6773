package service

import (
	"context"
	"fmt"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/locker"
	"marketplace_service/apps/mall/service/logic"
	"marketplace_service/apps/mall/service/logic/config"
	"marketplace_service/global"
	"marketplace_service/pkg/middleware"
	"marketplace_service/pkg/pagination"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/pkg/utils/excelize_lib"
	"marketplace_service/pkg/utils/snowflakeutl"
	"marketplace_service/third_party/tmt"
	"marketplace_service/third_party/wat"
	"marketplace_service/third_party/yc_open"
	ycdefine "marketplace_service/third_party/yc_open/define"
	"strings"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

// GetAdminResaleListingsList 管理端获取转卖挂单分页列表
func (s *Service) GetAdminResaleListingsList(req *define.GetAdminResaleListingsListReq) (*define.GetAdminResaleListingsListResp, error) {
	rlSchema := repo.GetQuery().ResaleListings
	resaleListingsList, count, err := repo.NewResaleListingsRepo(rlSchema.WithContext(s.ctx).Order(rlSchema.CreatedAt.Desc(), rlSchema.ID.Desc())).
		QuickSelectPage(req)
	if err != nil {
		return nil, errors.Wrap(err, "查询转卖挂单分页列表失败")
	}
	if len(resaleListingsList) == 0 {
		return &define.GetAdminResaleListingsListResp{
			List:  []*define.GetAdminResaleListingsListData{},
			Total: count,
		}, nil
	}
	list := make([]*define.GetAdminResaleListingsListData, 0)
	var userIDs []string
	for _, resaleListings := range resaleListingsList {
		userIDs = append(userIDs, resaleListings.SellerID)
	}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		return nil, err
	}
	for _, resaleListings := range resaleListingsList {
		data := &define.GetAdminResaleListingsListData{
			ID:              resaleListings.ID,
			SkuId:           resaleListings.SkuID,
			ItemId:          resaleListings.ItemID,
			ItemName:        resaleListings.ItemName,
			ItemIconURL:     resaleListings.ItemIconURL,
			ItemSpecs:       resaleListings.ItemSpecs,
			SalePrice:       resaleListings.SalePrice,
			SoldQuantity:    resaleListings.SoldQuantity,
			ListingQuantity: resaleListings.ListingQuantity,
			SellerId:        resaleListings.SellerID,
			SellerPhone:     resaleListings.SellerPhone,
			Status:          resaleListings.Status,
			Terminal:        resaleListings.Terminal,
			TotalFee:        resaleListings.TotalFee,
			TotalIncome:     resaleListings.TotalIncome,
			CreatedAt:       resaleListings.CreatedAt,
		}
		if userDetail, ok := userDetailMap[resaleListings.SellerID]; ok {
			data.SellerNickname = userDetail.Nickname
		}
		list = append(list, data)
	}
	return &define.GetAdminResaleListingsListResp{
		List:  list,
		Total: count,
	}, nil
}

// GetAdminResaleListingsDetail 获取转卖挂单详情
func (s *Service) GetAdminResaleListingsDetail(req *define.GetAdminResaleListingsDetailReq) (*define.GetAdminResaleListingsDetailResp, error) {
	rlSchema := repo.GetQuery().ResaleListings
	resaleListings, err := repo.NewResaleListingsRepo(rlSchema.WithContext(s.ctx)).
		SelectOne(search.NewWrapper().Where(rlSchema.ID.Eq(req.ID)))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "查询挂单详情失败")
	}
	if resaleListings == nil {
		return nil, errors.New("挂单不存在")
	}
	userIDs := []string{resaleListings.SellerID}
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, userIDs...)
	if err != nil {
		return nil, err
	}
	resp := &define.GetAdminResaleListingsDetailResp{
		ID:         resaleListings.ID,
		SellerId:   resaleListings.SellerID,
		Status:     resaleListings.Status,
		Terminal:   resaleListings.Terminal,
		AppVersion: resaleListings.AppVersion,
		CreatedAt:  resaleListings.CreatedAt,
		Operator:   resaleListings.Operator,
	}
	if userDetail, ok := userDetailMap[resaleListings.SellerID]; ok {
		resp.SellerNickname = userDetail.Nickname
	}
	resp.List = []*define.GetAdminResaleListingsDetailData{
		{
			SkuId:           resaleListings.SkuID,
			ItemId:          resaleListings.ItemID,
			ItemName:        resaleListings.ItemName,
			ItemIconURL:     resaleListings.ItemIconURL,
			ItemSpecs:       resaleListings.ItemSpecs,
			SalePrice:       resaleListings.SalePrice,
			TotalIncome:     resaleListings.TotalIncome,
			TotalFee:        resaleListings.TotalFee,
			SoldQuantity:    resaleListings.SoldQuantity,
			ListingQuantity: resaleListings.ListingQuantity,
		},
	}
	return resp, nil
}

// ExportResaleListingsList 导出转卖订单列表
func (s *Service) ExportResaleListingsList(req *define.GetAdminResaleListingsListReq) error {
	dataList := make([]*define.GetAdminResaleListingsListData, 0)
	for i := 1; i < 11000; i++ {
		req.PageSize = 100
		req.Page = i
		page, err := s.GetAdminResaleListingsList(req)
		if err != nil {
			return err
		}
		if len(page.List) == 0 {
			break
		}
		dataList = append(dataList, page.List...)
	}
	excel := excelize_lib.NewExcel()
	dataKey := make([]map[string]string, 0)
	dataKey = append(dataKey, map[string]string{"key": "created_at", "title": "上架时间", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "id", "title": "出售单号", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_id", "title": "商品ID", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "sku_id", "title": "SKU ID", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_name", "title": "商品名称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_specs", "title": "规格", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "sale_price", "title": "出售单价", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "listing_quantity", "title": "挂单数量", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "sold_quantity", "title": "成交数量", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "seller_id", "title": "卖家用户ID", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "seller_nickname", "title": "用户昵称", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "seller_phone", "title": "注册手机号", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "status", "title": "挂单状态", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "terminal", "title": "终端", "width": "30", "is_num": "0"})
	data := make([]map[string]interface{}, 0)
	for _, idata := range dataList {
		data = append(data, map[string]interface{}{
			"created_at":       utils.GetDateTimeFormatStr(idata.CreatedAt),
			"id":               utils.StrVal(idata.ID),
			"item_id":          idata.ItemId,
			"sku_id":           idata.SkuId,
			"item_name":        idata.ItemName,
			"item_specs":       idata.ItemSpecs,
			"sale_price":       "¥" + utils.FenToYuanString(idata.SalePrice),
			"listing_quantity": idata.ListingQuantity,
			"sold_quantity":    idata.SoldQuantity,
			"total_fee":        "¥" + utils.FenToYuanString(idata.TotalFee),
			"seller_id":        idata.SellerId,
			"seller_nickname":  idata.SellerNickname,
			"seller_phone":     utils.PhoneMix(idata.SellerPhone),
			"status":           enums.ResaleListingsStatusMap[idata.Status],
			"terminal":         enums.TerminalMap[idata.Terminal],
		})
	}

	err := excel.ExportToStream(dataKey, data, s.ctx.(*gin.Context))
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.ExportResaleListingsList] ExportToWeb err:%v", err)
		return response.SystemErr
	}
	return nil
}

// UpdateResaleListingsStatus 更新转卖挂单状态（下架）
func (s *Service) UpdateResaleListingsStatus(req *define.UpdateResaleListingsStatusReq) (*define.UpdateResaleListingsStatusResp, error) {
	if req.Status == nil || *req.Status != enums.ResaleListingsStatusDown.Val() {
		return nil, define.MS200023Err.SetMsg("当前挂单不可更改为此状态！")
	}
	adminID := s.GetAdminId()
	err := logic.TakeDownResaleListings(s.ctx, req.ID, adminID)
	if err != nil {
		return nil, err
	}

	return &define.UpdateResaleListingsStatusResp{
		ID:        req.ID,
		Status:    *req.Status,
		UpdatedAt: utils.GetDateFormatStr(utils.Now()),
	}, nil
}

// GetWebPublishedResaleListingsList 查询我发布的转卖挂单订单列表
func (s *Service) GetWebPublishedResaleListingsList(req *define.GetWebPublishedResaleListingsListReq) (*define.GetWebPublishedResaleListingsListResp, error) {
	userID := s.GetUserId()
	rliDB := repo.GetQuery().ResaleListingsItem.UnderlyingDB()
	baseQuery := rliDB.Select("resale_listings_id", "COUNT(*) AS on_sale_qty").
		Where("status = ?", enums.ResaleListingsItemStatusOnSale.Val()).
		Where("seller_id = ?", userID).
		Group("resale_listings_id")
	// 查询分页总数
	var total int64
	err := baseQuery.Count(&total).Error
	if err != nil {
		return nil, err
	}
	if total == 0 {
		return &define.GetWebPublishedResaleListingsListResp{
			List: []*define.GetWebPublishedResaleListingsListData{},
		}, nil
	}
	// 查询当前分页数据
	type countRecord struct {
		ResaleListingsID int64 `gorm:"column:resale_listings_id"`
		OnSaleQty        int32 `gorm:"column:on_sale_qty"`
	}
	var records []*countRecord
	err = baseQuery.
		Order("created_at DESC").
		Limit(req.GetPageSize()).
		Offset(req.GetOffset()).
		Find(&records).Error
	if err != nil {
		return nil, err
	}

	rlIDs := make([]int64, 0, len(records))
	qtyMap := make(map[int64]int32, len(records))
	for _, record := range records {
		rlIDs = append(rlIDs, record.ResaleListingsID)
		qtyMap[record.ResaleListingsID] = record.OnSaleQty
	}
	rlSchema := repo.GetQuery().ResaleListings
	rlWrapper := search.NewWrapper().Where(rlSchema.ID.In(rlIDs...)).OrderBy(rlSchema.CreatedAt.Desc())
	resaleListingsList, err := repo.NewResaleListingsRepo(rlSchema.WithContext(s.ctx)).SelectList(rlWrapper)
	if err != nil {
		return nil, err
	}

	itemIDs := make([]string, 0)
	for _, item := range resaleListingsList {
		if !utils.In(item.ItemID, itemIDs) {
			itemIDs = append(itemIDs, item.ItemID)
		}
	}
	// 查询最低在售，不限卖家
	rlDB := repo.GetQuery().ResaleListings.UnderlyingDB()
	type minPriceRecord struct {
		ItemID   string `gorm:"column:item_id"`
		MinPrice int64  `gorm:"column:min_price"`
	}
	var minPriceResults []*minPriceRecord
	err = rlDB.Where(rlSchema.ItemID.In(itemIDs...)).
		Select("item_id, MIN(sale_price) as min_price").
		Where(rlSchema.Status.Eq(enums.ResaleListingsStatusOnSale.Val())).
		Group("item_id").
		Find(&minPriceResults).
		Error
	if err != nil {
		return nil, err
	}
	minPriceMap := make(map[string]int64)
	for _, item := range minPriceResults {
		minPriceMap[item.ItemID] = item.MinPrice
	}

	list := make([]*define.GetWebPublishedResaleListingsListData, 0, len(resaleListingsList))
	for _, resaleListings := range resaleListingsList {
		data := &define.GetWebPublishedResaleListingsListData{
			ID:            resaleListings.ID,
			ItemId:        resaleListings.ItemID,
			ItemName:      resaleListings.ItemName,
			ItemIconUrl:   resaleListings.ItemIconURL,
			ItemSpecs:     resaleListings.ItemSpecs,
			SellPrice:     resaleListings.SalePrice,
			StockQuantity: qtyMap[resaleListings.ID],
			MinPrice:      minPriceMap[resaleListings.ItemID],
		}
		list = append(list, data)
	}

	return &define.GetWebPublishedResaleListingsListResp{
		List:  list,
		Total: total,
	}, nil
}

// TakeDownResaleListingsFromWeb 用户端下架转卖挂单
func (s *Service) TakeDownResaleListingsFromWeb(req *define.TakeDownResaleListingsFromWebReq) (*define.TakeDownResaleListingsFromWebResp, error) {
	userID := s.GetUserId()
	if userID == "" {
		return nil, errors.New("请先登录账号")
	}
	rlSchema := repo.GetQuery().ResaleListings
	wrapper := search.NewWrapper().
		Where(rlSchema.ID.Eq(req.ID)).
		Where(rlSchema.SellerID.Eq(userID)).
		Where(rlSchema.Status.Eq(enums.ResaleListingsStatusOnSale.Val()))
	// 只能下架自己的挂单，而且是出售中的
	resaleListings, err := repo.NewResaleListingsRepo(rlSchema.WithContext(s.ctx)).SelectOne(wrapper)
	if err != nil {
		return nil, err
	}
	err = logic.TakeDownResaleListings(s.ctx, resaleListings.ID, "")
	if err != nil {
		return nil, err
	}
	return &define.TakeDownResaleListingsFromWebResp{
		ID:       req.ID,
		DownTime: utils.Now(),
	}, nil
}

// GetWebResaleListingsItemOnSaleList 查询某个挂单商品在售列表
func (s *Service) GetWebResaleListingsItemOnSaleList(req *define.GetWebResaleListingsItemOnSaleListReq) (*define.GetWebResaleListingsItemOnSaleListResp, error) {
	// 查询转卖商品
	riSchema := repo.GetQuery().ResaleItem
	itemWrapper := search.NewWrapper().
		Where(riSchema.ItemID.Eq(req.ItemID))
	resaleItem, err := repo.NewResaleItemRepo(riSchema.WithContext(s.ctx)).SelectOne(itemWrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	// 全局配置
	resaleConfig, err := logic.GetResaleStandardConfig(s.ctx)
	if err != nil {
		return nil, err
	}
	if resaleItem == nil || (resaleConfig.ResaleStatus != enums.ConfSwitchOn.Val() || resaleItem.ResaleStatus != enums.ResaleItemResaleStatusOpen.Val()) {
		// 转卖商品不存在或者未开启转卖
		resp := &define.GetWebResaleListingsItemOnSaleListResp{
			List: make([]*define.GetWebResaleListingsItemOnSaleListData, 0),
		}
		return resp, nil
	}

	db := repo.GetQuery().ResaleListingsItem.UnderlyingDB()
	baseQuery := db.Select("seller_id", "sale_price", "COUNT(*) AS on_sale_qty").
		Where("status = ?", enums.ResaleListingsItemStatusOnSale.Val()).
		Where("item_id = ?", req.ItemID).
		Group("seller_id, sale_price")
	// 查询分页总数
	var total int64
	err = baseQuery.Count(&total).Error
	if err != nil {
		return nil, err
	}
	if total == 0 {
		return &define.GetWebResaleListingsItemOnSaleListResp{
			List: []*define.GetWebResaleListingsItemOnSaleListData{},
		}, nil
	}
	// 查询分页列表数据
	orderBy := "sale_price"
	switch req.OrderBy {
	case "created_at":
		orderBy = "created_at"
	}
	sortOrder := "asc"
	if strings.ToLower(req.SortOrder) == "desc" {
		sortOrder = "desc"
	}
	type countRecord struct {
		SellerID  string `gorm:"column:seller_id" json:"seller_id"`
		SalePrice int64  `gorm:"column:sale_price" json:"sale_price"`
		OnSaleQty int64  `gorm:"column:on_sale_qty" json:"on_sale_qty"`
	}
	var results []*countRecord
	err = baseQuery.
		Order(fmt.Sprintf("%s %s", orderBy, sortOrder)).
		Limit(req.GetPageSize()).
		Offset(req.GetOffset()).
		Find(&results).Error
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取某个商品在售挂单列表失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, global.CommonErr
	}
	if len(results) == 0 {
		return &define.GetWebResaleListingsItemOnSaleListResp{
			List: []*define.GetWebResaleListingsItemOnSaleListData{},
		}, nil
	}

	// 所有卖家 id
	var sellerIDs []string
	for _, item := range results {
		sellerIDs = append(sellerIDs, item.SellerID)
	}
	// 卖家信息
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, sellerIDs...)
	if err != nil {
		return nil, err
	}
	// 在售商品总数
	rliSchema := repo.GetQuery().ResaleListingsItem
	countWrapper := search.NewWrapper().
		Where(rliSchema.ItemID.Eq(req.ItemID)).
		Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val()))
	totalCount, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(s.ctx)).Count(countWrapper)
	if err != nil {
		return nil, err
	}
	// 卖家已售数量
	totalQtyMap, err := logic.GetSellerSoldTotalQtyMap(s.ctx, sellerIDs)
	if err != nil {
		return nil, err
	}

	list := make([]*define.GetWebResaleListingsItemOnSaleListData, 0, len(results))
	for _, r := range results {
		data := &define.GetWebResaleListingsItemOnSaleListData{
			ID:          snowflakeutl.GenerateID(), // 给用户端当主键用，无业务意义
			ItemId:      resaleItem.ItemID,
			ItemName:    resaleItem.ItemName,
			ItemIconUrl: resaleItem.ItemIconURL,
			ItemSpecs:   resaleItem.ItemSpecs,
			SellPrice:   r.SalePrice,
			OnSaleQty:   r.OnSaleQty,
			SellerId:    r.SellerID,
		}
		// 卖家信息
		if sellerDetail, ok := userDetailMap[r.SellerID]; ok {
			data.SellerNickname = utils.MaskNickname(sellerDetail.Nickname)
			data.SellerAvatar = sellerDetail.Avatar
		}
		// 卖家已售数量
		if qty, ok := totalQtyMap[data.SellerId]; ok {
			data.SellerTotalSoldQty = qty
		}

		list = append(list, data)
	}

	// 异步更新物品信息
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	itemID := req.ItemID
	go func() {
		syncErr := logic.SyncResaleUserItemsByItem(spanCtx, itemID)
		if syncErr != nil {
			log.Ctx(spanCtx).Errorf("同步物品信息失败：%v", syncErr)
		}
	}()

	return &define.GetWebResaleListingsItemOnSaleListResp{
		List:           list,
		Total:          total,
		TotalOnSaleQty: totalCount,
	}, nil
}

// GetWebResaleListingsItemSettlementList 查询某个挂单商品购买结算列表
func (s *Service) GetWebResaleListingsItemSettlementList(req *define.GetWebResaleListingsItemSettlementListReq) (*define.GetWebResaleListingsItemSettlementListResp, error) {
	userID := s.GetUserId()
	if userID == "" {
		return nil, errors.New("请先登录账号")
	}
	if userID == req.SellerId {
		return nil, define.MS200030Err
	}
	itemID := req.ItemID
	rlItemList := make([]*model.ResaleListingsItem, 0)
	rliSchema := repo.GetQuery().ResaleListingsItem
	specificIDs := make([]int64, 0) // 指定的挂单物品 id 记录
	// 指定卖家和价格
	if req.SellerId != "" && req.SalePrice > 0 {
		// 指定卖家和价格
		rliWrapper := search.NewWrapper().
			Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())).
			Where(rliSchema.ItemID.Eq(itemID)).
			Where(rliSchema.SellerID.Neq(userID)).
			Where(rliSchema.SellerID.Eq(req.SellerId)).
			Where(rliSchema.SalePrice.Eq(req.SalePrice)).
			OrderBy(rliSchema.CreatedAt.Asc(), rliSchema.ID.Desc())
		rlItems, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(s.ctx).Limit(int(req.Quantity))).
			SelectList(rliWrapper)
		if err != nil {
			return nil, err
		}
		rlItemList = append(rlItemList, rlItems...)
		for _, rlItem := range rlItems {
			specificIDs = append(specificIDs, rlItem.ID)
		}
	}
	// 指定数据不够时，用补充其他可购买的数据
	if len(rlItemList) < int(req.Quantity) {
		// 数据不够，用其他数据补足
		rliWrapper := search.NewWrapper()
		if len(specificIDs) > 0 {
			// 排除已经指定的记录
			rliWrapper.Where(rliSchema.ID.NotIn(specificIDs...))
		}
		rliWrapper.Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())).
			Where(rliSchema.ItemID.Eq(req.ItemID)).
			Where(rliSchema.SellerID.Neq(userID)).
			OrderBy(rliSchema.SalePrice.Asc(), rliSchema.CreatedAt.Asc(), rliSchema.ID.Desc())

		limit := int(req.Quantity) - len(rlItemList)
		rlItems, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(s.ctx).Limit(limit)).
			SelectList(rliWrapper)
		if err != nil {
			return nil, err
		}
		rlItemList = append(rlItemList, rlItems...)
	}

	// 处理无他人挂单或者库存不足的情况
	if len(rlItemList) == 0 {
		// 查询自己是否有挂单
		rliWrapper := search.NewWrapper().Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())).
			Where(rliSchema.ItemID.Eq(req.ItemID)).
			Where(rliSchema.SellerID.Eq(userID))
		count, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(s.ctx)).Count(rliWrapper)
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, define.MS200032Err
		} else {
			return nil, define.MS200031Err
		}
	} else if len(rlItemList) < int(req.Quantity) {
		// 库存不足
		return nil, define.MS200004Err.SetMsg("可供你购买的数量不足")
	}

	// 所有卖家 id
	var sellerIDs []string
	for _, item := range rlItemList {
		sellerIDs = append(sellerIDs, item.SellerID)
	}
	// 卖家信息
	userDetailMap, err := logic.GetUserDetailMap(s.ctx, sellerIDs...)
	if err != nil {
		return nil, err
	}

	list := make([]*define.GetWebResaleListingsItemSettlementListInfo, 0)
	// 根据卖家和价格做聚合
	firstOccurrence := make(map[string]int) // 记录首次出现位置
	for _, rlItem := range rlItemList {
		mapKey := fmt.Sprintf("%s_%d", rlItem.SellerID, rlItem.SalePrice)
		if idx, ok := firstOccurrence[mapKey]; ok {
			list[idx].OnSaleQty += 1
			list[idx].SelectedQty += 1
			rlList := list[idx].SelectedResaleListingsList
			rlExists := false
			for _, selectedResaleListings := range rlList {
				if rlItem.ResaleListingsID == selectedResaleListings.ResaleListingsID {
					rlExists = true
					selectedResaleListings.ResaleListingsItemIDs = append(selectedResaleListings.ResaleListingsItemIDs, utils.StrVal(rlItem.ID))
				}
			}
			if !rlExists {
				// 添加一条新的记录
				selectedResaleListings := &define.GetWebResaleListingsItemSettlementListSelectedItem{
					ResaleListingsID:      rlItem.ResaleListingsID,
					ResaleListingsItemIDs: []string{utils.StrVal(rlItem.ID)},
				}
				list[idx].SelectedResaleListingsList = append(list[idx].SelectedResaleListingsList, selectedResaleListings)
			}
		} else {
			// 选中的挂单列表
			selectedResaleListings := &define.GetWebResaleListingsItemSettlementListSelectedItem{
				ResaleListingsID:      rlItem.ResaleListingsID,
				ResaleListingsItemIDs: []string{utils.StrVal(rlItem.ID)},
			}
			listInfo := &define.GetWebResaleListingsItemSettlementListInfo{
				SellerId:                   rlItem.SellerID,
				SalePrice:                  rlItem.SalePrice,
				OnSaleQty:                  1,
				SelectedQty:                1,
				SelectedResaleListingsList: []*define.GetWebResaleListingsItemSettlementListSelectedItem{selectedResaleListings},
			}

			// 卖家信息
			if sellerDetail, ok := userDetailMap[rlItem.SellerID]; ok {
				listInfo.SellerNickname = utils.MaskNickname(sellerDetail.Nickname)
				listInfo.SellerAvatar = sellerDetail.Avatar
			}
			list = append(list, listInfo)
			// 记录首次出现的位置
			firstOccurrence[mapKey] = len(list) - 1
		}
	}

	// 处理最后一条数据
	if len(list) > 0 {
		lastItem := list[len(list)-1]
		// 查询在售数量，最后一条的在售数量可能大于选中数量
		wp := search.NewWrapper().
			Where(rliSchema.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())).
			Where(rliSchema.ItemID.Eq(req.ItemID)).
			Where(rliSchema.SellerID.Eq(lastItem.SellerId)).
			Where(rliSchema.SalePrice.Eq(lastItem.SalePrice))

		onSaleQty, err := repo.NewResaleListingsItemRepo(rliSchema.WithContext(s.ctx)).Count(wp)
		if err != nil {
			return nil, err
		}
		lastItem.OnSaleQty = onSaleQty
	}

	var totalAmount int64
	for _, item := range list {
		totalAmount += item.SelectedQty * item.SalePrice
	}

	buyMaxQty, _ := config.GetInt(s.ctx, constant.ResaleBuyMaxQty, 1000)
	return &define.GetWebResaleListingsItemSettlementListResp{
		List:        list,
		TotalAmount: totalAmount,
		BuyMaxQty:   int64(buyMaxQty),
	}, nil
}

// AddResaleListings 挂单出售
func (s *Service) AddResaleListings(req *define.AddResaleListingsReq) (*define.AddResaleListingsResp, error) {
	logPrefix := "AddResaleListings"
	// 获取用户信息
	userInfo, err := s.GetUserInfo()
	if err != nil {
		return nil, err
	}
	userID := userInfo.Id

	// 加锁
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewResaleListingsLock(userID, locker.ResaleListingsAdd)))
	if !l.Lock(s.ctx) {
		return nil, response.TooManyRequestErr
	}
	defer l.UnLock(s.ctx)

	// 获取转卖商品
	itemID := req.ItemID
	riSchema := repo.GetQuery().ResaleItem
	riWrapper := search.NewWrapper().
		Where(riSchema.ItemID.Eq(itemID))
	resaleItem, err := repo.NewResaleItemRepo(riSchema.WithContext(s.ctx)).SelectOne(riWrapper)
	if err != nil {
		return nil, err
	}
	// 出售密码校验
	chkSellPwdParams := &wat.CheckSellPwdInfoForm{
		UserId: userID,
	}
	sellPwdInfo, err := wat.CheckSellPwdInfo(s.ctx, chkSellPwdParams)
	if err != nil {
		return nil, err
	}
	if sellPwdInfo.NeedToInputPwd {
		if req.SellPassword == "" {
			return nil, define.MS200018Err
		}
		err := wat.VerifySellPwd(s.ctx, userID, req.SellPassword)
		if err != nil {
			return nil, define.MS200019Err
		}
	}

	// 转卖配置
	resaleConfig, err := logic.GetResaleItemTradeConfig(s.ctx, resaleItem)
	if err != nil {
		return nil, err
	}

	// 从云仓获取要出售的物品
	openUserInfo, err := tmt.QueryUserOpenById(s.ctx, userID)
	if err != nil {
		return nil, err
	}
	if openUserInfo == nil || openUserInfo.OpenInfo == nil {
		return nil, errors.New("请先绑定云仓")
	}
	openUserID := openUserInfo.OpenInfo.OpenUserId
	ycReq := &yc_open.QueryResaleUserItemListByUserItemLevelReq{
		Pagination: pagination.Pagination{
			Page:     1,
			PageSize: int(req.Quantity),
		},
		OpenUserID: openUserID,
		ItemID:     req.ItemID,
		StatusList: []any{ycdefine.UserItemStatusOwned.Val()}, // 只查询持有中的
	}
	if req.UserItemID != "" {
		// 指定单个物品出售
		ycReq.UserItemIDs = []string{req.UserItemID}
	}
	if resaleConfig.IntervalMinutes > 0 {
		ycReq.CreatedAtLt = utils.Now().Add(-time.Duration(resaleConfig.IntervalMinutes) * time.Minute).UTC().Format(time.RFC3339)
	}
	ycUserItemResult, err := yc_open.QueryResaleUserItemListByUserItemLevel(s.ctx, ycReq)
	if err != nil {
		return nil, err
	}
	validYcUserItemList := ycUserItemResult.List

	if len(validYcUserItemList) != int(req.Quantity) {
		// 持仓不足
		msg := fmt.Sprintf("出售数量 %d 不能大于可出售数量 %d", req.Quantity, len(validYcUserItemList))
		return nil, define.MS200004Err.SetMsg(msg)
	}

	// 业务校验
	err = logic.VerifyAddResaleListings(s.ctx, req, resaleItem, validYcUserItemList, resaleConfig)
	if err != nil {
		return nil, err
	}

	var userItemIDs []string
	for _, item := range validYcUserItemList {
		userItemIDs = append(userItemIDs, item.ID)
	}
	terminal := s.ctx.Value(middleware.ClientType).(string)   // 终端
	appVersion := s.ctx.Value(middleware.AppVersion).(string) // 版本号

	// 事务执行
	nowTime := utils.Now()
	rlID := snowflakeutl.GenerateID() // 挂单主表 id
	err = repo.ExecGenTx(s.ctx, func(ctx context.Context) error {
		// 先创建挂单主单
		rlSchema := repo.Query(ctx).ResaleListings
		totalAmount := req.SalePrice * int64(req.Quantity)
		rlModel := &model.ResaleListings{
			ID:              rlID,
			Status:          enums.ResaleListingsStatusOnSale.Val(),
			SellerID:        userID,
			SellerPhone:     userInfo.MobilePhone,
			ItemID:          itemID,
			SkuID:           resaleItem.SkuID,
			ItemName:        resaleItem.ItemName,
			ItemSpecs:       resaleItem.ItemSpecs,
			ItemIconURL:     resaleItem.ItemIconURL,
			TotalAmount:     totalAmount,
			SalePrice:       req.SalePrice,
			ListingQuantity: req.Quantity,
			SoldQuantity:    0,
			TotalIncome:     0,
			TotalFee:        0,
			Terminal:        terminal,
			AppVersion:      appVersion,
		}
		err = repo.NewResaleListingsRepo(rlSchema.WithContext(ctx)).Save(rlModel)
		if err != nil {
			return err
		}
		// 挂单子表
		var itemsToSave []*model.ResaleListingsItem
		for _, userItem := range validYcUserItemList {
			rlItem := &model.ResaleListingsItem{
				ID:               snowflakeutl.GenerateID(),
				SellerID:         userID,
				ResaleListingsID: rlID,
				Status:           enums.ResaleListingsItemStatusOnSale.Val(), // 出售中
				UserItemID:       userItem.ID,
				SalePrice:        req.SalePrice,
				ItemID:           itemID,
			}
			itemsToSave = append(itemsToSave, rlItem)
		}
		rliSchema := repo.Query(ctx).ResaleListingsItem
		err = repo.NewResaleListingsItemRepo(rliSchema.WithContext(ctx)).BatchSave(itemsToSave, 10)
		if err != nil {
			return err
		}

		// 更新云仓物品状态
		updateStatusReq := &yc_open.UpdateUserItemStatusReq{
			UserItemIDs:    userItemIDs,
			OriginalStatus: ycdefine.UserItemStatusOwned,
			UpdateStatus:   ycdefine.UserItemStatusForSale,
		}
		err = yc_open.UpdateUserItemStatus(s.ctx, updateStatusReq)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 更新数据统计
	err = logic.HandleResaleListingsOnSale(s.ctx, req.ItemID, req.Quantity)
	if err != nil {
		log.Ctx(s.ctx).Errorf("%s HandleResaleListingsOnSale error: %v", logPrefix, err)
	}

	return &define.AddResaleListingsResp{
		ID:        rlID,
		Quantity:  req.Quantity,
		CreatedAt: nowTime,
	}, nil
}
