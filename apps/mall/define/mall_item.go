package define

import (
	"marketplace_service/pkg/pagination"
	"time"
)

type (
	MallItemPageReq struct {
		pagination.Pagination
		ID           string   `form:"id,string" json:"id,string" search:"type:eq;column:id;table:mall_item"`                    // 直购商品ID
		ItemID       string   `form:"item_id" json:"item_id" search:"type:eq;column:item_id;table:mall_item"`                   // 商品ID
		ItemName     string   `form:"item_name" json:"item_name" search:"type:like;column:item_name;table:mall_item"`           // 商品名称
		SkuID        string   `form:"sku_id" json:"sku_id" search:"type:eq;column:sku_id;table:mall_item"`                      // sku_id
		Status       *int32   `form:"status" json:"status" search:"type:eq;column:status;table:mall_item"`                      // 状态
		SalePriceGte int64    `form:"sale_price_gte" json:"sale_price_gte" search:"type:gte;column:sale_price;table:mall_item"` // 出售价格
		SalePriceLte int64    `form:"sale_price_lte" json:"sale_price_lte" search:"type:lte;column:sale_price;table:mall_item"` // 出售价格
		CategoryIDs  []string `form:"category_ids" json:"category_ids" search:"type:in;column:category_id;table:mall_item"`     // 分类
		IPIDs        []string `form:"ip_ids" json:"ip_ids" search:"type:in;column:ip_id;table:mall_item"`                       // ip
		TrademarkIDs []string `form:"trademark_ids" json:"trademark_ids" search:"type:in;column:trademark_id;table:mall_item"`  // 品牌
		UpdatedBy    string   `form:"updated_by" json:"updated_by" search:"type:eq;column:updated_by;table:mall_item"`          // 操作人
	}

	MallItemPageResp struct {
		Total int64               `json:"total"` // 总数
		List  []*MallItemPageData `json:"list"`  // 列表
	}

	MallItemPageData struct {
		ID             int64     `json:"id,string"`       // 主键
		Status         int32     `json:"status"`          // 状态（0=待上架，1=已上架，2=已下架）
		ItemID         string    `json:"item_id"`         // 商品 ID
		SkuID          string    `json:"sku_id"`          // SKU ID
		ItemName       string    `json:"item_name"`       // 商品名称
		SpuID          string    `json:"spu_id"`          // SPU ID
		IPID           string    `json:"ip_id"`           // IP ID
		IPName         string    `json:"ip_name"`         // IP名称
		ItemIconURL    string    `json:"item_icon_url"`   // 商品主图
		ItemSpecs      string    `json:"item_specs"`      // 规格
		CategoryID     string    `json:"category_id"`     // 商品分类ID
		CategoryName   string    `json:"category_name"`   // 商品分类名称
		TrademarkID    string    `json:"trademark_id"`    // 商品品牌ID
		TrademarkName  string    `json:"trademark_name"`  // 商品品牌名称
		Stock          int32     `json:"stock"`           // 销售库存
		AvailableStock int32     `json:"available_stock"` // 剩余库存
		PurchasePrice  int64     `json:"purchase_price"`  // 进价
		SalePrice      int64     `json:"sale_price"`      // 售价
		Discount       *int32    `json:"discount"`        // 折扣
		DiscountPrice  int64     `json:"discount_price"`  // 折扣价
		Freight        int64     `json:"freight"`         // 运费
		StartTime      time.Time `json:"start_time"`      // 开始展示时间
		Priority       int32     `json:"priority"`        // 优先级
		UpdatedBy      string    `json:"updated_by"`      // 最后操作人
	}
)

type (
	MallItemDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"`
	}
	MallItemDetailResp struct {
		ID             int64     `json:"id,string"`       // 主键
		Status         int32     `json:"status"`          // 状态（0=待上架，1=已上架，2=已下架）
		ItemID         string    `json:"item_id"`         // 商品 id
		ItemName       string    `json:"item_name"`       // 商品名称
		SkuID          string    `json:"sku_id"`          // 商品 sku_id
		SpuID          string    `json:"spu_id"`          // 商品 spu_id
		TrademarkID    string    `json:"trademark_id"`    // 商品品牌id
		CategoryID     string    `json:"category_id"`     // 商品分类id
		IPID           string    `json:"ipid"`            // 商品 ip_id
		ItemSpecs      string    `json:"item_specs"`      // 商品规格
		PurchasePrice  int64     `json:"purchase_price"`  // 进价
		SellListings   int32     `json:"sell_listings"`   // 商品库存
		SalePrice      int64     `json:"sale_price"`      // 售价(分)
		Discount       *int32    `json:"discount"`        // 折扣
		Freight        int64     `json:"freight"`         // 运费(分)
		StockType      int32     `json:"stock_type"`      // 库存类型（1=限定库存，2=同步商品库存）
		Stock          int32     `json:"stock"`           // 限定库存量
		AvailableStock int32     `json:"available_stock"` // 直购商品可用库存
		TotalLimit     int32     `json:"total_limit"`     // 总限购数量
		DailyLimit     int32     `json:"daily_limit"`     // 每日限购数量
		StartTime      time.Time `json:"start_time"`      // 开始展示时间
		Priority       int32     `json:"priority"`        // 优先级
		CreatedAt      time.Time `json:"created_at"`      // 创建时间
	}
)

type (
	AddMallItemReq struct {
		ItemID     string    `json:"item_id" binding:"required"`                  // 商品ID
		SkuID      string    `json:"sku_id" binding:"required"`                   // 商品 sku_id
		SalePrice  int64     `json:"sale_price" binding:"required,min=1"`         // 售价(分)
		Discount   *int32    `json:"discount" binding:"omitempty,min=1,max=100"`  // 折扣
		Freight    *int64    `json:"freight" binding:"required,min=0"`            // 运费(分)
		StockType  int32     `json:"stock_type" binding:"required"`               // 库存类型（1=限定库存，2=同步商品库存）
		Stock      int32     `json:"stock" binding:"required,min=1"`              // 限定库存量
		TotalLimit *int32    `json:"total_limit" binding:"required,min=0"`        // 总限购数量
		DailyLimit *int32    `json:"daily_limit" binding:"required,min=0"`        // 每日限购数量
		StartTime  time.Time `json:"start_time" binding:"required"`               // 开始展示时间
		Priority   int32     `json:"priority" binding:"omitempty,min=0,max=9999"` // 优先级
	}

	AddMallItemResp struct {
		ID int64 `json:"id,string"` // 直购商品ID
	}
)

type (
	EditMallItemReq struct {
		ID         int64     `json:"id,string" binding:"required"`                // 直购商品ID
		ItemID     string    `json:"item_id" binding:"required"`                  // 商品ID
		SkuID      string    `json:"sku_id" binding:"required"`                   // 商品 sku_id
		SalePrice  int64     `json:"sale_price" binding:"required,min=1"`         // 售价(分)
		Discount   *int32    `json:"discount" binding:"omitempty,min=1,max=100"`  // 折扣
		Freight    *int64    `json:"freight" binding:"required,min=0"`            // 运费(分)
		StockType  int32     `json:"stock_type" binding:"required"`               // 库存类型（1=限定库存，2=同步商品库存）
		Stock      int32     `json:"stock" binding:"required,min=1"`              // 限定库存量
		TotalLimit *int32    `json:"total_limit" binding:"required,min=0"`        // 总限购数量
		DailyLimit *int32    `json:"daily_limit" binding:"required,min=0"`        // 每日限购数量
		StartTime  time.Time `json:"start_time" binding:"required"`               // 开始展示时间
		Priority   int32     `json:"priority" binding:"omitempty,min=0,max=9999"` // 优先级
	}

	EditMallItemResp struct {
		ID int64 `json:"id,string"` // 直购商品ID
	}
)

type (
	EditMallItemPriorityReq struct {
		ID       int64 `json:"id,string" binding:"required"`      // ID
		Priority int32 `json:"priority" binding:"min=1,max=9999"` // 优先级
	}

	EditMallItemPriorityResp struct {
		ID int64 `json:"id,string"` // 直购商品ID
	}
)

type (
	EditMallItemStatusReq struct {
		ID     int64 `json:"id,string" binding:"required"`    // ID
		Status int32 `json:"status" binding:"required,gte=0"` // 状态（0=待上架，1=已上架，2=已下架）

	}

	EditMallItemStatusResp struct {
		ID int64 `json:"id,string"` // 直购商品ID
	}
)

type (
	WebGetMallItemListReq struct {
		pagination.Pagination
		IPID      string `form:"ip_id" json:"ip_id"`           //商品 ip_id
		OrderBy   int32  `form:"order_by" json:"order_by"`     // 排序类型 1=综合，2=销量，3=价格
		SortOrder string `form:"sort_order" json:"sort_order"` // 排序方式  asc-升序 desc-降序
	}
	WebGetMallItemListResp struct {
		List    []*WebMallItemListData `json:"list"`
		HasMore bool                   `json:"has_more"` // 判断当前页是否为最后一页
	}
	WebMallItemListData struct {
		ID            int64  `json:"id,string"`      // 直购商品ID
		ItemID        string `json:"item_id"`        // 商品ID
		ItemName      string `json:"item_name"`      // 商品名称
		ItemIconURL   string `json:"item_icon_url"`  // 商品主图
		Priority      int32  `json:"priority"`       // 优先级
		SalePrice     int64  `json:"sale_price"`     // 售价
		Discount      *int32 `json:"discount"`       // 折扣
		DiscountPrice int64  `json:"discount_price"` // 折扣价
		Sales         int32  `json:"sales"`          // 销量
		WishlistCount int32  `json:"wishlist_count"` // 想要数量
	}
)

type (
	WebGetMallItemDetailReq struct {
		ID     int64  `form:"id" json:"id,string"`    // 直购商品ID
		ItemID string `form:"item_id" json:"item_id"` // 商品ID

	}
	WebGetMallItemDetailResp struct {
		ID              int64            `json:"id,string"`        // 直购商品ID
		Status          int32            `json:"status"`           // 直购商品状态（0=待上架，1=已上架，2=已下架）
		ItemID          string           `json:"item_id"`          // 商品ID
		ItemName        string           `json:"item_name"`        // 商品名称
		ItemIconUrl     string           `json:"item_icon_url"`    // 商品主图
		ImageInfos      []string         `json:"image_infos"`      // 商品图片
		Specs           string           `json:"specs"`            // 商品规格
		TrademarkID     string           `json:"trademark_id"`     // 商品品牌id
		CategoryID      string           `json:"category_id"`      // 商品分类id
		IPID            string           `json:"ip_id"`            // 商品 ip_id
		TrademarkName   string           `json:"trademark_name"`   // 商品品牌名称
		CategoryName    string           `json:"category_name"`    // 商品分类名称
		IPName          string           `json:"ip_name"`          // 商品IP名称
		Detail          string           `json:"detail"`           // 商品详情
		Stock           int32            `json:"stock"`            // 总库存
		AvailableStock  int32            `json:"available_stock"`  // 剩余库存
		SalePrice       int64            `json:"sale_price"`       // 售价
		Discount        *int32           `json:"discount"`         // 折扣
		DiscountPrice   int64            `json:"discount_price"`   // 折扣价
		Freight         int64            `json:"freight"`          // 运费
		MallInstruction *MallInstruction `json:"mall_instruction"` // 直购说明配置
	}
	MallInstruction struct {
		BannerUrl string `json:"banner_url"`
		Content   string `json:"content"`
	}
)

type (
	WebBuyReq struct {
		MallItemID int64  `json:"mall_item_id,string" binding:"required"` // 直购商品ID
		Quantity   int32  `json:"quantity" binding:"required"`            // 购买数量
		PayAmount  int64  `json:"pay_amount" binding:"required"`          // 支付金额
		AddressID  string `json:"address_id" binding:"required"`          // 地址ID
		Remark     string `json:"remark"`                                 // 备注
	}

	WebBuyResp struct {
		OrderID    int64     `json:"order_id,string"` // 直购订单ID
		PayAmount  int64     `json:"pay_amount"`      // 应支付金额
		CreatedAt  time.Time `json:"created_at"`      // 创建时间
		IsNewOrder bool      `json:"is_new_order"`    // 是否是新订单
	}
)

type (
	OpenTriggerDisplayByStartTimeReq struct{}
)

type (
	OpenUpdateMallItemNotifyReq struct {
		ItemIds []string `form:"item_ids" json:"item_ids" binding:"required"`
	}
)
