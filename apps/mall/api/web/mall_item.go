package web

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetMallItemList
// @Summary 获取直购商品列表
// @Description 获取直购商品列表
// @Tags 用户端-直购商品
// @x-apifox-folder "用户端/直购商品"
// @Param data query define.WebGetMallItemListReq true "获取参数"
// @Success 200 {object} response.Data{data=define.WebGetMallItemListResp}
// @Router  /web/v1/mall_item/list [GET]
// @Security Bearer
// @produce  json
func GetMallItemList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebGetMallItemListReq{}, s.WebGetMallItemList)
}

// GetMallItemDetail
// @Summary 获取直购商品详情
// @Description 获取直购商品详情
// @Tags 用户端-直购商品
// @x-apifox-folder "用户端/直购商品"
// @Param data query define.WebGetMallItemDetailReq true "获取参数"
// @Success 200 {object} response.Data{data=define.WebGetMallItemDetailResp}
// @Router  /web/v1/mall_item/detail [GET]
// @Security Bearer
// @produce  json
func GetMallItemDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebGetMallItemDetailReq{}, s.WebGetMallItemDetail)
}

// MallItemBuy
// @Summary 下单购买
// @Description 下单购买
// @Tags 用户端-直购商品
// @x-apifox-folder "用户端/直购商品"
// @Param data body define.WebBuyReq true "获取参数"
// @Success 200 {object} response.Data{data=define.WebBuyResp}
// @Router  /web/v1/mall_item/buy [post]
// @Security Bearer
// @produce  json
func MallItemBuy(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.WebBuyReq{}, s.WebMallItemBuyBuy)
}
