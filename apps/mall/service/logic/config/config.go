package config

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/global"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"strconv"
)

func GetString(ctx context.Context, key string, defaultValue string) (string, error) {
	result, err := global.REDIS.Get(ctx, constant.GetConfigKey(key)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return "", err
	}
	//缓存没命中
	if errors.Is(err, redis.Nil) {
		config, err := loadConfigInDb(ctx, key)
		if err != nil {
			return "", err
		}
		if config != nil {
			result = config.Value
		} else {
			return defaultValue, nil
		}
	}

	return result, err
}

func GetInt(ctx context.Context, key string, defaultValue int) (int, error) {
	result, err := global.REDIS.Get(ctx, constant.GetConfigKey(key)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return defaultValue, err
	}
	//缓存没命中
	if errors.Is(err, redis.Nil) {
		config, err := loadConfigInDb(ctx, key)
		if err != nil {
			log.Ctx(ctx).Errorf("load config err: %v", err)
			return defaultValue, err
		}
		if config != nil {
			result = config.Value
		} else {
			return defaultValue, nil
		}
	}

	parseInt, err := strconv.ParseInt(result, 10, 0)
	if err != nil {
		return defaultValue, err
	}

	return int(parseInt), err
}

func GetBool(ctx context.Context, key string, defaultValue bool) (bool, error) {
	result, err := global.REDIS.Get(ctx, constant.GetConfigKey(key)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return false, err
	}
	//缓存没命中
	if errors.Is(err, redis.Nil) {
		config, err := loadConfigInDb(ctx, key)
		if err != nil {
			return false, err
		}
		if config != nil {
			result = config.Value
		} else {
			return defaultValue, nil
		}
	}

	parseBool, err := strconv.ParseBool(result)
	if err != nil {
		return false, err
	}

	return parseBool, err
}

func GetObj(ctx context.Context, key string, model any) error {
	result, err := global.REDIS.Get(ctx, constant.GetConfigKey(key)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return err
	}
	//缓存没命中
	if errors.Is(err, redis.Nil) {
		config, err := loadConfigInDb(ctx, key)
		if err != nil {
			return err
		}
		if config != nil {
			result = config.Value
			err = buildCache(ctx, key, result)
			if err != nil {
				return err
			}
		} else {
			return nil
		}
	}
	err = json.Unmarshal([]byte(result), &model)
	if err != nil {
		return err
	}
	return nil
}

func GetObjs(ctx context.Context, key ...string) (map[string]interface{}, error) {
	var configKeys []string
	for _, k := range key {
		configKeys = append(configKeys, constant.GetConfigKey(k))
	}
	result, err := global.REDIS.MGet(ctx, configKeys...).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	dataList := make(map[string]interface{}, 0)
	var loadDbKeys []string
	// 需要查数据库
	for i, v := range result {
		if v == nil {
			loadDbKeys = append(loadDbKeys, key[i])
		}
	}
	if len(loadDbKeys) > 0 {
		commonConfigList, err := loadConfigInDbs(ctx, loadDbKeys...)
		if err != nil {
			return nil, err
		}
		for _, v := range commonConfigList {
			if v != nil {
				var data interface{}
				err = json.Unmarshal([]byte(utils.StrVal(v.Value)), &data)
				if err != nil {
					return nil, err
				}
				dataList[v.ConfigKey] = data
				// 加入缓存
				_ = buildCache(ctx, v.ConfigKey, data)
			}
		}
	} else {
		// 直接返回
		for i, v := range result {
			if v != nil {
				var data interface{}
				err = json.Unmarshal([]byte(utils.StrVal(v)), &data)
				if err != nil {
					return nil, err
				} else {
					dataList[key[i]] = data
				}
			}
		}
	}
	return dataList, nil
}

func loadConfigInDb(ctx context.Context, key string) (*model.CommonConfig, error) {
	cc := repo.GetQuery().CommonConfig
	qw := search.NewWrapper().Where(cc.ConfigKey.Eq(key))
	config, err := repo.NewCommonConfigRepo(cc.WithContext(ctx)).SelectOne(qw)
	if err != nil {
		return nil, errors.Wrap(err, "查询配置详情失败")
	}
	return config, nil
}
func loadConfigInDbs(ctx context.Context, key ...string) ([]*model.CommonConfig, error) {
	cc := repo.GetQuery().CommonConfig
	qw := search.NewWrapper().Where(cc.ConfigKey.In(key...))
	commonConfigList, err := repo.NewCommonConfigRepo(cc.WithContext(ctx)).SelectList(qw)
	if err != nil {
		return nil, err
	}
	return commonConfigList, nil
}

func buildCache(ctx context.Context, key string, val any) error {
	result, err := global.REDIS.Set(ctx, constant.GetConfigKey(key), utils.StrVal(val), 0).Result()
	if err != nil {
		log.Ctx(ctx).Errorf("设置缓存失败 k:%v, v:%v", key, val)
		return err
	}
	if result != "OK" {
		log.Ctx(ctx).Errorf("设置缓存失败 k:%v, v:%v", key, val)
		return errors.New("set cache fail")
	}
	return nil
}
