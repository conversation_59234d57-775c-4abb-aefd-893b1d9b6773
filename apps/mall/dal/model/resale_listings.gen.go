// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameResaleListings = "resale_listings"

// ResaleListings 转卖挂单表
type ResaleListings struct {
	ID                 int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	Status             int32                 `gorm:"column:status;type:tinyint;not null;comment:状态（0-已下架，1-出售中，2-已出售，3-已取消）" json:"status"`                   // 状态（0-已下架，1-出售中，2-已出售，3-已取消）
	SellerID           string                `gorm:"column:seller_id;type:varchar(32);not null;comment:卖家ID" json:"seller_id"`                                // 卖家ID
	SellerPhone        string                `gorm:"column:seller_phone;type:varchar(11);comment:卖家手机号码" json:"seller_phone"`                                 // 卖家手机号码
	ItemID             string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                    // 商品ID
	SkuID              string                `gorm:"column:sku_id;type:varchar(32);not null;comment:商品 sku_id" json:"sku_id"`                                 // 商品 sku_id
	ItemName           string                `gorm:"column:item_name;type:varchar(255);not null;comment:商品名称" json:"item_name"`                               // 商品名称
	ItemSpecs          string                `gorm:"column:item_specs;type:varchar(255);not null;comment:商品规格" json:"item_specs"`                             // 商品规格
	ItemIconURL        string                `gorm:"column:item_icon_url;type:varchar(255);not null;comment:商品主图" json:"item_icon_url"`                       // 商品主图
	TotalAmount        int64                 `gorm:"column:total_amount;type:bigint;not null;comment:挂单总额(分)" json:"total_amount"`                            // 挂单总额(分)
	SalePrice          int64                 `gorm:"column:sale_price;type:bigint;not null;comment:出售单价(分)" json:"sale_price"`                                // 出售单价(分)
	ListingQuantity    int32                 `gorm:"column:listing_quantity;type:int;not null;comment:出售总量" json:"listing_quantity"`                          // 出售总量
	SoldQuantity       int32                 `gorm:"column:sold_quantity;type:int;not null;comment:已出售数量" json:"sold_quantity"`                               // 已出售数量
	TotalIncome        int64                 `gorm:"column:total_income;type:bigint;not null;comment:总收入" json:"total_income"`                                // 总收入
	TotalFee           int64                 `gorm:"column:total_fee;type:bigint;not null;comment:总手续费" json:"total_fee"`                                     // 总手续费
	LatestTradeAt      *time.Time            `gorm:"column:latest_trade_at;type:datetime(3);comment:最新订单成交时间" json:"latest_trade_at"`                         // 最新订单成交时间
	Terminal           string                `gorm:"column:terminal;type:varchar(32);not null;comment:终端" json:"terminal"`                                    // 终端
	AppVersion         string                `gorm:"column:app_version;type:varchar(32);not null;comment:版本号" json:"app_version"`                             // 版本号
	Operator           string                `gorm:"column:operator;type:varchar(32);comment:最后操作人" json:"operator"`                                          // 最后操作人
	CreatedAt          time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt          time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel              soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
	ResaleListingsItem []*ResaleListingsItem `gorm:"foreignKey:resale_listings_id" json:"resale_listings_item"`
}

// TableName ResaleListings's table name
func (*ResaleListings) TableName() string {
	return TableNameResaleListings
}
