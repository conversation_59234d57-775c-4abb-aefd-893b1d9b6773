// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newCommonConfig(db *gorm.DB, opts ...gen.DOOption) commonConfig {
	_commonConfig := commonConfig{}

	_commonConfig.commonConfigDo.UseDB(db, opts...)
	_commonConfig.commonConfigDo.UseModel(&model.CommonConfig{})

	tableName := _commonConfig.commonConfigDo.TableName()
	_commonConfig.ALL = field.NewAsterisk(tableName)
	_commonConfig.ID = field.NewInt64(tableName, "id")
	_commonConfig.ConfigKey = field.NewString(tableName, "config_key")
	_commonConfig.Value = field.NewString(tableName, "value")
	_commonConfig.Remark = field.NewString(tableName, "remark")
	_commonConfig.CreatedBy = field.NewString(tableName, "created_by")
	_commonConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_commonConfig.UpdatedBy = field.NewString(tableName, "updated_by")
	_commonConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_commonConfig.IsDel = field.NewField(tableName, "is_del")

	_commonConfig.fillFieldMap()

	return _commonConfig
}

type commonConfig struct {
	commonConfigDo

	ALL       field.Asterisk
	ID        field.Int64  // 主键
	ConfigKey field.String // 配置Key
	Value     field.String // 配置值
	Remark    field.String // 备注
	CreatedBy field.String // 创建人
	CreatedAt field.Time   // 创建时间
	UpdatedBy field.String // 更新人
	UpdatedAt field.Time   // 更新时间
	IsDel     field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (c commonConfig) Table(newTableName string) *commonConfig {
	c.commonConfigDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c commonConfig) As(alias string) *commonConfig {
	c.commonConfigDo.DO = *(c.commonConfigDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *commonConfig) updateTableName(table string) *commonConfig {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.ConfigKey = field.NewString(table, "config_key")
	c.Value = field.NewString(table, "value")
	c.Remark = field.NewString(table, "remark")
	c.CreatedBy = field.NewString(table, "created_by")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedBy = field.NewString(table, "updated_by")
	c.UpdatedAt = field.NewTime(table, "updated_at")
	c.IsDel = field.NewField(table, "is_del")

	c.fillFieldMap()

	return c
}

func (c *commonConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *commonConfig) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 9)
	c.fieldMap["id"] = c.ID
	c.fieldMap["config_key"] = c.ConfigKey
	c.fieldMap["value"] = c.Value
	c.fieldMap["remark"] = c.Remark
	c.fieldMap["created_by"] = c.CreatedBy
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_by"] = c.UpdatedBy
	c.fieldMap["updated_at"] = c.UpdatedAt
	c.fieldMap["is_del"] = c.IsDel
}

func (c commonConfig) clone(db *gorm.DB) commonConfig {
	c.commonConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c commonConfig) replaceDB(db *gorm.DB) commonConfig {
	c.commonConfigDo.ReplaceDB(db)
	return c
}

type commonConfigDo struct{ gen.DO }

type ICommonConfigDo interface {
	gen.SubQuery
	Debug() ICommonConfigDo
	WithContext(ctx context.Context) ICommonConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICommonConfigDo
	WriteDB() ICommonConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICommonConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICommonConfigDo
	Not(conds ...gen.Condition) ICommonConfigDo
	Or(conds ...gen.Condition) ICommonConfigDo
	Select(conds ...field.Expr) ICommonConfigDo
	Where(conds ...gen.Condition) ICommonConfigDo
	Order(conds ...field.Expr) ICommonConfigDo
	Distinct(cols ...field.Expr) ICommonConfigDo
	Omit(cols ...field.Expr) ICommonConfigDo
	Join(table schema.Tabler, on ...field.Expr) ICommonConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICommonConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICommonConfigDo
	Group(cols ...field.Expr) ICommonConfigDo
	Having(conds ...gen.Condition) ICommonConfigDo
	Limit(limit int) ICommonConfigDo
	Offset(offset int) ICommonConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICommonConfigDo
	Unscoped() ICommonConfigDo
	Create(values ...*model.CommonConfig) error
	CreateInBatches(values []*model.CommonConfig, batchSize int) error
	Save(values ...*model.CommonConfig) error
	First() (*model.CommonConfig, error)
	Take() (*model.CommonConfig, error)
	Last() (*model.CommonConfig, error)
	Find() ([]*model.CommonConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommonConfig, err error)
	FindInBatches(result *[]*model.CommonConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.CommonConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICommonConfigDo
	Assign(attrs ...field.AssignExpr) ICommonConfigDo
	Joins(fields ...field.RelationField) ICommonConfigDo
	Preload(fields ...field.RelationField) ICommonConfigDo
	FirstOrInit() (*model.CommonConfig, error)
	FirstOrCreate() (*model.CommonConfig, error)
	FindByPage(offset int, limit int) (result []*model.CommonConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICommonConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c commonConfigDo) Debug() ICommonConfigDo {
	return c.withDO(c.DO.Debug())
}

func (c commonConfigDo) WithContext(ctx context.Context) ICommonConfigDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c commonConfigDo) ReadDB() ICommonConfigDo {
	return c.Clauses(dbresolver.Read)
}

func (c commonConfigDo) WriteDB() ICommonConfigDo {
	return c.Clauses(dbresolver.Write)
}

func (c commonConfigDo) Session(config *gorm.Session) ICommonConfigDo {
	return c.withDO(c.DO.Session(config))
}

func (c commonConfigDo) Clauses(conds ...clause.Expression) ICommonConfigDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c commonConfigDo) Returning(value interface{}, columns ...string) ICommonConfigDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c commonConfigDo) Not(conds ...gen.Condition) ICommonConfigDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c commonConfigDo) Or(conds ...gen.Condition) ICommonConfigDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c commonConfigDo) Select(conds ...field.Expr) ICommonConfigDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c commonConfigDo) Where(conds ...gen.Condition) ICommonConfigDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c commonConfigDo) Order(conds ...field.Expr) ICommonConfigDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c commonConfigDo) Distinct(cols ...field.Expr) ICommonConfigDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c commonConfigDo) Omit(cols ...field.Expr) ICommonConfigDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c commonConfigDo) Join(table schema.Tabler, on ...field.Expr) ICommonConfigDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c commonConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICommonConfigDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c commonConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) ICommonConfigDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c commonConfigDo) Group(cols ...field.Expr) ICommonConfigDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c commonConfigDo) Having(conds ...gen.Condition) ICommonConfigDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c commonConfigDo) Limit(limit int) ICommonConfigDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c commonConfigDo) Offset(offset int) ICommonConfigDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c commonConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICommonConfigDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c commonConfigDo) Unscoped() ICommonConfigDo {
	return c.withDO(c.DO.Unscoped())
}

func (c commonConfigDo) Create(values ...*model.CommonConfig) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c commonConfigDo) CreateInBatches(values []*model.CommonConfig, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c commonConfigDo) Save(values ...*model.CommonConfig) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c commonConfigDo) First() (*model.CommonConfig, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommonConfig), nil
	}
}

func (c commonConfigDo) Take() (*model.CommonConfig, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommonConfig), nil
	}
}

func (c commonConfigDo) Last() (*model.CommonConfig, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommonConfig), nil
	}
}

func (c commonConfigDo) Find() ([]*model.CommonConfig, error) {
	result, err := c.DO.Find()
	return result.([]*model.CommonConfig), err
}

func (c commonConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommonConfig, err error) {
	buf := make([]*model.CommonConfig, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c commonConfigDo) FindInBatches(result *[]*model.CommonConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c commonConfigDo) Attrs(attrs ...field.AssignExpr) ICommonConfigDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c commonConfigDo) Assign(attrs ...field.AssignExpr) ICommonConfigDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c commonConfigDo) Joins(fields ...field.RelationField) ICommonConfigDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c commonConfigDo) Preload(fields ...field.RelationField) ICommonConfigDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c commonConfigDo) FirstOrInit() (*model.CommonConfig, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommonConfig), nil
	}
}

func (c commonConfigDo) FirstOrCreate() (*model.CommonConfig, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommonConfig), nil
	}
}

func (c commonConfigDo) FindByPage(offset int, limit int) (result []*model.CommonConfig, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c commonConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c commonConfigDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c commonConfigDo) Delete(models ...*model.CommonConfig) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *commonConfigDo) withDO(do gen.Dao) *commonConfigDo {
	c.DO = *do.(*gen.DO)
	return c
}
