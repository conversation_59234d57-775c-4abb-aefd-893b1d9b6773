package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/global"
	"marketplace_service/pkg/cache"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/tmt"
)

// GetItemClassifyInfo 获取商品分类信息(带缓存)
func GetItemClassifyInfo(ctx context.Context, id string) (*tmt.GetItemClassifyInfo, error) {
	var itemClassifyInfo *tmt.GetItemClassifyInfo

	cacheKey := constant.GetItemClassifyInfoKey(id)
	err := cache.GetFromCache(ctx, cacheKey, &itemClassifyInfo)
	if errors.Is(err, redis.Nil) {
		itemClassifyInfo, err = GetItemClassifyInfoByRemote(ctx, id)
		if err != nil {
			log.Ctx(ctx).Errorf("redis获取商品分类信息失败 err:%+v", err)
			return nil, err
		}
	} else if err != nil {
		log.Ctx(ctx).Errorf("redis获取商品分类信息失败 err:%+v", err)
		return nil, err
	}
	return itemClassifyInfo, nil
}

// GetItemClassifyInfoByRemote 获取商品分类信息(不使用缓存,强制刷新缓存)
func GetItemClassifyInfoByRemote(ctx context.Context, id string) (*tmt.GetItemClassifyInfo, error) {
	itemClassifyInfos, err := tmt.GetItemClassifyInfos(ctx, []string{id})
	if err != nil {
		log.Ctx(ctx).Errorf("查询商品分类信息错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询商品分类信息错误")
	}
	if len(itemClassifyInfos) > 0 {
		itemClassifyInfo := itemClassifyInfos[0]
		cacheKey := constant.GetItemClassifyInfoKey(id)
		if err := cache.SetToCache(ctx, cacheKey, itemClassifyInfo, constant.ItemClassifyInfoTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置商品分类信息缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置商品分类信息缓存成功 classifyId:%v", id)
		return itemClassifyInfo, nil
	}
	return nil, nil
}

// GetItemClassifyInfos 获取商品分类信息列表(带缓存)
func GetItemClassifyInfos(ctx context.Context, ids ...string) ([]*tmt.GetItemClassifyInfo, error) {
	result := make([]*tmt.GetItemClassifyInfo, 0)
	var queryIds []string
	var redisKeys []string

	// 生成所有缓存键
	for _, id := range ids {
		redisKeys = append(redisKeys, constant.GetItemClassifyInfoKey(id))
	}

	// 批量从缓存获取
	itemClassifyInfoStrList, _ := global.REDIS.MGet(ctx, redisKeys...).Result()

	// 处理缓存命中数据
	if itemClassifyInfoStrList != nil && len(itemClassifyInfoStrList) > 0 {
		for k, infoStr := range itemClassifyInfoStrList {
			if infoStr != nil && infoStr != "" {
				itemClassifyInfo := &tmt.GetItemClassifyInfo{}
				err := utils.MsgpackUnmarshal([]byte(infoStr.(string)), itemClassifyInfo)
				if err != nil {
					log.Ctx(ctx).Errorf("redis商品分类信息转换结构体失败 err:%+v", err)
				} else {
					result = append(result, itemClassifyInfo)
				}
			} else {
				queryIds = append(queryIds, ids[k])
			}
		}
	}

	// 处理缓存未命中数据
	if len(queryIds) > 0 {
		itemClassifyInfos, err := tmt.GetItemClassifyInfos(ctx, queryIds)
		if err != nil {
			log.Ctx(ctx).Errorf("查询商品分类信息列表错误 err:%+v", err)
			return nil, response.ParamErr.SetMsg("查询商品分类信息列表错误")
		}

		for _, itemClassifyInfo := range itemClassifyInfos {
			result = append(result, itemClassifyInfo)
			cacheKey := constant.GetItemClassifyInfoKey(itemClassifyInfo.Id)
			if err := cache.SetToCache(ctx, cacheKey, itemClassifyInfo, constant.ItemClassifyInfoTTL); err != nil {
				log.Ctx(ctx).Errorf("redis设置商品分类信息缓存失败 err:%+v", err)
			}
			log.Ctx(ctx).Infof("redis设置商品分类信息缓存成功 classifyId:%v", itemClassifyInfo.Id)
		}
	}
	return result, nil
}

// GetItemClassifyInfoMap 获取商品分类信息map(带缓存)
func GetItemClassifyInfoMap(ctx context.Context, ids ...string) (map[string]*tmt.GetItemClassifyInfo, error) {
	itemClassifyInfos, err := GetItemClassifyInfos(ctx, ids...)
	if err != nil {
		return nil, err
	}
	itemClassifyInfoMap := make(map[string]*tmt.GetItemClassifyInfo, len(itemClassifyInfos))
	for _, itemClassifyInfo := range itemClassifyInfos {
		itemClassifyInfoMap[itemClassifyInfo.Id] = itemClassifyInfo
	}
	return itemClassifyInfoMap, nil
}

// GetItemClassifyInfosByRemote 获取商品分类信息列表(不使用缓存,强制刷新缓存)
func GetItemClassifyInfosByRemote(ctx context.Context, ids ...string) ([]*tmt.GetItemClassifyInfo, error) {
	result := make([]*tmt.GetItemClassifyInfo, 0)
	itemClassifyInfos, err := tmt.GetItemClassifyInfos(ctx, ids)
	if err != nil {
		log.Ctx(ctx).Errorf("查询remote商品分类信息列表错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询remote商品分类信息列表错误")
	}

	for _, itemClassifyInfo := range itemClassifyInfos {
		result = append(result, itemClassifyInfo)
		cacheKey := constant.GetItemClassifyInfoKey(itemClassifyInfo.Id)
		if err := cache.SetToCache(ctx, cacheKey, itemClassifyInfo, constant.ItemClassifyInfoTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置商品分类信息缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置商品分类信息缓存成功 classifyId:%v", itemClassifyInfo.Id)
	}
	return result, nil
}

// GetItemClassifyInfosByRemoteMap 获取商品分类信息map(不使用缓存,强制刷新缓存)
func GetItemClassifyInfosByRemoteMap(ctx context.Context, ids ...string) (map[string]*tmt.GetItemClassifyInfo, error) {
	itemClassifyInfos, err := GetItemClassifyInfosByRemote(ctx, ids...)
	if err != nil {
		return nil, err
	}
	itemClassifyInfoMap := make(map[string]*tmt.GetItemClassifyInfo, len(itemClassifyInfos))
	for _, itemClassifyInfo := range itemClassifyInfos {
		itemClassifyInfoMap[itemClassifyInfo.Id] = itemClassifyInfo
	}
	return itemClassifyInfoMap, nil
}
