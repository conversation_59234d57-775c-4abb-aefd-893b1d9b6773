package admin

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetConfigList
// @Summary 获取配置列表
// @Description 获取配置列表
// @Tags 管理端-配置管理
// @x-apifox-folder "管理端/配置管理"
// @Param data query define.GetConfigListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetConfigListResp}
// @Router  /admin/v1/common_config/list [get]
// @Security Bearer
// @produce  json
func GetConfigList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetConfigListReq{}, s.GetConfigList)
}

// GetConfigDetail
// @Summary 获取配置详情
// @Description 获取配置详情
// @Tags 管理端-配置管理
// @x-apifox-folder "管理端/配置管理"
// @Param data query define.GetConfigDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetConfigDetailResp}
// @Router  /admin/v1/common_config/detail [get]
// @Security Bearer
// @produce  json
func GetConfigDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetConfigDetailReq{}, s.GetConfigDetail)
}

// EditConfig
// @Summary 编辑配置
// @Description 编辑配置
// @Tags 管理端-配置管理
// @x-apifox-folder "管理端/配置管理"
// @Param data body define.EditConfigReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditConfigResp}
// @Router  /admin/v1/common_config/edit [POST]
// @Security Bearer
// @produce  json
func EditConfig(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditConfigReq{}, s.EditConfig)
}
