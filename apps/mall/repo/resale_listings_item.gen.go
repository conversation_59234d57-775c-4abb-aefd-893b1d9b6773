package repo

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/dal/query"
	"marketplace_service/pkg/pagination"
	"marketplace_service/pkg/search"
)

type ResaleListingsItemRepository struct {
	do query.IResaleListingsItemDo
}

func NewResaleListingsItemRepo(do query.IResaleListingsItemDo) *ResaleListingsItemRepository {
	return &ResaleListingsItemRepository{
		do: do,
	}
}

func (r *ResaleListingsItemRepository) SelectOne(wrapper *search.Wrapper) (*model.ResaleListingsItem, error) {
	records, err := r.do.Scopes(wrapper.Build()).Find()
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	if len(records) > 1 {
		return nil, errors.New("more than one item found")
	}
	return records[0], nil
}

func (r *ResaleListingsItemRepository) SelectList(wrapper *search.Wrapper) ([]*model.ResaleListingsItem, error) {
	records, err := r.do.Scopes(wrapper.Build()).Find()
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (r *ResaleListingsItemRepository) SelectPage(wrapper *search.Wrapper, req pagination.IPagination) ([]*model.ResaleListingsItem, int64, error) {
	records, count, err := r.do.Scopes(wrapper.Build()).
		FindByPage(search.Paginate(req.GetPageSize(), req.GetPage()))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *ResaleListingsItemRepository) QuickSelectPage(req pagination.IPagination) ([]*model.ResaleListingsItem, int64, error) {
	records, count, err := r.do.Scopes(search.MakeCondition(req)).
		FindByPage(search.Paginate(req.GetPageSize(), req.GetPage()))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *ResaleListingsItemRepository) Count(wrapper *search.Wrapper) (int64, error) {
	count, err := r.do.Scopes(wrapper.Build()).Count()
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *ResaleListingsItemRepository) Save(model *model.ResaleListingsItem) error {
	err := r.do.Create(model)
	if err != nil {
		return err
	}
	return nil
}

func (r *ResaleListingsItemRepository) BatchSave(models []*model.ResaleListingsItem, batchSize int) error {
	err := r.do.CreateInBatches(models, batchSize)
	if err != nil {
		return err
	}
	return nil
}

func (r *ResaleListingsItemRepository) UpdateById(model *model.ResaleListingsItem) error {
	result, err := r.do.Updates(model)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *ResaleListingsItemRepository) Update(ms *model.ResaleListingsItem, wrapper *search.Wrapper) error {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(ms)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *ResaleListingsItemRepository) UpdateField(params interface{}, wrapper *search.Wrapper) error {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(params)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *ResaleListingsItemRepository) RemoveByIds(ms ...*model.ResaleListingsItem) error {
	result, err := r.do.Delete(ms...)
	if err != nil {
		return err
	} else if result.RowsAffected != int64(len(ms)) {
		return UpdateFail
	}
	return nil
}
