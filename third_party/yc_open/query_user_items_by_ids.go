package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"marketplace_service/global"
	"marketplace_service/third_party/yc_open/define"
)

type (
	QueryUserItemsByIDsData struct {
		List []*define.UserItemData `json:"list"`
	}
	QueryUserItemsByIDsResp struct {
		Code int32                   `json:"code" form:"code"`
		Desc string                  `json:"desc" form:"desc"`
		Data QueryUserItemsByIDsData `json:"data" form:"data"`
	}
)

// QueryUserItemsByIDs 根据 ids 获取物品列表（一次最多 200 条）
func QueryUserItemsByIDs(ctx context.Context, userItemIDs []string) ([]*define.UserItemData, error) {
	rsp := &QueryUserItemsByIDsResp{}

	params := map[string]interface{}{
		"user_item_ids": userItemIDs,
		"sign":          "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("QueryUserItemsByIDs sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/query_user_items_by_ids"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询气仓列表，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询气仓列表，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询气仓列表失败")
	}
	return rsp.Data.List, err
}
