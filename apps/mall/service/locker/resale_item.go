package locker

import (
	"fmt"
	"time"
)

// ResaleItemAction 转卖商品
type ResaleItemAction string

const (
	UpdateResaleItem       ResaleItemAction = "update_resale_item"        // 更新转卖商品
	TakeDownResaleListings ResaleItemAction = "take_down_resale_listings" // 下架挂单

)

type ResaleItemLock struct {
	ac  ResaleItemAction // 行为
	tag string           // 唯一标识
}

func (p *ResaleItemLock) GetCacheKey() string {
	return fmt.Sprintf("marketplace_service:mall_item:locker:%s:%s", p.ac, p.tag)
}

func (p *ResaleItemLock) LockTime() time.Duration {
	return time.Minute * 10
}

func NewResaleItemLockActionLock(ac ResaleItemAction, tag string) *ResaleItemLock {
	return &ResaleItemLock{ac: ac, tag: tag}
}
