package repo

import (
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/pkg/search"
)

func (r *ResaleListingsItemRepository) BatchUpdate(ms *model.ResaleListingsItem, wrapper *search.Wrapper) (int64, error) {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(ms)
	if err != nil {
		return 0, err
	} else if result.RowsAffected == 0 {
		return 0, UpdateFail
	}
	return result.RowsAffected, nil
}
