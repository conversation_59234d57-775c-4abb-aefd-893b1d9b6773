package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/global"
	"marketplace_service/pkg/cache"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/tmt"
)

// GetTrademarkInfo 获取商品品牌信息(带缓存)
func GetTrademarkInfo(ctx context.Context, id string) (*tmt.GetTrademarksInfo, error) {
	var trademarkInfo *tmt.GetTrademarksInfo

	cacheKey := constant.GetTrademarkInfoKey(id)
	err := cache.GetFromCache(ctx, cacheKey, &trademarkInfo)
	if errors.Is(err, redis.Nil) {
		trademarkInfo, err = GetTrademarkInfoByRemote(ctx, id)
		if err != nil {
			log.Ctx(ctx).Errorf("redis获取商品品牌信息失败 err:%+v", err)
			return nil, err
		}
	} else if err != nil {
		log.Ctx(ctx).Errorf("redis获取商品品牌信息失败 err:%+v", err)
		return nil, err
	}
	return trademarkInfo, nil
}

// GetTrademarkInfoByRemote 获取商品品牌信息(不使用缓存,强制刷新缓存)
func GetTrademarkInfoByRemote(ctx context.Context, id string) (*tmt.GetTrademarksInfo, error) {
	trademarkInfos, err := tmt.GetTrademarksInfos(ctx, []string{id})
	if err != nil {
		log.Ctx(ctx).Errorf("查询商品品牌信息错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询商品品牌信息错误")
	}
	if len(trademarkInfos) > 0 {
		trademarkInfo := trademarkInfos[0]
		cacheKey := constant.GetTrademarkInfoKey(id)
		if err := cache.SetToCache(ctx, cacheKey, trademarkInfo, constant.TrademarkInfoTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置商品品牌信息缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置商品品牌信息缓存成功 trademarkId:%v", id)
		return trademarkInfo, nil
	}
	return nil, nil
}

// GetTrademarksInfos 获取商品品牌信息列表(带缓存)
func GetTrademarksInfos(ctx context.Context, ids ...string) ([]*tmt.GetTrademarksInfo, error) {
	result := make([]*tmt.GetTrademarksInfo, 0)
	var queryIds []string
	var redisKeys []string

	// 生成所有缓存键
	for _, id := range ids {
		redisKeys = append(redisKeys, constant.GetTrademarkInfoKey(id))
	}

	// 批量从缓存获取
	trademarkInfoStrList, _ := global.REDIS.MGet(ctx, redisKeys...).Result()

	// 处理缓存命中数据
	if trademarkInfoStrList != nil && len(trademarkInfoStrList) > 0 {
		for k, infoStr := range trademarkInfoStrList {
			if infoStr != nil && infoStr != "" {
				trademarkInfo := &tmt.GetTrademarksInfo{}
				err := utils.MsgpackUnmarshal([]byte(infoStr.(string)), trademarkInfo)
				if err != nil {
					log.Ctx(ctx).Errorf("redis商品品牌信息转换结构体失败 err:%+v", err)
				} else {
					result = append(result, trademarkInfo)
				}
			} else {
				queryIds = append(queryIds, ids[k])
			}
		}
	}

	// 处理缓存未命中数据
	if len(queryIds) > 0 {
		trademarkInfos, err := tmt.GetTrademarksInfos(ctx, queryIds)
		if err != nil {
			log.Ctx(ctx).Errorf("查询商品品牌信息列表错误 err:%+v", err)
			return nil, response.ParamErr.SetMsg("查询商品品牌信息列表错误")
		}

		for _, trademarkInfo := range trademarkInfos {
			result = append(result, trademarkInfo)
			cacheKey := constant.GetTrademarkInfoKey(trademarkInfo.Id)
			if err := cache.SetToCache(ctx, cacheKey, trademarkInfo, constant.TrademarkInfoTTL); err != nil {
				log.Ctx(ctx).Errorf("redis设置商品品牌信息缓存失败 err:%+v", err)
			}
			log.Ctx(ctx).Infof("redis设置商品品牌信息缓存成功 trademarkId:%v", trademarkInfo.Id)
		}
	}
	return result, nil
}

// GetTrademarksInfoMap 获取商品品牌信息map(带缓存)
func GetTrademarksInfoMap(ctx context.Context, ids ...string) (map[string]*tmt.GetTrademarksInfo, error) {
	trademarkInfos, err := GetTrademarksInfos(ctx, ids...)
	if err != nil {
		return nil, err
	}
	trademarkInfoMap := make(map[string]*tmt.GetTrademarksInfo, len(trademarkInfos))
	for _, trademarkInfo := range trademarkInfos {
		trademarkInfoMap[trademarkInfo.Id] = trademarkInfo
	}
	return trademarkInfoMap, nil
}

// GetTrademarksInfosByRemote 获取商品品牌信息列表(不使用缓存,强制刷新缓存)
func GetTrademarksInfosByRemote(ctx context.Context, ids ...string) ([]*tmt.GetTrademarksInfo, error) {
	result := make([]*tmt.GetTrademarksInfo, 0)
	trademarkInfos, err := tmt.GetTrademarksInfos(ctx, ids)
	if err != nil {
		log.Ctx(ctx).Errorf("查询remote商品品牌信息列表错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询remote商品品牌信息列表错误")
	}

	for _, trademarkInfo := range trademarkInfos {
		result = append(result, trademarkInfo)
		cacheKey := constant.GetTrademarkInfoKey(trademarkInfo.Id)
		if err := cache.SetToCache(ctx, cacheKey, trademarkInfo, constant.TrademarkInfoTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置商品品牌信息缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置商品品牌信息缓存成功 trademarkId:%v", trademarkInfo.Id)
	}
	return result, nil
}

// GetTrademarksInfosByRemoteMap 获取商品品牌信息map(不使用缓存,强制刷新缓存)
func GetTrademarksInfosByRemoteMap(ctx context.Context, ids ...string) (map[string]*tmt.GetTrademarksInfo, error) {
	trademarkInfos, err := GetTrademarksInfosByRemote(ctx, ids...)
	if err != nil {
		return nil, err
	}
	trademarkInfoMap := make(map[string]*tmt.GetTrademarksInfo, len(trademarkInfos))
	for _, trademarkInfo := range trademarkInfos {
		trademarkInfoMap[trademarkInfo.Id] = trademarkInfo
	}
	return trademarkInfoMap, nil
}
