package utils

import (
	"bytes"
	"encoding/gob"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"
)

var (
	ErrInvalidInput = errors.New("invalid input")
	ErrDecodeFailed = errors.New("decode failed")
)

// JsonStrToMap converts JSON string to map[string]interface{}
// Example: `{"key":"value"}` -> map[string]interface{}{"key":"value"}
func JsonStrToMap(str string) (map[string]interface{}, error) {
	if str == "" {
		return nil, fmt.Errorf("%w: empty string", ErrInvalidInput)
	}

	var result map[string]interface{}
	if err := json.Unmarshal([]byte(str), &result); err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDecodeFailed, err)
	}
	return result, nil
}

// ObjToMap converts any object to map[string]interface{}
// Supports: struct, map, string (JSON)
func ObjToMap(obj interface{}) (map[string]interface{}, error) {
	if obj == nil {
		return nil, fmt.Errorf("%w: nil input", ErrInvalidInput)
	}

	switch v := obj.(type) {
	case string:
		return JsonStrToMap(v)
	case map[string]interface{}:
		return v, nil
	default:
		// Use mapstructure for direct conversion (more efficient than JSON roundtrip)
		var result map[string]interface{}
		if err := mapstructure.Decode(obj, &result); err != nil {
			return nil, fmt.Errorf("%w: %v", ErrDecodeFailed, err)
		}
		return result, nil
	}
}

// ObjToJsonStr converts object to JSON string with unescaped HTML
func ObjToJsonStr(obj interface{}) (string, error) {
	if obj == nil {
		return "", fmt.Errorf("%w: nil input", ErrInvalidInput)
	}

	bf := bytes.NewBuffer(nil)
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	if err := jsonEncoder.Encode(obj); err != nil {
		return "", fmt.Errorf("%w: %v", ErrDecodeFailed, err)
	}
	return strings.TrimSpace(bf.String()), nil
}

// Obj2JsonStr converts object to JSON string with unescaped HTML
func Obj2JsonStr(obj interface{}) string {
	if obj == nil {
		return ""
	}

	bf := bytes.NewBuffer(nil)
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	if err := jsonEncoder.Encode(obj); err != nil {
		return ""
	}
	return strings.TrimSpace(bf.String())
}

// StringToTimeHookFunc returns a DecodeHookFunc that converts strings to time.Time
func StringToTimeHookFunc(layout string) mapstructure.DecodeHookFunc {
	return func(
		f reflect.Type,
		t reflect.Type,
		data interface{}) (interface{}, error) {
		if t != reflect.TypeOf(time.Time{}) {
			return data, nil
		}

		switch f.Kind() {
		case reflect.String:
			return time.Parse(layout, data.(string))
		case reflect.Float64:
			return time.Unix(0, int64(data.(float64))*int64(time.Millisecond)), nil
		case reflect.Int64:
			return time.Unix(0, data.(int64)*int64(time.Millisecond)), nil
		default:
			return data, nil
		}
	}
}

// JsonStrToStruct converts JSON string directly to struct
func JsonStrToStruct(jsonStr string, out interface{}) error {
	if jsonStr == "" {
		return fmt.Errorf("%w: empty json string", ErrInvalidInput)
	}

	if err := json.Unmarshal([]byte(jsonStr), out); err != nil {
		return fmt.Errorf("%w: %v", ErrDecodeFailed, err)
	}
	return nil
}

// NumberToInt64 converts any numeric type to int64
func NumberToInt64(value interface{}) (int64, error) {
	switch v := value.(type) {
	case int:
		return int64(v), nil
	case int64:
		return v, nil
	case float64:
		return int64(v), nil
	case string:
		return strconv.ParseInt(v, 10, 64)
	default:
		return 0, fmt.Errorf("%w: unsupported type %T", ErrInvalidInput, value)
	}
}

// NumberToInt32 converts any numeric type to int32
func NumberToInt32(value interface{}) (int32, error) {
	num, err := NumberToInt64(value)
	if err != nil {
		return 0, err
	}
	return int32(num), nil
}

// NumberStrToInt32Slice converts comma-separated string to []int32
func NumberStrToInt32Slice(value string) ([]int32, error) {
	if value == "" {
		return nil, nil
	}

	strValues := strings.Split(value, ",")
	nums := make([]int32, 0, len(strValues))

	for _, str := range strValues {
		num, err := NumberToInt32(strings.TrimSpace(str))
		if err != nil {
			return nil, err
		}
		nums = append(nums, num)
	}

	return nums, nil
}

// BoolToInt32 converts bool to *int32 (1 for true, 0 for false)
func BoolToInt32(isTrue bool) *int32 {
	var result int32
	if isTrue {
		result = 1
	} else {
		result = 0
	}
	return &result
}

// GetBytes encodes any object to []byte using gob
func GetBytes(key interface{}) ([]byte, error) {
	if key == nil {
		return nil, fmt.Errorf("%w: nil input", ErrInvalidInput)
	}

	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	if err := enc.Encode(key); err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDecodeFailed, err)
	}
	return buf.Bytes(), nil
}

func MapToStructArray(data []map[string]interface{}, result interface{}) error {
	// 1. 将 []map[string]interface{} 转换为 JSON 字节
	bytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	// 2. 将 JSON 字节解析到目标结构体（result 必须是指针）
	return json.Unmarshal(bytes, result)
}

func MapToStruct(m map[string]interface{}, target interface{}) error {
	// 将 map 转换为 JSON 字节
	data, err := json.Marshal(m)
	if err != nil {
		return err
	}
	// 将 JSON 字节解析到目标结构体
	return json.Unmarshal(data, target)
}
