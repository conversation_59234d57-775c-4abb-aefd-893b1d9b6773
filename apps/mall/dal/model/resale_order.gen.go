// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameResaleOrder = "resale_order"

// ResaleOrder 转卖订单表
type ResaleOrder struct {
	ID               int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                    // 主键
	Status           int32                 `gorm:"column:status;type:tinyint;not null;comment:状态（0=待支付，1=物品锁定成功，10=已支付，20=转移中，30=已完成，40=已取消）" json:"status"` // 状态（0=待支付，1=物品锁定成功，10=已支付，20=转移中，30=已完成，40=已取消）
	BuyerID          string                `gorm:"column:buyer_id;type:varchar(32);not null;comment:用户ID" json:"buyer_id"`                                   // 用户ID
	BuyerPhone       string                `gorm:"column:buyer_phone;type:varchar(11);not null;comment:用户手机号码" json:"buyer_phone"`                           // 用户手机号码
	SellerID         string                `gorm:"column:seller_id;type:varchar(32);not null;comment:出售用户ID" json:"seller_id"`                               // 出售用户ID
	SellerPhone      string                `gorm:"column:seller_phone;type:varchar(11);comment:出售用户手机号码" json:"seller_phone"`                                // 出售用户手机号码
	SkuID            string                `gorm:"column:sku_id;type:varchar(32);not null;comment:sku ID" json:"sku_id"`                                     // sku ID
	ItemID           string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                     // 商品ID
	ItemName         string                `gorm:"column:item_name;type:varchar(255);not null;comment:商品名称" json:"item_name"`                                // 商品名称
	ItemIconURL      string                `gorm:"column:item_icon_url;type:varchar(255);not null;comment:商品主图" json:"item_icon_url"`                        // 商品主图
	ItemSpecs        string                `gorm:"column:item_specs;type:varchar(255);not null;comment:商品规格" json:"item_specs"`                              // 商品规格
	ResaleListingsID int64                 `gorm:"column:resale_listings_id;type:bigint;not null;comment:出售单ID" json:"resale_listings_id"`                   // 出售单ID
	PaymentStatus    int32                 `gorm:"column:payment_status;type:tinyint;not null;comment:支付状态（0=未支付，1=已支付）" json:"payment_status"`              // 支付状态（0=未支付，1=已支付）
	TotalAmount      int64                 `gorm:"column:total_amount;type:bigint;not null;comment:总金额" json:"total_amount"`                                 // 总金额
	TotalFee         int64                 `gorm:"column:total_fee;type:bigint;not null;comment:手续费" json:"total_fee"`                                       // 手续费
	PayAmount        int64                 `gorm:"column:pay_amount;type:bigint;not null;comment:支付金额" json:"pay_amount"`                                    // 支付金额
	SalePrice        int64                 `gorm:"column:sale_price;type:bigint;not null;comment:售价" json:"sale_price"`                                      // 售价
	Quantity         int32                 `gorm:"column:quantity;type:int;not null;comment:购买数量" json:"quantity"`                                           // 购买数量
	PaymentMethod    string                `gorm:"column:payment_method;type:varchar(255);comment:支付方式" json:"payment_method"`                               // 支付方式
	PaymentAt        *time.Time            `gorm:"column:payment_at;type:datetime(3);comment:支付时间" json:"payment_at"`                                        // 支付时间
	TransferAt       *time.Time            `gorm:"column:transfer_at;type:datetime(3);comment:物品转移时间" json:"transfer_at"`                                    // 物品转移时间
	FinishedAt       *time.Time            `gorm:"column:finished_at;type:datetime(3);comment:订单完成时间" json:"finished_at"`                                    // 订单完成时间
	Terminal         string                `gorm:"column:terminal;type:varchar(32);not null;comment:终端" json:"terminal"`                                     // 终端
	AppVersion       string                `gorm:"column:app_version;type:varchar(32);not null;comment:版本号" json:"app_version"`                              // 版本号
	BatchID          int64                 `gorm:"column:batch_id;type:bigint;not null;comment:批量购买ID" json:"batch_id"`                                      // 批量购买ID
	CreatedAt        time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`  // 创建时间
	UpdatedAt        time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`  // 更新时间
	IsDel            soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`         // 是否删除【0->未删除; 1->删除】
	ResaleOrderItem  []*ResaleOrderItem    `gorm:"foreignKey:resale_order_id" json:"resale_order_item"`
}

// TableName ResaleOrder's table name
func (*ResaleOrder) TableName() string {
	return TableNameResaleOrder
}
