package locker

import (
	"fmt"
	"time"
)

// ResaleListingsAction 转卖挂单相关行为枚举
type ResaleListingsAction string

const (
	ResaleListingsAdd        ResaleListingsAction = "resale_listings_add"         // 挂单出售
	ResaleListingsDown       ResaleListingsAction = "resale_listings_down"        // 挂单下架
	ResaleListingsItemCancel ResaleListingsAction = "resale_listings_item_cancel" // 挂单物品取消
	ResaleListingsItemSync   ResaleListingsAction = "resale_listings_item_sync"   // 挂单物品同步数据
)

type ResaleListingsLock struct {
	ac  ResaleListingsAction // 行为
	tag string               // 唯一标识
}

func (p *ResaleListingsLock) GetCacheKey() string {
	return fmt.Sprintf("marketplace_service:resale_listings:locker:%s:%s", p.ac, p.tag)
}

func (p *ResaleListingsLock) LockTime() time.Duration {
	return time.Second * 10
}

func NewResaleListingsLock(tag string, ac ResaleListingsAction) *ResaleListingsLock {
	return &ResaleListingsLock{tag: tag, ac: ac}
}
