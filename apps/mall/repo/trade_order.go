package repo

import (
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
)

func (r *TradeOrderRepository) GetWebTradeOrderStatusStat(useId string) (res []*define.GetWebTradeOrderStatusStatData, err error) {
	data := make([]*define.GetWebTradeOrderStatusStatData, 0)
	err = GetDB().Model(&model.TradeOrder{}).Where("user_id = ? and order_status in ?", useId, enums.OrderStatusForUserList).
		Select("COUNT(id) as count, order_status as order_status").Group("order_status").Scan(&data).Error
	return data, err
}
