package tmt

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type GetTrademarksInfo struct {
	Id       string `json:"_id" form:"id"`
	Name     string `json:"name" form:"name"`
	Priority int64  `json:"priority" form:"priority"`
	Status   int32  `json:"status" form:"status"`
	Level    int32  `json:"level" form:"level"`
	Icon     string `json:"icon" form:"icon"`
}

type GetGetTrademarksInfosRsp struct {
	Code int32                `json:"code" form:"code"`
	Desc string               `json:"desc" form:"desc"`
	Data []*GetTrademarksInfo `json:"data" form:"data"`
}

func GetTrademarksInfos(ctx context.Context, ids []string) ([]*GetTrademarksInfo, error) {
	rsp := &GetGetTrademarksInfosRsp{}
	req, err := request.Tmt()
	if err != nil {
		return nil, err
	}
	err = req.Call(
		ctx,
		"open/trademark/v1/list",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"trademarks_ids": ids}),
		utilRequest.WithMethodPost(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data, err
}
