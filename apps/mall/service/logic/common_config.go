package logic

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/pkg/search"
)

// GetCommonConfig 获取指定配置
func GetCommonConfig(ctx context.Context, key string) (string, error) {
	cc := repo.GetQuery().CommonConfig
	qw := search.NewWrapper().Where(cc.ConfigKey.Eq(key))
	config, err := repo.NewCommonConfigRepo(cc.WithContext(ctx)).SelectOne(qw)
	if err != nil {
		return "", errors.Wrap(err, "查询配置详情失败")
	}
	return config.Value, nil
}

// GetResaleStandardConfig 获取转卖标准配置
func GetResaleStandardConfig(ctx context.Context) (*define.ResaleStandardConfig, error) {
	config, err := GetCommonConfig(ctx, constant.ResaleStandardConfigKey)
	if err != nil {
		return nil, err
	}
	var resaleStandardConfig define.ResaleStandardConfig
	if err := json.Unmarshal([]byte(config), &resaleStandardConfig); err != nil {
		return nil, errors.Wrap(err, "解析转卖标准配置失败")
	}
	return &resaleStandardConfig, nil
}

// GetResaleInstructionConfig 获取转卖说明配置
func GetResaleInstructionConfig(ctx context.Context) (*define.ResaleInstructionConfig, error) {
	config, err := GetCommonConfig(ctx, constant.ResaleInstructionConfigKey)
	if err != nil {
		return nil, err
	}
	var resaleInstructionConfig define.ResaleInstructionConfig
	if err := json.Unmarshal([]byte(config), &resaleInstructionConfig); err != nil {
		return nil, errors.Wrap(err, "解析转卖说明配置失败")
	}
	return &resaleInstructionConfig, nil
}

// GetMallInstructionConfig 获取转卖说明配置
func GetMallInstructionConfig(ctx context.Context) (*define.MallInstructionConfig, error) {
	config, err := GetCommonConfig(ctx, constant.MallInstructionConfigKey)
	if err != nil {
		return nil, err
	}
	var mallInstructionConfig define.MallInstructionConfig
	if err := json.Unmarshal([]byte(config), &mallInstructionConfig); err != nil {
		return nil, errors.Wrap(err, "解析直购说明配置失败")
	}
	return &mallInstructionConfig, nil
}
