package main

import (
	"bytes"
	"fmt"
	"os"
	"text/template"
)

const templateCode = `package repo

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"marketplace_service/apps/{{.AppName}}/dal/model"
	"marketplace_service/apps/{{.AppName}}/dal/query"
	"marketplace_service/pkg/pagination"
	"marketplace_service/pkg/search"
)

type {{.AliasName}}Repository struct {
	do query.{{.InterfaceName}}
}

func New{{.AliasName}}Repo(do query.{{.InterfaceName}}) *{{.AliasName}}Repository {
	return &{{.AliasName}}Repository{
		do: do,
	}
}

func (r *{{.AliasName}}Repository) SelectOne(wrapper *search.Wrapper) (*model.{{.ModelName}}, error) {
	records, err := r.do.Scopes(wrapper.Build()).Find()
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	if len(records) > 1 {
		return nil, errors.New("more than one item found")
	}
	return records[0], nil
}

func (r *{{.AliasName}}Repository) SelectList(wrapper *search.Wrapper) ([]*model.{{.ModelName}}, error) {
	records, err := r.do.Scopes(wrapper.Build()).Find()
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (r *{{.AliasName}}Repository) SelectPage(wrapper *search.Wrapper, req pagination.IPagination) ([]*model.{{.ModelName}}, int64, error) {
	records, count, err := r.do.Scopes(wrapper.Build()).
		FindByPage(search.Paginate(req.GetPageSize(), req.GetPage()))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *{{.AliasName}}Repository) QuickSelectPage(req pagination.IPagination) ([]*model.{{.ModelName}}, int64, error) {
	records, count, err := r.do.Scopes(search.MakeCondition(req)).
		FindByPage(search.Paginate(req.GetPageSize(), req.GetPage()))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *{{.AliasName}}Repository) Count(wrapper *search.Wrapper) (int64, error) {
	count, err := r.do.Scopes(wrapper.Build()).Count()
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *{{.AliasName}}Repository) Save(model *model.{{.ModelName}}) error {
	err := r.do.Create(model)
	if err != nil {
		return err
	}
	return nil
}

func (r *{{.AliasName}}Repository) BatchSave(models []*model.{{.ModelName}}, batchSize int) error {
	err := r.do.CreateInBatches(models, batchSize)
	if err != nil {
		return err
	}
	return nil
}

func (r *{{.AliasName}}Repository) UpdateById(model *model.{{.ModelName}}) error {
	result, err := r.do.Updates(model)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *{{.AliasName}}Repository) Update(ms *model.{{.ModelName}}, wrapper *search.Wrapper) error {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(ms)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *{{.AliasName}}Repository) UpdateField(params interface{}, wrapper *search.Wrapper) error {
	if wrapper != nil {
		r.do = r.do.Scopes(
			wrapper.Build(),
		)
	}
	result, err := r.do.Updates(params)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *{{.AliasName}}Repository) RemoveByIds(ms ...*model.{{.ModelName}}) error {
	result, err := r.do.Delete(ms...)
	if err != nil {
		return err
	} else if result.RowsAffected != int64(len(ms)) {
		return UpdateFail
	}
	return nil
}
`

type TemplateData struct {
	AppName       string
	AliasName     string
	InterfaceName string
	ModelName     string
}

func GenRepo(appName string, aliasName string, fileName string, interfaceName string, modelName string) {
	data := TemplateData{
		AppName:       appName,
		AliasName:     aliasName,
		InterfaceName: interfaceName,
		ModelName:     modelName,
	}
	tmpl, err := template.New("code").Parse(templateCode)
	if err != nil {
		panic(err)
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		panic(err)
	}

	// Write the generated code to a file
	err = os.WriteFile(fmt.Sprintf("./apps/%s/repo/%s.gen.go", appName, fileName), buf.Bytes(), 0644)
	if err != nil {
		panic(err)
	}

	// Print the generated code to the console
	println(buf.String())
}
