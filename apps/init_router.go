package apps

import (
	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	_ "marketplace_service/apps/mall/router"
	"marketplace_service/global"
	"net/http"
)

// Check 健康检查
func Check(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, "ok")
}

// Init 初始化路由
func Init() *gin.Engine {
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.ContextWithFallback = true
	// 注册路由中间件
	router.Use(g.Cors())

	// 健康检查路由
	router.GET("/check", Check)

	// 定义swagger 的路径，首页是 /swagger/index.html
	router.GET("/swagger/marketplace_service/*any", ginSwagger.WrapHandler(swaggerFiles.Handler, ginSwagger.InstanceName("marketplace_service")))

	global.SetEngine(router)

	return router
}
