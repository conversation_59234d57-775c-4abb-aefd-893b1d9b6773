definitions:
  define.AddMallItemReq:
    properties:
      daily_limit:
        description: 每日限购数量
        minimum: 0
        type: integer
      discount:
        description: 折扣
        maximum: 100
        minimum: 1
        type: integer
      freight:
        description: 运费(分)
        minimum: 0
        type: integer
      item_id:
        description: 商品ID
        type: string
      priority:
        description: 优先级
        maximum: 9999
        minimum: 0
        type: integer
      sale_price:
        description: 售价(分)
        minimum: 1
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      start_time:
        description: 开始展示时间
        type: string
      stock:
        description: 限定库存量
        minimum: 1
        type: integer
      stock_type:
        description: 库存类型（1=限定库存，2=同步商品库存）
        type: integer
      total_limit:
        description: 总限购数量
        minimum: 0
        type: integer
    required:
    - daily_limit
    - freight
    - item_id
    - sale_price
    - sku_id
    - start_time
    - stock
    - stock_type
    - total_limit
    type: object
  define.AddMallItemResp:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
    type: object
  define.AddResaleItemReq:
    properties:
      item_id:
        description: 商品ID
        type: string
      priority:
        description: 优先级
        maximum: 9999
        minimum: 0
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
    required:
    - item_id
    - sku_id
    type: object
  define.AddResaleItemResp:
    properties:
      id:
        description: 转卖商品ID
        example: "0"
        type: string
    type: object
  define.AddResaleListingsReq:
    properties:
      item_id:
        description: 商品 id
        type: string
      quantity:
        description: 出售数量
        minimum: 1
        type: integer
      sale_price:
        description: 卖出价格（分）
        minimum: 2
        type: integer
      sell_password:
        description: 出售密码（MD5 32位纯小写）
        type: string
      user_item_id:
        description: 持仓 id/背包 id，如果传了表示单个出售
        type: string
    required:
    - item_id
    - quantity
    - sale_price
    type: object
  define.AddResaleListingsResp:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: 主键
        example: "0"
        type: string
      quantity:
        description: 出售数量
        type: integer
    type: object
  define.AdminGetOrderPrivateInfoResp:
    properties:
      consignee_phone:
        description: 收货人电话
        type: string
    type: object
  define.CancelAdminTradeOrderReq:
    properties:
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - id
    type: object
  define.CancelAdminTradeOrderResp:
    type: object
  define.CancelResaleUserItemFromWebReq:
    properties:
      id:
        type: string
    type: object
  define.CancelResaleUserItemFromWebResp:
    properties:
      canceled_at:
        type: string
      id:
        type: string
    type: object
  define.CancelTradeOrderReq:
    properties:
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - id
    type: object
  define.CancelTradeOrderResp:
    type: object
  define.Config:
    properties:
      config_key:
        description: config_key
        type: string
      id:
        description: 主键
        example: "0"
        type: string
      remark:
        description: 备注
        type: string
      value:
        description: value
    type: object
  define.ConfirmTradeOrderReq:
    properties:
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - id
    type: object
  define.ConfirmTradeOrderResp:
    type: object
  define.DelTradeOrderReq:
    properties:
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - id
    type: object
  define.DelTradeOrderResp:
    type: object
  define.EditConfigReq:
    properties:
      config_key:
        description: config_key
        type: string
      value:
        description: value
    required:
    - config_key
    - value
    type: object
  define.EditConfigResp:
    properties:
      id:
        example: "0"
        type: string
    type: object
  define.EditMallItemPriorityReq:
    properties:
      id:
        description: ID
        example: "0"
        type: string
      priority:
        description: 优先级
        maximum: 9999
        minimum: 1
        type: integer
    required:
    - id
    type: object
  define.EditMallItemPriorityResp:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
    type: object
  define.EditMallItemReq:
    properties:
      daily_limit:
        description: 每日限购数量
        minimum: 0
        type: integer
      discount:
        description: 折扣
        maximum: 100
        minimum: 1
        type: integer
      freight:
        description: 运费(分)
        minimum: 0
        type: integer
      id:
        description: 直购商品ID
        example: "0"
        type: string
      item_id:
        description: 商品ID
        type: string
      priority:
        description: 优先级
        maximum: 9999
        minimum: 0
        type: integer
      sale_price:
        description: 售价(分)
        minimum: 1
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      start_time:
        description: 开始展示时间
        type: string
      stock:
        description: 限定库存量
        minimum: 1
        type: integer
      stock_type:
        description: 库存类型（1=限定库存，2=同步商品库存）
        type: integer
      total_limit:
        description: 总限购数量
        minimum: 0
        type: integer
    required:
    - daily_limit
    - freight
    - id
    - item_id
    - sale_price
    - sku_id
    - start_time
    - stock
    - stock_type
    - total_limit
    type: object
  define.EditMallItemResp:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
    type: object
  define.EditMallItemStatusReq:
    properties:
      id:
        description: ID
        example: "0"
        type: string
      status:
        description: 状态（0=待上架，1=已上架，2=已下架）
        minimum: 0
        type: integer
    required:
    - id
    - status
    type: object
  define.EditMallItemStatusResp:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
    type: object
  define.EditResaleItemPriorityReq:
    properties:
      id:
        description: ID
        example: "0"
        type: string
      priority:
        description: 优先级
        maximum: 9999
        minimum: 1
        type: integer
    required:
    - id
    type: object
  define.EditResaleItemPriorityResp:
    properties:
      id:
        description: 转卖商品ID
        example: "0"
        type: string
    type: object
  define.EditResaleItemReq:
    properties:
      id:
        description: ID
        example: "0"
        type: string
      is_custom_price_limit:
        description: 是否使用特定限价（0-关闭, 1-开启）
        type: integer
      max_resale_ratio:
        description: 最高售价比例（基于历史最高成交价）
        type: integer
      min_resale_ratio:
        description: 最低售价百分比（基于市场公允进价）
        type: integer
      resale_status:
        description: 转卖状态（0-关闭, 1-开启）
        type: integer
      trade_frequency_type:
        description: 交易频率类型（1-同步全局标准, 2-特定交易频次）
        type: integer
      trade_interval:
        description: 交易间隔（分）
        type: integer
    required:
    - id
    - is_custom_price_limit
    - resale_status
    - trade_frequency_type
    type: object
  define.EditResaleItemResp:
    properties:
      id:
        description: 转卖商品ID
        example: "0"
        type: string
    type: object
  define.EditResaleItemStatusReq:
    properties:
      id:
        description: ID
        example: "0"
        type: string
      status:
        description: 状态（0=待上架，1=已上架，2=已下架）
        minimum: 0
        type: integer
    required:
    - id
    - status
    type: object
  define.EditResaleItemStatusResp:
    properties:
      id:
        description: 转卖商品ID
        example: "0"
        type: string
    type: object
  define.GetAdminResaleListingsDetailData:
    properties:
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      listing_quantity:
        description: 挂单数量
        type: integer
      sale_price:
        description: 售价
        type: integer
      sku_id:
        description: 商品sku_id
        type: string
      sold_quantity:
        description: 已成交数量
        type: integer
      total_fee:
        description: 手续费
        type: integer
      total_income:
        description: 卖家收入（分）
        type: integer
    type: object
  define.GetAdminResaleListingsDetailResp:
    properties:
      app_version:
        description: 版本号
        type: string
      created_at:
        description: 创建时间/上架时间
        type: string
      id:
        description: 出售单号
        example: "0"
        type: string
      list:
        description: 商品信息
        items:
          $ref: '#/definitions/define.GetAdminResaleListingsDetailData'
        type: array
      operator:
        description: 最后操作人 id
        type: string
      seller_id:
        description: 卖家 ID
        type: string
      seller_nickname:
        description: 卖家昵称
        type: string
      status:
        description: 挂单状态（0-已下架，1-出售中，2-已出售，3-已取消）
        type: integer
      terminal:
        description: 终端
        type: string
    type: object
  define.GetAdminResaleListingsListData:
    properties:
      created_at:
        description: 创建时间/上架时间
        type: string
      id:
        description: 出售单号
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图 URL
        type: string
      item_id:
        description: 商品 id
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      listing_quantity:
        description: 挂单数量
        type: integer
      sale_price:
        description: 出售单价（分）
        type: integer
      seller_id:
        description: 卖家 id
        type: string
      seller_nickname:
        description: 卖家昵称
        type: string
      seller_phone:
        description: 卖家手机号
        type: string
      sku_id:
        description: SKUID
        type: string
      sold_quantity:
        description: 成交数量
        type: integer
      status:
        description: 出售状态状态（0-已下架，1-出售中，2-已出售，3-已取消）
        type: integer
      terminal:
        description: 终端
        type: string
      total_fee:
        description: 手续费
        type: integer
      total_income:
        description: 卖家收入（分）
        type: integer
    type: object
  define.GetAdminResaleListingsListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetAdminResaleListingsListData'
        type: array
      total:
        type: integer
    type: object
  define.GetAdminResaleOrderDetailData:
    properties:
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      pay_amount:
        description: 支付金额
        type: integer
      quantity:
        description: 购买数量
        type: integer
      resale_listings_id:
        description: 出售单号
        example: "0"
        type: string
      sale_price:
        description: 售价
        type: integer
      sku_id:
        description: 商品sku_id
        type: string
      total_amount:
        description: 总金额
        type: integer
      total_fee:
        description: 手续费
        type: integer
    type: object
  define.GetAdminResaleOrderDetailResp:
    properties:
      app_version:
        description: 版本号
        type: string
      buyer_id:
        description: 购买用户ID
        type: string
      buyer_nickname:
        description: 购买用户昵称
        type: string
      created_at:
        description: 创建时间
        type: string
      finished_at:
        description: 交易完成时间
        type: string
      id:
        description: 合同编号
        example: "0"
        type: string
      item_info:
        allOf:
        - $ref: '#/definitions/define.GetAdminResaleOrderDetailData'
        description: 商品信息
      payment_at:
        description: 支付时间
        type: string
      seller_id:
        description: 出售用户ID
        type: string
      seller_nickname:
        description: 出售用户昵称
        type: string
      status:
        description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        type: integer
      terminal:
        description: 终端
        type: string
      transfer_at:
        description: 转移时间
        type: string
    type: object
  define.GetAdminResaleOrderListData:
    properties:
      buyer_id:
        description: 购买用户ID
        type: string
      buyer_nickname:
        description: 购买用户昵称
        type: string
      buyer_phone:
        description: 购买用户手机号码
        type: string
      created_at:
        description: 创建时间
        type: string
      id:
        description: 合同编号
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      pay_amount:
        description: 支付金额
        type: integer
      quantity:
        description: 购买数量
        type: integer
      resale_listings_id:
        description: 出售单号
        example: "0"
        type: string
      sale_price:
        description: 售价
        type: integer
      seller_id:
        description: 出售用户ID
        type: string
      seller_nickname:
        description: 出售用户昵称
        type: string
      seller_phone:
        description: 出售用户手机号码
        type: string
      sku_id:
        description: 商品sku_id
        type: string
      status:
        description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        type: integer
      terminal:
        description: 终端
        type: string
      total_amount:
        description: 总金额
        type: integer
      total_fee:
        description: 手续费
        type: integer
    type: object
  define.GetAdminResaleOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetAdminResaleOrderListData'
        type: array
      total:
        type: integer
    type: object
  define.GetAdminTradeOrderData:
    properties:
      created_at:
        description: 创建时间
        type: string
      freight_amount:
        description: 运费
        type: integer
      id:
        description: 订单ID
        example: "0"
        type: string
      order_items:
        description: 订单子单列表
        items:
          $ref: '#/definitions/define.GetAdminTradeOrderItemData'
        type: array
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
      pay_amount:
        description: 支付金额
        type: integer
      total_amount:
        description: 总金额
        type: integer
      user_id:
        description: 用户ID
        type: string
      user_nickname:
        description: 用户昵称
        type: string
    type: object
  define.GetAdminTradeOrderDetailFreight:
    properties:
      delivered_at:
        description: 发货时间
        type: string
      delivery_company:
        description: 快递公司
        type: string
      delivery_number:
        description: 快递单号
        type: string
      order_item_id:
        description: 订单子单ID
        example: "0"
        type: string
      records:
        description: 物流信息
        items:
          type: integer
        type: array
    type: object
  define.GetAdminTradeOrderDetailResp:
    properties:
      cancel_at:
        description: 取消时间
        type: string
      cancel_type:
        description: 取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）
        type: integer
      consignee_address:
        description: 收货地址
        items:
          type: integer
        type: array
      consignee_name:
        description: 收货人姓名
        type: string
      consignee_phone:
        description: 收货人电话
        type: string
      created_at:
        description: 下单时间
        type: string
      delivered_at:
        description: 发货时间
        type: string
      discount_amount:
        description: 折扣金额
        type: integer
      finished_at:
        description: 完成时间
        type: string
      freight_amount:
        description: 运费
        type: integer
      freight_infos:
        description: 物流信息
        items:
          $ref: '#/definitions/define.GetAdminTradeOrderDetailFreight'
        type: array
      id:
        description: 订单ID
        example: "0"
        type: string
      order_items:
        description: 物品列表
        items:
          $ref: '#/definitions/define.GetAdminTradeOrderItemData'
        type: array
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
      order_type:
        description: 订单类型（1=直购订单）
        type: integer
      pay_amount:
        description: 支付金额
        type: integer
      pay_order_id:
        description: 支付订单ID
        type: string
      payment_method:
        description: 支付方式
        type: string
      payment_status:
        description: 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
        type: integer
      payment_time:
        description: 支付时间
        type: string
      shipping_status:
        description: 发货状态（0=未发货，1=已发货，2=已签收）
        type: integer
      supply_chain_order_id:
        description: 供应链ID
        example: ""
        type: string
      terminal:
        description: 订单渠道
        type: string
      total_amount:
        description: 总金额
        type: integer
      user_id:
        description: 用户ID
        type: string
      user_nickname:
        description: 用户昵称
        type: string
      user_remark:
        description: 用户备注
        type: string
    type: object
  define.GetAdminTradeOrderItemData:
    properties:
      category_id:
        description: 商品分类id
        type: string
      category_name:
        description: 商品分类名称
        type: string
      discount_amount:
        description: 商品享受的优惠金额
        type: integer
      freight_amount:
        description: 运费
        type: integer
      id:
        description: 直购商品ID
        example: "0"
        type: string
      ip_id:
        description: 商品 ip_id
        type: string
      ip_name:
        description: 商品IP名称
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品属性
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 单价
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      trademark_id:
        description: 商品品牌id
        type: string
      trademark_name:
        description: 商品品牌名称
        type: string
    type: object
  define.GetAdminTradeOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetAdminTradeOrderData'
        type: array
      total:
        type: integer
    type: object
  define.GetConfigDetailResp:
    properties:
      config_key:
        description: config_key
        type: string
      id:
        description: 主键
        example: "0"
        type: string
      remark:
        description: 备注
        type: string
      value:
        description: value
    type: object
  define.GetConfigListResp:
    properties:
      config_list:
        items:
          $ref: '#/definitions/define.Config'
        type: array
    type: object
  define.GetTradeOrderDetailResp:
    properties:
      freight_amount:
        description: 运费金额(单位:分)
        type: integer
      pay_amount:
        description: 支付金额(单位:分)
        type: integer
      payment_status:
        description: 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
        type: integer
    type: object
  define.GetWebPublishedResaleListingsListData:
    properties:
      id:
        description: 主键
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      min_price:
        description: 最低在售
        type: integer
      sell_price:
        description: 出售单价（分）
        type: integer
      stock_quantity:
        description: 预卖出数量
        type: integer
    type: object
  define.GetWebPublishedResaleListingsListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebPublishedResaleListingsListData'
        type: array
      total:
        type: integer
    type: object
  define.GetWebResaleListingsItemOnSaleListData:
    properties:
      id:
        description: 主键
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      on_sale_qty:
        description: 在售数量
        type: integer
      sell_price:
        description: 出售单价（分）
        type: integer
      seller_avatar:
        description: 卖家头像
        type: string
      seller_id:
        description: 卖家 id
        type: string
      seller_nickname:
        description: 卖家昵称
        type: string
      seller_total_sold_qty:
        description: 卖家总共售出数量（不限当前商品）
        type: integer
    type: object
  define.GetWebResaleListingsItemOnSaleListResp:
    properties:
      list:
        description: 列表数据
        items:
          $ref: '#/definitions/define.GetWebResaleListingsItemOnSaleListData'
        type: array
      total:
        description: 列表总数
        type: integer
      total_on_sale_qty:
        description: 当前商品所有在售数量
        type: integer
    type: object
  define.GetWebResaleListingsItemSettlementListInfo:
    properties:
      on_sale_qty:
        description: 在售数量
        type: integer
      sale_price:
        description: 出售单价（分）
        type: integer
      selected_qty:
        description: 选择中数量
        type: integer
      selected_resale_listings_list:
        description: 选中的挂单列表
        items:
          $ref: '#/definitions/define.GetWebResaleListingsItemSettlementListSelectedItem'
        type: array
      seller_avatar:
        description: 卖家头像
        type: string
      seller_id:
        description: 卖家 id
        type: string
      seller_nickname:
        description: 卖家昵称
        type: string
    type: object
  define.GetWebResaleListingsItemSettlementListResp:
    properties:
      buy_max_qty:
        description: 购买最大数量限制
        type: integer
      list:
        description: 列表数据
        items:
          $ref: '#/definitions/define.GetWebResaleListingsItemSettlementListInfo'
        type: array
      total_amount:
        description: 合计价格（分）
        type: integer
    type: object
  define.GetWebResaleListingsItemSettlementListSelectedItem:
    properties:
      resale_listings_id:
        description: 转卖挂单主单 id
        example: "0"
        type: string
      resale_listings_item_ids:
        description: 挂单物品 id 列表
        items:
          type: string
        type: array
    type: object
  define.GetWebResaleOrderBuyDetailData:
    properties:
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      pay_amount:
        description: 支付金额
        type: integer
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 售价
        type: integer
      sold_out:
        description: 商品转卖和直购都下架状态 0=正常，1=下架
        type: integer
      total_amount:
        description: 总金额
        type: integer
    type: object
  define.GetWebResaleOrderBuyDetailResp:
    properties:
      buyer_id:
        description: 购买用户ID
        type: string
      buyer_nickname:
        description: 购买用户昵称
        type: string
      buyer_real_name:
        description: 购买用户真实名称
        type: string
      created_at:
        description: 创建时间
        type: string
      finished_at:
        description: 交易完成时间
        type: string
      id:
        description: 合同编号
        example: "0"
        type: string
      item_info:
        allOf:
        - $ref: '#/definitions/define.GetWebResaleOrderBuyDetailData'
        description: 商品信息
      item_name:
        description: 商品名称
        type: string
      payment_at:
        description: 支付时间
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 售价
        type: integer
      seller_id:
        description: 出售用户ID
        type: string
      seller_nickname:
        description: 出售用户昵称
        type: string
      seller_real_name:
        description: 出售用户真实名称
        type: string
      status:
        description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        type: integer
      total_amount:
        description: 总金额
        type: integer
      total_fee:
        description: 手续费
        type: integer
    type: object
  define.GetWebResaleOrderBuyListData:
    properties:
      id:
        description: 主键
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 售价
        type: integer
      seller_avatar:
        description: 出售用户头像
        type: string
      seller_nickname:
        description: 出售用户昵称
        type: string
      sold_out:
        description: 商品转卖和直购都下架状态 0=正常，1=下架
        type: integer
      status:
        description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        type: integer
      total_amount:
        description: 总金额
        type: integer
    type: object
  define.GetWebResaleOrderBuyListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebResaleOrderBuyListData'
        type: array
      total:
        type: integer
    type: object
  define.GetWebResaleOrderRecentListData:
    properties:
      buyer_avatar:
        description: 购买用户头像
        type: string
      buyer_nickname:
        description: 购买用户昵称
        type: string
      created_at:
        description: 创建时间
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 售价
        type: integer
    type: object
  define.GetWebResaleOrderRecentListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebResaleOrderRecentListData'
        type: array
      total:
        type: integer
    type: object
  define.GetWebResaleOrderSaleDetailData:
    properties:
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 售价
        type: integer
      seller_income_amount:
        description: 卖方收入
        type: integer
      sold_out:
        description: 下架状态 0=正常，1=下架
        type: integer
      total_amount:
        description: 总金额
        type: integer
    type: object
  define.GetWebResaleOrderSaleDetailResp:
    properties:
      buyer_id:
        description: 购买用户ID
        type: string
      buyer_nickname:
        description: 购买用户昵称
        type: string
      buyer_real_name:
        description: 购买用户真实名称
        type: string
      created_at:
        description: 创建时间
        type: string
      finished_at:
        description: 交易完成时间
        type: string
      id:
        description: 合同编号
        example: "0"
        type: string
      item_info:
        allOf:
        - $ref: '#/definitions/define.GetWebResaleOrderSaleDetailData'
        description: 商品信息
      item_name:
        description: 商品名称
        type: string
      payment_at:
        description: 支付时间
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 售价
        type: integer
      seller_id:
        description: 出售用户ID
        type: string
      seller_income_amount:
        description: 卖方收入
        type: integer
      seller_nickname:
        description: 出售用户昵称
        type: string
      seller_real_name:
        description: 出售用户真实名称
        type: string
      status:
        description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        type: integer
      total_amount:
        description: 总金额
        type: integer
      total_fee:
        description: 手续费
        type: integer
    type: object
  define.GetWebResaleOrderSaleListData:
    properties:
      buyer_avatar:
        description: 购买用户头像
        type: string
      buyer_nickname:
        description: 购买用户昵称
        type: string
      id:
        description: 主键
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 售价
        type: integer
      sold_out:
        description: 商品转卖和直购都下架状态 0=正常，1=下架
        type: integer
      status:
        description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        type: integer
      total_amount:
        description: 总金额
        type: integer
    type: object
  define.GetWebResaleOrderSaleListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebResaleOrderSaleListData'
        type: array
      total:
        type: integer
    type: object
  define.GetWebResaleUserItemListByItemData:
    properties:
      hold_qty:
        description: 持有数量
        type: integer
      id:
        description: 主键
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品 id
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      resale_status:
        description: 转卖状态（0-关闭, 1-开启）
        type: integer
      sold_out:
        description: 商品转卖和直购都下架状态 0=正常，1=下架
        type: integer
    type: object
  define.GetWebResaleUserItemListByItemResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebResaleUserItemListByItemData'
        type: array
      total:
        type: integer
    type: object
  define.GetWebResaleUserItemListByUserItemData:
    properties:
      buy_price:
        description: 买入价格（分），为 0 则展示：外部存入
        type: integer
      buy_time:
        description: 买入时间/物品获得时间
        type: string
      countdown_second:
        description: 倒计时秒数
        type: integer
      delivery_time:
        description: 提货时间
        type: string
      id:
        description: 持仓 id
        type: string
      interval_minutes:
        description: 转卖时间间隔（单位：分钟）
        type: integer
      item_icon_url:
        description: 商品图片
        type: string
      item_id:
        description: 商品 id
        type: string
      pre_sale:
        description: 是否预售
        type: boolean
      resale_listings_item_id:
        description: 转卖挂单商品 id
        example: "0"
        type: string
      resale_status:
        description: 转卖状态（0-关闭, 1-开启）
        type: integer
      sale_price:
        description: 出售单价（分），状态为出售中时展示
        type: integer
      status:
        description: 状态，1：持有中，100：出售中
        type: integer
    type: object
  define.GetWebResaleUserItemListByUserItemResp:
    properties:
      has_more:
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.GetWebResaleUserItemListByUserItemData'
        type: array
    type: object
  define.GetWebResaleUserItemTradeInfoResp:
    properties:
      available_qty:
        description: 当前用户可出售的物品数量
        type: integer
      fee_rate:
        description: 服务费百分比，比如 3% 就返回 3
        type: integer
      item_icon_url:
        description: 商品图片
        type: string
      item_id:
        description: 商品 id
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      latest_sale_price:
        description: 最新成交价（分），为 -1 表示暂无挂单
        type: integer
      max_limit_price:
        description: 最高限价（分）
        type: integer
      min_limit_price:
        description: 最低限价（分）
        type: integer
      min_sale_price:
        description: 最低在售价（分），为 -1 表示暂无挂单
        type: integer
      sale_max_qty:
        description: 出售最大数量限制
        type: integer
    type: object
  define.GetWebTradeOrderData:
    properties:
      countdown_second:
        description: 倒计时秒数
        type: integer
      freight_info:
        description: 物流信息
        items:
          $ref: '#/definitions/define.GetWebTradeOrderDetailFreight'
        type: array
      id:
        description: id
        example: "0"
        type: string
      order_items:
        description: 物品列表
        items:
          $ref: '#/definitions/define.GetWebTradeOrderItemData'
        type: array
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
      pay_amount:
        description: 支付金额
        type: integer
      payment_status:
        description: 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
        type: integer
      shipping_status:
        description: 发货状态（0=未发货，1=已发货，2=已签收）
        type: integer
      total_amount:
        description: 总金额
        type: integer
    type: object
  define.GetWebTradeOrderDetailFreight:
    properties:
      delivery_company:
        description: 快递公司
        type: string
      delivery_number:
        description: 快递单号
        type: string
    type: object
  define.GetWebTradeOrderDetailResp:
    properties:
      cancel_type:
        description: 取消类型（1=主动取消，2=系统超时取消，3=文潮管理端取消，4=云仓管理端取消）
        type: integer
      consignee_address:
        description: 收货地址
        items:
          type: integer
        type: array
      consignee_name:
        description: 收货人姓名
        type: string
      consignee_phone:
        description: 收货人电话
        type: string
      countdown_second:
        description: 倒计时秒数
        type: integer
      created_at:
        description: 下单时间
        type: string
      discount_amount:
        description: 折扣金额
        type: integer
      finished_second:
        description: 订单完成倒计时秒数
        type: integer
      freight_amount:
        description: 运费
        type: integer
      freight_info:
        description: 物流信息
        items:
          $ref: '#/definitions/define.GetWebTradeOrderDetailFreight'
        type: array
      id:
        description: id
        example: "0"
        type: string
      order_items:
        description: 物品列表
        items:
          $ref: '#/definitions/define.GetWebTradeOrderItemData'
        type: array
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
      pay_amount:
        description: 支付金额
        type: integer
      payment_method:
        description: 支付方式
        type: string
      payment_status:
        description: 支付状态（0=未支付，1=已支付，2=退款，3=全额退款）
        type: integer
      payment_time:
        description: 支付时间
        type: string
      shipping_status:
        description: 发货状态（0=未发货，1=已发货，2=已签收）
        type: integer
      total_amount:
        description: 总金额
        type: integer
      user_remark:
        description: 用户备注
        type: string
    type: object
  define.GetWebTradeOrderFreightInfoResp:
    properties:
      checked:
        description: 0未签收 1已签收
        type: integer
      delivery_company:
        description: 快递公司
        type: string
      delivery_number:
        description: 快递单号
        type: string
      records:
        items:
          type: integer
        type: array
      status:
        type: integer
    type: object
  define.GetWebTradeOrderItemData:
    properties:
      id:
        description: 直购商品ID
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品属性
        type: string
      quantity:
        description: 购买数量
        type: integer
      sale_price:
        description: 单价
        type: integer
    type: object
  define.GetWebTradeOrderListResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebTradeOrderData'
        type: array
      total:
        type: integer
    type: object
  define.GetWebTradeOrderStatusStatData:
    properties:
      count:
        type: integer
      order_status:
        description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        type: integer
    type: object
  define.GetWebTradeOrderStatusStatResp:
    properties:
      list:
        items:
          $ref: '#/definitions/define.GetWebTradeOrderStatusStatData'
        type: array
    type: object
  define.LockedHandlerReq:
    properties:
      id:
        description: id
        example: "0"
        type: string
    type: object
  define.LockedHandlerResp:
    type: object
  define.MallInstruction:
    properties:
      banner_url:
        type: string
      content:
        type: string
    type: object
  define.MallItemDetailResp:
    properties:
      available_stock:
        description: 直购商品可用库存
        type: integer
      category_id:
        description: 商品分类id
        type: string
      created_at:
        description: 创建时间
        type: string
      daily_limit:
        description: 每日限购数量
        type: integer
      discount:
        description: 折扣
        type: integer
      freight:
        description: 运费(分)
        type: integer
      id:
        description: 主键
        example: "0"
        type: string
      ipid:
        description: 商品 ip_id
        type: string
      item_id:
        description: 商品 id
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      priority:
        description: 优先级
        type: integer
      purchase_price:
        description: 进价
        type: integer
      sale_price:
        description: 售价(分)
        type: integer
      sell_listings:
        description: 商品库存
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      spu_id:
        description: 商品 spu_id
        type: string
      start_time:
        description: 开始展示时间
        type: string
      status:
        description: 状态（0=待上架，1=已上架，2=已下架）
        type: integer
      stock:
        description: 限定库存量
        type: integer
      stock_type:
        description: 库存类型（1=限定库存，2=同步商品库存）
        type: integer
      total_limit:
        description: 总限购数量
        type: integer
      trademark_id:
        description: 商品品牌id
        type: string
    type: object
  define.MallItemPageData:
    properties:
      available_stock:
        description: 剩余库存
        type: integer
      category_id:
        description: 商品分类ID
        type: string
      category_name:
        description: 商品分类名称
        type: string
      discount:
        description: 折扣
        type: integer
      discount_price:
        description: 折扣价
        type: integer
      freight:
        description: 运费
        type: integer
      id:
        description: 主键
        example: "0"
        type: string
      ip_id:
        description: IP ID
        type: string
      ip_name:
        description: IP名称
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品 ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 规格
        type: string
      priority:
        description: 优先级
        type: integer
      purchase_price:
        description: 进价
        type: integer
      sale_price:
        description: 售价
        type: integer
      sku_id:
        description: SKU ID
        type: string
      spu_id:
        description: SPU ID
        type: string
      start_time:
        description: 开始展示时间
        type: string
      status:
        description: 状态（0=待上架，1=已上架，2=已下架）
        type: integer
      stock:
        description: 销售库存
        type: integer
      trademark_id:
        description: 商品品牌ID
        type: string
      trademark_name:
        description: 商品品牌名称
        type: string
      updated_by:
        description: 最后操作人
        type: string
    type: object
  define.MallItemPageResp:
    properties:
      list:
        description: 列表
        items:
          $ref: '#/definitions/define.MallItemPageData'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  define.OpenSyncViewCountToDBReq:
    type: object
  define.OpenSyncViewCountToDBResp:
    type: object
  define.OpenTriggerDisplayByStartTimeReq:
    type: object
  define.OpenUpdateMallItemNotifyReq:
    properties:
      item_ids:
        items:
          type: string
        type: array
    required:
    - item_ids
    type: object
  define.RefreshHomeFeedReq:
    properties:
      item_ids:
        items:
          type: string
        type: array
    required:
    - item_ids
    type: object
  define.RefreshHomeFeedResp:
    type: object
  define.ReleaseUserItemsReq:
    properties:
      id:
        description: id
        example: "0"
        type: string
    type: object
  define.ReleaseUserItemsResp:
    type: object
  define.ResaleInstruction:
    properties:
      banner_url:
        type: string
      content:
        type: string
    type: object
  define.ResaleItemBuyReq:
    properties:
      password:
        description: 密码
        type: string
      quantity:
        description: 购买数量
        minimum: 1
        type: integer
      resale_listings_item_info:
        description: 挂单列表信息
        items:
          $ref: '#/definitions/define.ResaleListingsItemInfo'
        type: array
      total_amount:
        description: 总金额
        minimum: 1
        type: integer
    required:
    - password
    - quantity
    - resale_listings_item_info
    - total_amount
    type: object
  define.ResaleItemBuyResp:
    properties:
      pay_success_order_ids:
        items:
          type: integer
        type: array
    type: object
  define.ResaleItemDetailResp:
    properties:
      category_id:
        description: 商品分类ID
        type: string
      category_name:
        description: 商品分类名称
        type: string
      detail_h5:
        description: 商品详情
        type: string
      id:
        description: 转卖商品ID
        example: "0"
        type: string
      image_infos:
        description: 商品详情图片
        items:
          type: string
        type: array
      ip_id:
        description: IP ID
        type: string
      ip_name:
        description: IP名称
        type: string
      is_custom_price_limit:
        description: 是否使用特定限价（0-关闭, 1-开启）
        type: integer
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品 ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 规格
        type: string
      market_price:
        description: 市场公允价（分）
        type: integer
      max_resale_ratio:
        description: 最高售价比例（基于历史最高成交价）
        type: integer
      min_resale_ratio:
        description: 最低售价百分比（基于市场公允进价）
        type: integer
      resale_status:
        description: 转卖状态（0-关闭, 1-开启）
        type: integer
      sell_listings:
        description: 商品库存
        type: integer
      sku_id:
        description: SKU ID
        type: string
      trade_frequency_type:
        description: 交易频率类型（1-同步全局标准, 2-特定交易频次）
        type: integer
      trade_interval:
        description: 交易间隔（分）
        type: integer
      trademark_id:
        description: 商品品牌ID
        type: string
      trademark_name:
        description: 商品品牌名称
        type: string
    type: object
  define.ResaleItemPageData:
    properties:
      category_id:
        description: 商品分类ID
        type: string
      category_name:
        description: 商品分类名称
        type: string
      id:
        description: 主键
        example: "0"
        type: string
      ip_id:
        description: 商品IP ID
        type: string
      ip_name:
        description: 商品IP名称
        type: string
      is_custom_price_limit:
        description: 是否使用特定限价（0-关闭, 1-开启）
        type: integer
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品 ID
        type: string
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      market_price:
        description: 市场公允价（分）
        type: integer
      max_limit_price:
        description: 最高限价（分）
        type: integer
      min_limit_price:
        description: 最低限价（分）
        type: integer
      priority:
        description: 优先级
        type: integer
      resale_status:
        description: 转卖状态（0-关闭, 1-开启）
        type: integer
      sku_id:
        description: 商品 sku_id
        type: string
      status:
        description: 状态（0=待上架，1=已上架，2=已下架）
        type: integer
      trade_frequency_type:
        description: 交易频次类型（1-同步全局标准, 2-特定交易频次）
        type: integer
      trademark_id:
        description: 商品品牌ID
        type: string
      trademark_name:
        description: 商品品牌名称
        type: string
      updated_at:
        description: 最后操作时间
        type: string
      updated_by:
        description: 最后操作人
        type: string
    type: object
  define.ResaleItemPageResp:
    properties:
      list:
        description: 列表
        items:
          $ref: '#/definitions/define.ResaleItemPageData'
        type: array
      total:
        description: 总数
        type: integer
    type: object
  define.ResaleItemTransferHandlerReq:
    properties:
      id:
        description: id
        example: "0"
        type: string
    type: object
  define.ResaleItemTransferHandlerResp:
    type: object
  define.ResaleListingsItemInfo:
    properties:
      resale_listings_id:
        description: 转卖挂单id列表
        type: string
      resale_listings_item_ids:
        description: 转卖挂单物品id列表
        items:
          type: string
        type: array
    type: object
  define.SupplyChainNoticeReq:
    properties:
      data:
        items:
          additionalProperties: true
          type: object
        type: array
      type:
        type: integer
    required:
    - type
    type: object
  define.SupplyChainNoticeResp:
    properties:
      code:
        type: integer
      data:
        properties:
          failed:
            description: 失败的消息ID（请求参数中的"mid"）
            items:
              type: integer
            type: array
        type: object
      desc:
        type: string
    type: object
  define.SyncAdminTradeOrderFreightReq:
    properties:
      id:
        description: 订单详情ID
        example: "0"
        type: string
    required:
    - id
    type: object
  define.SyncAdminTradeOrderFreightResp:
    type: object
  define.TakeDownResaleListingsFromWebReq:
    properties:
      id:
        description: 主键
        example: "0"
        type: string
    required:
    - id
    type: object
  define.TakeDownResaleListingsFromWebResp:
    properties:
      down_time:
        description: 下架时间
        type: string
      id:
        description: 主键
        example: "0"
        type: string
    type: object
  define.TradeOrderFailedRetryUploadToSupplyReq:
    type: object
  define.TradeOrderFinishReq:
    type: object
  define.TradeOrderFinishResp:
    type: object
  define.TradeOrderPaySuccessReq:
    properties:
      id:
        example: "0"
        type: string
      pay_method:
        type: string
      pay_time:
        type: string
      recharge_order_id:
        type: string
    required:
    - id
    - recharge_order_id
    type: object
  define.TradeOrderPaySuccessResp:
    type: object
  define.TradeOrderRefundReq:
    properties:
      amount:
        type: integer
      recharge_order_id:
        type: string
    required:
    - amount
    - recharge_order_id
    type: object
  define.TradeOrderRefundResp:
    properties:
      amount:
        type: integer
    type: object
  define.TradeOrderTimeoutCloseReq:
    type: object
  define.UpdateResaleListingsStatusResp:
    properties:
      id:
        example: "0"
        type: string
      status:
        type: integer
      updated_at:
        type: string
    type: object
  define.UpdateTradeOrderAddressReq:
    properties:
      address_id:
        description: 地址ID
        type: string
      id:
        description: 订单id
        example: "0"
        type: string
    required:
    - address_id
    - id
    type: object
  define.UpdateTradeOrderAddressResp:
    type: object
  define.WebAddHomeFeedViewCountReq:
    properties:
      item_id:
        description: 商品ID
        type: string
    required:
    - item_id
    type: object
  define.WebAddHomeFeedViewCountResp:
    type: object
  define.WebBuyReq:
    properties:
      address_id:
        description: 地址ID
        type: string
      mall_item_id:
        description: 直购商品ID
        example: "0"
        type: string
      pay_amount:
        description: 支付金额
        type: integer
      quantity:
        description: 购买数量
        type: integer
      remark:
        description: 备注
        type: string
    required:
    - address_id
    - mall_item_id
    - pay_amount
    - quantity
    type: object
  define.WebBuyResp:
    properties:
      created_at:
        description: 创建时间
        type: string
      is_new_order:
        description: 是否是新订单
        type: boolean
      order_id:
        description: 直购订单ID
        example: "0"
        type: string
      pay_amount:
        description: 应支付金额
        type: integer
    type: object
  define.WebGetHomeFeedDetailResp:
    properties:
      detail:
        description: 商品详情
        type: string
      id:
        description: 首页主键
        example: "0"
        type: string
      image_infos:
        description: 商品图片
        items:
          type: string
        type: array
      ip_icon_url:
        description: 商品IP图片
        type: string
      ip_id:
        description: 商品 ip_id
        type: string
      ip_name:
        description: 商品IP名称
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_mall_status:
        description: 商品直购状态
        type: integer
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      resale_count:
        description: 转卖数量
        type: integer
      resale_instruction:
        allOf:
        - $ref: '#/definitions/define.ResaleInstruction'
        description: 转卖说明配置
      resale_status:
        description: 商品转卖状态
        type: integer
      sale_price:
        description: 出售价格
        type: integer
      sales_volume:
        description: 销量
        type: integer
      status:
        description: 状态
        type: integer
      trademark_id:
        description: 商品品牌id
        type: string
      trademark_name:
        description: 商品品牌名称
        type: string
      user_wishlist_status:
        description: 用户想要状态
        type: integer
      view_count:
        description: 浏览数量
        type: integer
      wish_count:
        description: 想要数量
        type: integer
    type: object
  define.WebGetHomeFeedListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.WebHomeFeedListData'
        type: array
    type: object
  define.WebGetMallItemDetailResp:
    properties:
      available_stock:
        description: 剩余库存
        type: integer
      category_id:
        description: 商品分类id
        type: string
      category_name:
        description: 商品分类名称
        type: string
      detail:
        description: 商品详情
        type: string
      discount:
        description: 折扣
        type: integer
      discount_price:
        description: 折扣价
        type: integer
      freight:
        description: 运费
        type: integer
      id:
        description: 直购商品ID
        example: "0"
        type: string
      image_infos:
        description: 商品图片
        items:
          type: string
        type: array
      ip_id:
        description: 商品 ip_id
        type: string
      ip_name:
        description: 商品IP名称
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      mall_instruction:
        allOf:
        - $ref: '#/definitions/define.MallInstruction'
        description: 直购说明配置
      sale_price:
        description: 售价
        type: integer
      specs:
        description: 商品规格
        type: string
      status:
        description: 直购商品状态（0=待上架，1=已上架，2=已下架）
        type: integer
      stock:
        description: 总库存
        type: integer
      trademark_id:
        description: 商品品牌id
        type: string
      trademark_name:
        description: 商品品牌名称
        type: string
    type: object
  define.WebGetMallItemListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.WebMallItemListData'
        type: array
    type: object
  define.WebHomeFeedListData:
    properties:
      id:
        description: 首页主键
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_mall_status:
        description: 商品直购状态
        type: integer
      item_name:
        description: 商品名称
        type: string
      item_specs:
        description: 商品规格
        type: string
      original_price:
        description: 闪购原价
        type: integer
      priority:
        description: 优先级
        type: integer
      resale_count:
        description: 转卖数量
        type: integer
      resale_status:
        description: 商品转卖状态
        type: integer
      sale_price:
        description: 售价
        type: integer
      sales_volume:
        description: 销量
        type: integer
      status:
        description: 状态
        type: integer
      wish_count:
        description: 想要数量
        type: integer
    type: object
  define.WebMallItemListData:
    properties:
      discount:
        description: 折扣
        type: integer
      discount_price:
        description: 折扣价
        type: integer
      id:
        description: 直购商品ID
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_name:
        description: 商品名称
        type: string
      priority:
        description: 优先级
        type: integer
      sale_price:
        description: 售价
        type: integer
      sales:
        description: 销量
        type: integer
      wishlist_count:
        description: 想要数量
        type: integer
    type: object
  define.WebWishlistListData:
    properties:
      home_feed_id:
        description: 首页ID
        type: integer
      home_feed_status:
        description: 首页状态
        type: integer
      id:
        description: 用户想要ID
        example: "0"
        type: string
      item_icon_url:
        description: 商品主图
        type: string
      item_id:
        description: 商品ID
        type: string
      item_mall_status:
        description: 商品直购状态
        type: integer
      item_name:
        description: 商品名称
        type: string
      item_resale_status:
        description: 商品转卖状态
        type: integer
      item_specs:
        description: 商品规格
        type: string
      original_price:
        description: 闪购原价
        type: integer
      priority:
        description: 优先级
        type: integer
      resale_count:
        description: 转卖数量
        type: integer
      sale_price:
        description: 售价
        type: integer
      sales_volume:
        description: 销量
        type: integer
      wish_count:
        description: 想要数量
        type: integer
    type: object
  define.WebWishlistListResp:
    properties:
      has_more:
        description: 判断当前页是否为最后一页
        type: boolean
      list:
        items:
          $ref: '#/definitions/define.WebWishlistListData'
        type: array
    type: object
  define.WebWishlistReq:
    properties:
      item_id:
        type: string
    required:
    - item_id
    type: object
  response.Data:
    properties:
      code:
        type: integer
      data: {}
      desc:
        type: string
      trace_id:
        description: 链路id
        type: string
    type: object
info:
  contact: {}
  description: 接口文档
  title: app API
  version: 1.0.0
paths:
  /admin/v1/common_config/detail:
    get:
      description: 获取配置详情
      parameters:
      - description: config_key
        in: query
        name: config_key
        type: string
      - description: 主键
        example: "0"
        in: query
        name: id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetConfigDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取配置详情
      tags:
      - 管理端-配置管理
      x-apifox-folder: 管理端/配置管理
  /admin/v1/common_config/edit:
    post:
      description: 编辑配置
      parameters:
      - description: 编辑参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditConfigReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditConfigResp'
              type: object
      security:
      - Bearer: []
      summary: 编辑配置
      tags:
      - 管理端-配置管理
      x-apifox-folder: 管理端/配置管理
  /admin/v1/common_config/list:
    get:
      description: 获取配置列表
      parameters:
      - description: 逗号分隔
        in: query
        name: config_keys
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetConfigListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取配置列表
      tags:
      - 管理端-配置管理
      x-apifox-folder: 管理端/配置管理
  /admin/v1/home_feed/refresh:
    post:
      description: 管理端刷新首页商品
      parameters:
      - description: 商品信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.RefreshHomeFeedReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.RefreshHomeFeedResp'
              type: object
      security:
      - Bearer: []
      summary: 刷新首页商品
      tags:
      - 管理端-首页管理
      x-apifox-folder: 管理端/首页管理
  /admin/v1/mall_item/add:
    post:
      description: 管理端创建新商品
      parameters:
      - description: 商品信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddMallItemReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddMallItemResp'
              type: object
      security:
      - Bearer: []
      summary: 创建商品
      tags:
      - 管理端-直购商品管理
      x-apifox-folder: 管理端/直购商品管理
  /admin/v1/mall_item/detail:
    get:
      description: 管理端获取商品详细信息
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.MallItemDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取商品详情
      tags:
      - 管理端-直购商品管理
      x-apifox-folder: 管理端/直购商品管理
  /admin/v1/mall_item/edit:
    post:
      description: 管理端更新商品信息
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditMallItemReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditMallItemResp'
              type: object
      security:
      - Bearer: []
      summary: 更新商品
      tags:
      - 管理端-直购商品管理
      x-apifox-folder: 管理端/直购商品管理
  /admin/v1/mall_item/edit_priority:
    post:
      description: 管理端更新商品优先级
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditMallItemPriorityReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditMallItemPriorityResp'
              type: object
      security:
      - Bearer: []
      summary: 更新商品优先级
      tags:
      - 管理端-直购商品管理
      x-apifox-folder: 管理端/直购商品管理
  /admin/v1/mall_item/edit_status:
    post:
      description: 管理端更新商品信息
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditMallItemStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditMallItemStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 更新商品状态
      tags:
      - 管理端-直购商品管理
      x-apifox-folder: 管理端/直购商品管理
  /admin/v1/mall_item/list:
    get:
      description: 管理端获取商品分页列表
      parameters:
      - collectionFormat: csv
        description: 分类
        in: query
        items:
          type: string
        name: category_ids
        type: array
      - description: 直购商品ID
        example: ""
        in: query
        name: id
        type: string
      - collectionFormat: csv
        description: ip
        in: query
        items:
          type: string
        name: ip_ids
        type: array
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 出售价格
        in: query
        name: sale_price_gte
        type: integer
      - description: 出售价格
        in: query
        name: sale_price_lte
        type: integer
      - description: sku_id
        in: query
        name: sku_id
        type: string
      - description: 状态
        in: query
        name: status
        type: integer
      - collectionFormat: csv
        description: 品牌
        in: query
        items:
          type: string
        name: trademark_ids
        type: array
      - description: 操作人
        in: query
        name: updated_by
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.MallItemPageResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取商品列表
      tags:
      - 管理端-直购商品管理
      x-apifox-folder: 管理端/直购商品管理
  /admin/v1/resale_item/add:
    post:
      description: 管理端创建转卖商品
      parameters:
      - description: 商品信息
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddResaleItemReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddResaleItemResp'
              type: object
      security:
      - Bearer: []
      summary: 创建转卖商品
      tags:
      - 管理端-转卖商品管理
      x-apifox-folder: 管理端/转卖商品管理
  /admin/v1/resale_item/detail:
    get:
      description: 管理端获取转卖商品详细信息
      parameters:
      - description: 转卖商品ID
        example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ResaleItemDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取转卖商品详情
      tags:
      - 管理端-转卖商品管理
      x-apifox-folder: 管理端/转卖商品管理
  /admin/v1/resale_item/edit:
    post:
      description: 管理端更新商品信息
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditResaleItemReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditResaleItemResp'
              type: object
      security:
      - Bearer: []
      summary: 更新转卖商品
      tags:
      - 管理端-转卖商品管理
      x-apifox-folder: 管理端/转卖商品管理
  /admin/v1/resale_item/edit_priority:
    post:
      description: 管理端更新转卖商品优先级
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditResaleItemPriorityReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditResaleItemPriorityResp'
              type: object
      security:
      - Bearer: []
      summary: 更新转卖商品优先级
      tags:
      - 管理端-转卖商品管理
      x-apifox-folder: 管理端/转卖商品管理
  /admin/v1/resale_item/edit_status:
    post:
      description: 管理端更新转卖商品状态
      parameters:
      - description: 更新参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.EditResaleItemStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.EditResaleItemStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 更新转卖商品状态
      tags:
      - 管理端-转卖商品管理
      x-apifox-folder: 管理端/转卖商品管理
  /admin/v1/resale_item/list:
    get:
      description: 管理端获取转卖商品分页列表
      parameters:
      - collectionFormat: csv
        description: 商品分类
        in: query
        items:
          type: string
        name: category_ids
        type: array
      - description: 转卖商品ID
        example: ""
        in: query
        name: id
        type: string
      - collectionFormat: csv
        description: 商品ip
        in: query
        items:
          type: string
        name: ip_ids
        type: array
      - description: 是否使用特定限价（0-关闭, 1-开启）
        in: query
        name: is_custom_price_limit
        type: integer
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 转卖状态（0=关闭，1=开启）
        in: query
        name: resale_status
        type: integer
      - description: 商品 sku_id
        in: query
        name: sku_id
        type: string
      - description: 状态（0=待上架，1=已上架，2=已下架）
        in: query
        name: status
        type: integer
      - collectionFormat: csv
        description: 商品品牌
        in: query
        items:
          type: string
        name: trademark_ids
        type: array
      - description: 操作人
        in: query
        name: updated_by
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.ResaleItemPageResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取转卖商品列表
      tags:
      - 管理端-转卖商品管理
      x-apifox-folder: 管理端/转卖商品管理
  /admin/v1/resale_listings/detail:
    get:
      description: 管理端获取转卖挂单详细信息
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAdminResaleListingsDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取转卖挂单详情
      tags:
      - 管理端-转卖挂单管理
      x-apifox-folder: 管理端/转卖挂单管理
  /admin/v1/resale_listings/export:
    get:
      description: 导出转卖挂单列表，下载文件
      parameters:
      - description: 创建时间开始
        in: query
        name: created_at_gte
        type: string
      - description: 创建时间结束
        in: query
        name: created_at_lte
        type: string
      - description: 出售单号
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 卖家 id
        in: query
        name: seller_id
        type: string
      - description: 卖家手机号
        in: query
        name: seller_phone
        type: string
      - description: 商品sku_id
        in: query
        name: sku_id
        type: string
      - description: 挂单状态（0-已下架，1-出售中，2-已出售，3-已取消）
        in: query
        name: status
        type: integer
      - description: 终端
        in: query
        name: terminal
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAdminResaleListingsListResp'
              type: object
      security:
      - Bearer: []
      summary: 导出转卖挂单列表
      tags:
      - 管理端-转卖挂单管理
      x-apifox-folder: 管理端/转卖挂单管理
  /admin/v1/resale_listings/list:
    get:
      description: 管理端获取转卖挂单分页列表
      parameters:
      - description: 创建时间开始
        in: query
        name: created_at_gte
        type: string
      - description: 创建时间结束
        in: query
        name: created_at_lte
        type: string
      - description: 出售单号
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 卖家 id
        in: query
        name: seller_id
        type: string
      - description: 卖家手机号
        in: query
        name: seller_phone
        type: string
      - description: 商品sku_id
        in: query
        name: sku_id
        type: string
      - description: 挂单状态（0-已下架，1-出售中，2-已出售，3-已取消）
        in: query
        name: status
        type: integer
      - description: 终端
        in: query
        name: terminal
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetAdminResaleListingsListResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取转卖挂单列表
      tags:
      - 管理端-转卖挂单管理
      x-apifox-folder: 管理端/转卖挂单管理
  /admin/v1/resale_listings/update_status:
    post:
      description: 管理端更新转卖挂单状态，比如下架
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      - in: query
        name: status
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.UpdateResaleListingsStatusResp'
              type: object
      security:
      - Bearer: []
      summary: 更新转卖挂单状态
      tags:
      - 管理端-转卖挂单管理
      x-apifox-folder: 管理端/转卖挂单管理
  /admin/v1/resale_order/detail:
    get:
      description: 管理端获取转卖订单详细信息
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAdminResaleOrderDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取转卖订单详情
      tags:
      - 管理端-转卖订单管理
      x-apifox-folder: 管理端/转卖订单管理
  /admin/v1/resale_order/export:
    get:
      description: 导出转卖订单列表，下载文件
      parameters:
      - description: 购买用户ID
        in: query
        name: buyer_id
        type: string
      - description: 购买用户手机号码
        in: query
        name: buyer_phone
        type: string
      - description: 创建时间开始
        in: query
        name: created_at_gte
        type: string
      - description: 创建时间结束
        in: query
        name: created_at_lte
        type: string
      - description: 合同编号
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 出售单号
        example: "0"
        in: query
        name: resale_listings_id
        type: string
      - description: 出售用户ID
        in: query
        name: seller_id
        type: string
      - description: 出售用户手机号码
        in: query
        name: seller_phone
        type: string
      - description: 商品sku_id
        in: query
        name: sku_id
        type: string
      - description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        in: query
        name: status
        type: integer
      - description: 终端
        in: query
        name: terminal
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAdminResaleOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 导出转卖订单列表
      tags:
      - 管理端-转卖订单管理
      x-apifox-folder: 管理端/转卖订单管理
  /admin/v1/resale_order/list:
    get:
      description: 管理端获取转卖订单分页列表
      parameters:
      - description: 购买用户ID
        in: query
        name: buyer_id
        type: string
      - description: 购买用户手机号码
        in: query
        name: buyer_phone
        type: string
      - description: 创建时间开始
        in: query
        name: created_at_gte
        type: string
      - description: 创建时间结束
        in: query
        name: created_at_lte
        type: string
      - description: 合同编号
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 出售单号
        example: "0"
        in: query
        name: resale_listings_id
        type: string
      - description: 出售用户ID
        in: query
        name: seller_id
        type: string
      - description: 出售用户手机号码
        in: query
        name: seller_phone
        type: string
      - description: 商品sku_id
        in: query
        name: sku_id
        type: string
      - description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        in: query
        name: status
        type: integer
      - description: 终端
        in: query
        name: terminal
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetAdminResaleOrderListResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取转卖订单列表
      tags:
      - 管理端-转卖订单管理
      x-apifox-folder: 管理端/转卖订单管理
  /admin/v1/trade_order/cancel:
    post:
      description: 取消订单
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.CancelAdminTradeOrderReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.CancelAdminTradeOrderResp'
              type: object
      security:
      - Bearer: []
      summary: 取消订单
      tags:
      - 管理端-直购订单管理
      x-apifox-folder: 管理端/直购订单管理
  /admin/v1/trade_order/detail:
    get:
      description: 管理端获取订单详细信息
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetAdminTradeOrderDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取订单详情
      tags:
      - 管理端-直购订单管理
      x-apifox-folder: 管理端/直购订单管理
  /admin/v1/trade_order/list:
    get:
      description: 管理端获取订单分页列表
      parameters:
      - description: 创建时间开始
        in: query
        name: created_at_gte
        type: string
      - description: 创建时间结束
        in: query
        name: created_at_lte
        type: string
      - in: query
        name: delivery_number
        type: string
      - description: 订单号
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 直购商品ID
        example: "0"
        in: query
        name: mall_item_id
        type: string
      - description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        in: query
        name: order_status
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 支付订单ID
        in: query
        name: pay_order_id
        type: string
      - description: 商品 sku_id
        in: query
        name: sku_id
        type: string
      - description: 用户ID
        in: query
        name: user_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetAdminTradeOrderListResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取订单列表
      tags:
      - 管理端-直购订单管理
      x-apifox-folder: 管理端/直购订单管理
  /admin/v1/trade_order/private_info:
    get:
      description: 管理端获取订单隐私详情
      parameters:
      - description: 订单id
        example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AdminGetOrderPrivateInfoResp'
              type: object
      security:
      - Bearer: []
      summary: 获取订单隐私详情
      tags:
      - 管理端-直购订单管理
      x-apifox-folder: 管理端/直购订单管理
  /admin/v1/trade_order/refund:
    post:
      description: 订单发起退款
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderRefundReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.TradeOrderRefundResp'
              type: object
      security:
      - Bearer: []
      summary: 订单发起退款
      tags:
      - 管理端-直购订单管理
      x-apifox-folder: 管理端/直购订单管理
  /admin/v1/trade_order/sync_freight:
    post:
      description: 同步订单物流信息
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SyncAdminTradeOrderFreightReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SyncAdminTradeOrderFreightResp'
              type: object
      security:
      - Bearer: []
      summary: 同步订单物流信息
      tags:
      - 管理端-直购订单管理
      x-apifox-folder: 管理端/直购订单管理
  /open/v1/home_feed/sync_view_count:
    post:
      description: 同步首页浏览量到数据库
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.OpenSyncViewCountToDBReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.OpenSyncViewCountToDBResp'
              type: object
      security:
      - Bearer: []
      summary: 同步首页浏览量到数据库
      tags:
      - open端-首页管理
      x-apifox-folder: open端/首页管理
  /open/v1/mall_item/trigger_display:
    post:
      description: 处理开始展示的直购商品
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.OpenTriggerDisplayByStartTimeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 处理开始展示的直购商品
      tags:
      - open端-直购商品
      x-apifox-folder: open端/直购商品
  /open/v1/mall_item/update_notify:
    post:
      description: 商品信息变动通知
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.OpenUpdateMallItemNotifyReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 商品信息变动通知
      tags:
      - open端-直购商品
      x-apifox-folder: open端/直购商品
  /open/v1/notice/supply_chain:
    post:
      description: 供应链通知
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.SupplyChainNoticeReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.SupplyChainNoticeResp'
              type: object
      security:
      - Bearer: []
      summary: 供应链通知
      tags:
      - open端-通知管理
      x-apifox-folder: open端/通知管理
  /open/v1/resale_order/locked_handler:
    post:
      description: 物品异常锁定订单处理
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.LockedHandlerReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.LockedHandlerResp'
              type: object
      security:
      - Bearer: []
      summary: 物品异常锁定订单处理
      tags:
      - open端-转卖订单
      x-apifox-folder: open端/转卖订单
  /open/v1/resale_order/release_user_items:
    post:
      description: 释放锁单失败订单的背包物品
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ReleaseUserItemsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ReleaseUserItemsResp'
              type: object
      security:
      - Bearer: []
      summary: 释放锁单失败订单的背包物品
      tags:
      - open端-转卖订单
      x-apifox-folder: open端/转卖订单
  /open/v1/resale_order/resale_item_transfer_handler:
    post:
      description: 物品发放异常订单处理
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ResaleItemTransferHandlerReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ResaleItemTransferHandlerResp'
              type: object
      security:
      - Bearer: []
      summary: 物品发放异常订单处理
      tags:
      - open端-转卖订单
      x-apifox-folder: open端/转卖订单
  /open/v1/trade_order/detail:
    get:
      description: 获取订单详情
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      - in: query
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetTradeOrderDetailResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 获取订单详情
      tags:
      - open端-直购订单
      x-apifox-folder: open端/直购订单
  /open/v1/trade_order/finish:
    post:
      description: 订单支付自动收货
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderFinishReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.TradeOrderFinishResp'
              type: object
      security:
      - Bearer: []
      summary: 订单支付自动收货
      tags:
      - open端-直购订单
      x-apifox-folder: open端/直购订单
  /open/v1/trade_order/pay_success:
    post:
      description: 订单支付成功回调
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderPaySuccessReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.TradeOrderPaySuccessResp'
              type: object
      security:
      - Bearer: []
      summary: 订单支付成功回调
      tags:
      - open端-直购订单
      x-apifox-folder: open端/直购订单
  /open/v1/trade_order/retry_upload_to_supply:
    post:
      description: 重新上传失败的订单到供应链
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderFailedRetryUploadToSupplyReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 重新上传失败的订单到供应链
      tags:
      - open端-直购订单
      x-apifox-folder: open端/直购订单
  /open/v1/trade_order/timeout_close:
    post:
      description: 订单支付超时关闭
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TradeOrderTimeoutCloseReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 订单支付超时关闭
      tags:
      - open端-直购订单
      x-apifox-folder: open端/直购订单
  /web/v1/home_feed/add_view_count:
    post:
      description: 增加浏览数量
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.WebAddHomeFeedViewCountReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebAddHomeFeedViewCountResp'
              type: object
      security:
      - Bearer: []
      summary: 增加浏览数量
      tags:
      - 用户端-首页管理
      x-apifox-folder: 用户端/首页管理
  /web/v1/home_feed/detail:
    get:
      description: 获取首页详情
      parameters:
      - description: 首页ID
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebGetHomeFeedDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取首页详情
      tags:
      - 用户端-首页管理
      x-apifox-folder: 用户端/首页管理
  /web/v1/home_feed/list:
    get:
      description: 获取首页列表
      parameters:
      - description: 商品 ip_id
        in: query
        name: ip_id
        type: string
      - description: 排序类型 1=综合，2=销量，3=价格
        in: query
        name: order_by
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 排序方式  asc-升序 desc-降序
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebGetHomeFeedListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取首页列表
      tags:
      - 用户端-首页管理
      x-apifox-folder: 用户端/首页管理
  /web/v1/mall_item/buy:
    post:
      description: 下单购买
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.WebBuyReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebBuyResp'
              type: object
      security:
      - Bearer: []
      summary: 下单购买
      tags:
      - 用户端-直购商品
      x-apifox-folder: 用户端/直购商品
  /web/v1/mall_item/detail:
    get:
      description: 获取直购商品详情
      parameters:
      - description: 直购商品ID
        example: "0"
        in: query
        name: id
        type: string
      - description: 商品ID
        in: query
        name: item_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebGetMallItemDetailResp'
              type: object
      security:
      - Bearer: []
      summary: 获取直购商品详情
      tags:
      - 用户端-直购商品
      x-apifox-folder: 用户端/直购商品
  /web/v1/mall_item/list:
    get:
      description: 获取直购商品列表
      parameters:
      - description: 商品 ip_id
        in: query
        name: ip_id
        type: string
      - description: 排序类型 1=综合，2=销量，3=价格
        in: query
        name: order_by
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 排序方式  asc-升序 desc-降序
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebGetMallItemListResp'
              type: object
      security:
      - Bearer: []
      summary: 获取直购商品列表
      tags:
      - 用户端-直购商品
      x-apifox-folder: 用户端/直购商品
  /web/v1/resale_item/buy:
    post:
      description: 转卖商品购买
      parameters:
      - description: 获取参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ResaleItemBuyReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ResaleItemBuyResp'
              type: object
      security:
      - Bearer: []
      summary: 转卖商品购买
      tags:
      - 用户端-转卖商品
      x-apifox-folder: 用户端/转卖商品
  /web/v1/resale_listings/add:
    post:
      description: 挂单出售
      parameters:
      - description: 参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.AddResaleListingsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.AddResaleListingsResp'
              type: object
      security:
      - Bearer: []
      summary: 挂单出售
      tags:
      - 用户端-转卖挂单管理
      x-apifox-folder: 用户端/转卖挂单
  /web/v1/resale_listings/published_list:
    get:
      description: 查询我发布的转卖挂单列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebPublishedResaleListingsListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询我发布的转卖挂单列表
      tags:
      - 用户端-转卖挂单
      x-apifox-folder: 用户端/转卖挂单
  /web/v1/resale_listings/take_down:
    post:
      description: 下架转卖挂单
      parameters:
      - description: 查询参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.TakeDownResaleListingsFromWebReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.TakeDownResaleListingsFromWebResp'
              type: object
      security:
      - Bearer: []
      summary: 下架转卖挂单
      tags:
      - 用户端-转卖挂单
      x-apifox-folder: 用户端/转卖挂单
  /web/v1/resale_listings_item/on_sale_list:
    get:
      description: 查询某个挂单商品在售列表
      parameters:
      - description: 商品 id
        in: query
        name: item_id
        required: true
        type: string
      - description: '排序字段，sale_price: 按照出售单价排序，created_at: 按照创建时间/发布时间排序'
        in: query
        name: order_by
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 排序方式，asc：升序，desc：降序
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebResaleListingsItemOnSaleListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询某个挂单商品在售列表
      tags:
      - 用户端-转卖挂单
      x-apifox-folder: 用户端/转卖挂单
  /web/v1/resale_listings_item/settlement_list:
    get:
      description: 查询某个挂单商品购买结算列表
      parameters:
      - description: 商品 id
        in: query
        name: item_id
        required: true
        type: string
      - description: 购买数量
        in: query
        minimum: 1
        name: quantity
        required: true
        type: integer
      - description: 出售单价（分）
        in: query
        name: sale_price
        type: integer
      - description: 卖家 id
        in: query
        name: seller_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebResaleListingsItemSettlementListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询某个挂单商品购买结算列表
      tags:
      - 用户端-转卖挂单管理
      x-apifox-folder: 用户端/转卖挂单
  /web/v1/resale_order/buy/detail:
    get:
      description: 查询我购买的转卖订单详情
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetWebResaleOrderBuyDetailResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询我购买的转卖订单详情
      tags:
      - 用户端-转卖订单
      x-apifox-folder: 用户端/转卖订单
  /web/v1/resale_order/buy/list:
    get:
      description: 查询我购买的转卖订单列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        in: query
        name: status
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebResaleOrderBuyListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询我购买的转卖订单列表
      tags:
      - 用户端-转卖订单
      x-apifox-folder: 用户端/转卖订单
  /web/v1/resale_order/recent_list:
    get:
      description: 查询最近成交记录
      parameters:
      - description: 商品ID
        in: query
        name: item_id
        required: true
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebResaleOrderRecentListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询最近成交记录
      tags:
      - 用户端-转卖订单
      x-apifox-folder: 用户端/转卖订单
  /web/v1/resale_order/sale/detail:
    get:
      description: 查询我出售的转卖订单详情
      parameters:
      - example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetWebResaleOrderSaleDetailResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询我出售的转卖订单详情
      tags:
      - 用户端-转卖订单
      x-apifox-folder: 用户端/转卖订单
  /web/v1/resale_order/sale/list:
    get:
      description: 查询我出售的转卖订单列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      - description: 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
        in: query
        name: status
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebResaleOrderSaleListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询我出售的转卖订单列表
      tags:
      - 用户端-转卖订单
      x-apifox-folder: 用户端/转卖订单
  /web/v1/resale_user_item/cancel:
    post:
      description: 取消出售
      parameters:
      - description: 查询参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.CancelResaleUserItemFromWebReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.CancelResaleUserItemFromWebResp'
              type: object
      security:
      - Bearer: []
      summary: 取消出售
      tags:
      - 用户端-转卖商品持仓
      x-apifox-folder: 用户端/转卖商品持仓
  /web/v1/resale_user_item/item_list:
    get:
      description: 查询转卖商品持仓商品列表
      parameters:
      - description: 商品名称
        in: query
        name: item_name
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebResaleUserItemListByItemResp'
              type: object
      security:
      - Bearer: []
      summary: 查询转卖商品持仓商品列表
      tags:
      - 用户端-转卖商品持仓
      x-apifox-folder: 用户端/转卖商品持仓
  /web/v1/resale_user_item/trade_info:
    get:
      description: 获取持仓商品交易信息
      parameters:
      - description: 商品 id
        in: query
        name: item_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebResaleUserItemTradeInfoResp'
              type: object
      security:
      - Bearer: []
      summary: 获取持仓商品交易信息
      tags:
      - 用户端-转卖商品持仓
      x-apifox-folder: 用户端/转卖商品持仓
  /web/v1/resale_user_item/user_item_list:
    get:
      description: 获取某个转卖商品的持仓列表
      parameters:
      - description: 商品 id
        in: query
        name: item_id
        required: true
        type: string
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebResaleUserItemListByUserItemResp'
              type: object
      security:
      - Bearer: []
      summary: 获取某个转卖商品的持仓列表
      tags:
      - 用户端-转卖商品持仓
      x-apifox-folder: 用户端/转卖商品持仓
  /web/v1/trade_order/cancel:
    post:
      description: 取消订单
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.CancelTradeOrderReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.CancelTradeOrderResp'
              type: object
      security:
      - Bearer: []
      summary: 取消订单
      tags:
      - 用户端-直购订单
      x-apifox-folder: 用户端/直购订单
  /web/v1/trade_order/confirm:
    post:
      description: 确定收货
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.ConfirmTradeOrderReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.ConfirmTradeOrderResp'
              type: object
      security:
      - Bearer: []
      summary: 确定收货
      tags:
      - 用户端-直购订单
      x-apifox-folder: 用户端/直购订单
  /web/v1/trade_order/del:
    post:
      description: 删除订单
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.DelTradeOrderReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.DelTradeOrderResp'
              type: object
      security:
      - Bearer: []
      summary: 删除订单
      tags:
      - 用户端-直购订单
      x-apifox-folder: 用户端/直购订单
  /web/v1/trade_order/detail:
    get:
      description: 查询直购订单详情
      parameters:
      - description: 订单id
        example: "0"
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetWebTradeOrderDetailResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询直购订单详情
      tags:
      - 用户端-直购订单
      x-apifox-folder: 用户端/直购订单
  /web/v1/trade_order/freight_info:
    get:
      description: 查询直购订单物流信息
      parameters:
      - description: 快递单号
        in: query
        name: delivery_number
        required: true
        type: string
      - description: 订单id
        example: "0"
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetWebTradeOrderFreightInfoResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询直购订单物流信息
      tags:
      - 用户端-直购订单
      x-apifox-folder: 用户端/直购订单
  /web/v1/trade_order/list:
    get:
      description: 查询直购订单列表
      parameters:
      - description: 订单状态（0=待支付，10=待发货，20=已发货，30=已完成，40=已取消）
        in: query
        name: order_status
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.GetWebTradeOrderListResp'
              type: object
      security:
      - Bearer: []
      summary: 查询直购订单列表
      tags:
      - 用户端-直购订单
      x-apifox-folder: 用户端/直购订单
  /web/v1/trade_order/status_stat:
    get:
      description: 查询直购订单状态统计
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/define.GetWebTradeOrderStatusStatResp'
                  type: array
              type: object
      security:
      - Bearer: []
      summary: 查询直购订单状态统计
      tags:
      - 用户端-直购订单
      x-apifox-folder: 用户端/直购订单
  /web/v1/trade_order/update_address:
    post:
      description: 修改地址
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.UpdateTradeOrderAddressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.UpdateTradeOrderAddressResp'
              type: object
      security:
      - Bearer: []
      summary: 修改地址
      tags:
      - 用户端-直购订单
      x-apifox-folder: 用户端/直购订单
  /web/v1/user_wishlist/add:
    post:
      description: 添加想要
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.WebWishlistReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 添加想要
      tags:
      - 用户端-商品想要
      x-apifox-folder: 用户端/商品想要
  /web/v1/user_wishlist/list:
    get:
      description: 用户想要列表
      parameters:
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      - description: 每页显示的条目数量
        in: query
        name: page_size
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/response.Data'
            - properties:
                data:
                  $ref: '#/definitions/define.WebWishlistListResp'
              type: object
      security:
      - Bearer: []
      summary: 用户想要列表
      tags:
      - 用户端-商品想要
      x-apifox-folder: 用户端/商品想要
  /web/v1/user_wishlist/remove:
    post:
      description: 移除想要
      parameters:
      - description: 请求参数
        in: body
        name: data
        required: true
        schema:
          $ref: '#/definitions/define.WebWishlistReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/response.Data'
      security:
      - Bearer: []
      summary: 移除想要
      tags:
      - 用户端-商品想要
      x-apifox-folder: 用户端/商品想要
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
