package utils

// FindIntInSlice 判断是否存在于切片内(int)
func FindIntInSlice(v int32, s []int32) bool {
	for _, sv := range s {
		if sv == v {
			return true
		}
	}
	return false
}

// Slice2KeyMap 将切片转为指定字段为key的map
func Slice2KeyMap[T any, K comparable](slice []T, keyFunc func(T) K) map[K]T {
	result := make(map[K]T)
	for _, item := range slice {
		key := keyFunc(item)
		result[key] = item
	}
	return result
}

// ChunkSlice 将切片按照大小拆分成一个二维切片
func ChunkSlice[T any](slice []T, chunkSize int) [][]T {
	if chunkSize <= 0 {
		panic("chunkSize must be positive")
	}

	chunks := make([][]T, 0, (len(slice)+chunkSize-1)/chunkSize)
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}

		// 创建新切片避免引用原始大数组
		chunk := make([]T, end-i)
		copy(chunk, slice[i:end])
		chunks = append(chunks, chunk)
	}
	return chunks
}
