package tmt

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type UserAddressInfo struct {
	Id          string `json:"_id" form:"_id"`
	Name        string `json:"name" form:"name"`
	MobilePhone string `json:"mobile_phone" form:"mobile_phone"`
	Code        string `json:"code" form:"code"`
	Area        string `json:"area" form:"area"`
	Place       string `json:"place" form:"place"`
}

type QueryAddressResp struct {
	Code int32            `json:"code" form:"code"`
	Desc string           `json:"desc" form:"desc"`
	Data *UserAddressInfo `json:"data" form:"data"`
}

func QueryAddressById(ctx context.Context, userId string, addressId string) (*UserAddressInfo, error) {
	rsp := &QueryAddressResp{}
	req, err := request.Tmt()
	if err != nil {
		return nil, err
	}
	err = req.Call(
		ctx,
		"open/user/v1/user_address",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"user_id": userId, "address_id": addressId}),
		utilRequest.WithMethodGet(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data, err
}
