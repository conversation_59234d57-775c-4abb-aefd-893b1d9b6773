// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newResaleOrderItem(db *gorm.DB, opts ...gen.DOOption) resaleOrderItem {
	_resaleOrderItem := resaleOrderItem{}

	_resaleOrderItem.resaleOrderItemDo.UseDB(db, opts...)
	_resaleOrderItem.resaleOrderItemDo.UseModel(&model.ResaleOrderItem{})

	tableName := _resaleOrderItem.resaleOrderItemDo.TableName()
	_resaleOrderItem.ALL = field.NewAsterisk(tableName)
	_resaleOrderItem.ID = field.NewInt64(tableName, "id")
	_resaleOrderItem.Status = field.NewInt32(tableName, "status")
	_resaleOrderItem.ResaleOrderID = field.NewInt64(tableName, "resale_order_id")
	_resaleOrderItem.ResaleListingsID = field.NewInt64(tableName, "resale_listings_id")
	_resaleOrderItem.ResaleListingsItemID = field.NewInt64(tableName, "resale_listings_item_id")
	_resaleOrderItem.SellerID = field.NewString(tableName, "seller_id")
	_resaleOrderItem.ItemID = field.NewString(tableName, "item_id")
	_resaleOrderItem.UserItemID = field.NewString(tableName, "user_item_id")
	_resaleOrderItem.SalePrice = field.NewInt64(tableName, "sale_price")
	_resaleOrderItem.NewUserItemID = field.NewString(tableName, "new_user_item_id")
	_resaleOrderItem.Fee = field.NewInt64(tableName, "fee")
	_resaleOrderItem.CreatedAt = field.NewTime(tableName, "created_at")
	_resaleOrderItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_resaleOrderItem.IsDel = field.NewField(tableName, "is_del")

	_resaleOrderItem.fillFieldMap()

	return _resaleOrderItem
}

// resaleOrderItem 转卖订单详情表
type resaleOrderItem struct {
	resaleOrderItemDo

	ALL                  field.Asterisk
	ID                   field.Int64  // 主键
	Status               field.Int32  // 状态（0=待支付，1=物品锁定成功，10=已支付，20=转移中，30=已完成，40=已取消）
	ResaleOrderID        field.Int64  // 转卖订单ID
	ResaleListingsID     field.Int64  // 出售单ID
	ResaleListingsItemID field.Int64  // 出售子单ID
	SellerID             field.String // 出售用户ID
	ItemID               field.String // 商品ID
	UserItemID           field.String // 用户物品ID
	SalePrice            field.Int64  // 单价(分）
	NewUserItemID        field.String // 新的用户物品ID
	Fee                  field.Int64  // 手续费
	CreatedAt            field.Time   // 创建时间
	UpdatedAt            field.Time   // 更新时间
	IsDel                field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (r resaleOrderItem) Table(newTableName string) *resaleOrderItem {
	r.resaleOrderItemDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r resaleOrderItem) As(alias string) *resaleOrderItem {
	r.resaleOrderItemDo.DO = *(r.resaleOrderItemDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *resaleOrderItem) updateTableName(table string) *resaleOrderItem {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt64(table, "id")
	r.Status = field.NewInt32(table, "status")
	r.ResaleOrderID = field.NewInt64(table, "resale_order_id")
	r.ResaleListingsID = field.NewInt64(table, "resale_listings_id")
	r.ResaleListingsItemID = field.NewInt64(table, "resale_listings_item_id")
	r.SellerID = field.NewString(table, "seller_id")
	r.ItemID = field.NewString(table, "item_id")
	r.UserItemID = field.NewString(table, "user_item_id")
	r.SalePrice = field.NewInt64(table, "sale_price")
	r.NewUserItemID = field.NewString(table, "new_user_item_id")
	r.Fee = field.NewInt64(table, "fee")
	r.CreatedAt = field.NewTime(table, "created_at")
	r.UpdatedAt = field.NewTime(table, "updated_at")
	r.IsDel = field.NewField(table, "is_del")

	r.fillFieldMap()

	return r
}

func (r *resaleOrderItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *resaleOrderItem) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 14)
	r.fieldMap["id"] = r.ID
	r.fieldMap["status"] = r.Status
	r.fieldMap["resale_order_id"] = r.ResaleOrderID
	r.fieldMap["resale_listings_id"] = r.ResaleListingsID
	r.fieldMap["resale_listings_item_id"] = r.ResaleListingsItemID
	r.fieldMap["seller_id"] = r.SellerID
	r.fieldMap["item_id"] = r.ItemID
	r.fieldMap["user_item_id"] = r.UserItemID
	r.fieldMap["sale_price"] = r.SalePrice
	r.fieldMap["new_user_item_id"] = r.NewUserItemID
	r.fieldMap["fee"] = r.Fee
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["is_del"] = r.IsDel
}

func (r resaleOrderItem) clone(db *gorm.DB) resaleOrderItem {
	r.resaleOrderItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r resaleOrderItem) replaceDB(db *gorm.DB) resaleOrderItem {
	r.resaleOrderItemDo.ReplaceDB(db)
	return r
}

type resaleOrderItemDo struct{ gen.DO }

type IResaleOrderItemDo interface {
	gen.SubQuery
	Debug() IResaleOrderItemDo
	WithContext(ctx context.Context) IResaleOrderItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IResaleOrderItemDo
	WriteDB() IResaleOrderItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IResaleOrderItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IResaleOrderItemDo
	Not(conds ...gen.Condition) IResaleOrderItemDo
	Or(conds ...gen.Condition) IResaleOrderItemDo
	Select(conds ...field.Expr) IResaleOrderItemDo
	Where(conds ...gen.Condition) IResaleOrderItemDo
	Order(conds ...field.Expr) IResaleOrderItemDo
	Distinct(cols ...field.Expr) IResaleOrderItemDo
	Omit(cols ...field.Expr) IResaleOrderItemDo
	Join(table schema.Tabler, on ...field.Expr) IResaleOrderItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IResaleOrderItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IResaleOrderItemDo
	Group(cols ...field.Expr) IResaleOrderItemDo
	Having(conds ...gen.Condition) IResaleOrderItemDo
	Limit(limit int) IResaleOrderItemDo
	Offset(offset int) IResaleOrderItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleOrderItemDo
	Unscoped() IResaleOrderItemDo
	Create(values ...*model.ResaleOrderItem) error
	CreateInBatches(values []*model.ResaleOrderItem, batchSize int) error
	Save(values ...*model.ResaleOrderItem) error
	First() (*model.ResaleOrderItem, error)
	Take() (*model.ResaleOrderItem, error)
	Last() (*model.ResaleOrderItem, error)
	Find() ([]*model.ResaleOrderItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleOrderItem, err error)
	FindInBatches(result *[]*model.ResaleOrderItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ResaleOrderItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IResaleOrderItemDo
	Assign(attrs ...field.AssignExpr) IResaleOrderItemDo
	Joins(fields ...field.RelationField) IResaleOrderItemDo
	Preload(fields ...field.RelationField) IResaleOrderItemDo
	FirstOrInit() (*model.ResaleOrderItem, error)
	FirstOrCreate() (*model.ResaleOrderItem, error)
	FindByPage(offset int, limit int) (result []*model.ResaleOrderItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IResaleOrderItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (r resaleOrderItemDo) Debug() IResaleOrderItemDo {
	return r.withDO(r.DO.Debug())
}

func (r resaleOrderItemDo) WithContext(ctx context.Context) IResaleOrderItemDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r resaleOrderItemDo) ReadDB() IResaleOrderItemDo {
	return r.Clauses(dbresolver.Read)
}

func (r resaleOrderItemDo) WriteDB() IResaleOrderItemDo {
	return r.Clauses(dbresolver.Write)
}

func (r resaleOrderItemDo) Session(config *gorm.Session) IResaleOrderItemDo {
	return r.withDO(r.DO.Session(config))
}

func (r resaleOrderItemDo) Clauses(conds ...clause.Expression) IResaleOrderItemDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r resaleOrderItemDo) Returning(value interface{}, columns ...string) IResaleOrderItemDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r resaleOrderItemDo) Not(conds ...gen.Condition) IResaleOrderItemDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r resaleOrderItemDo) Or(conds ...gen.Condition) IResaleOrderItemDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r resaleOrderItemDo) Select(conds ...field.Expr) IResaleOrderItemDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r resaleOrderItemDo) Where(conds ...gen.Condition) IResaleOrderItemDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r resaleOrderItemDo) Order(conds ...field.Expr) IResaleOrderItemDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r resaleOrderItemDo) Distinct(cols ...field.Expr) IResaleOrderItemDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r resaleOrderItemDo) Omit(cols ...field.Expr) IResaleOrderItemDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r resaleOrderItemDo) Join(table schema.Tabler, on ...field.Expr) IResaleOrderItemDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r resaleOrderItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IResaleOrderItemDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r resaleOrderItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IResaleOrderItemDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r resaleOrderItemDo) Group(cols ...field.Expr) IResaleOrderItemDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r resaleOrderItemDo) Having(conds ...gen.Condition) IResaleOrderItemDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r resaleOrderItemDo) Limit(limit int) IResaleOrderItemDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r resaleOrderItemDo) Offset(offset int) IResaleOrderItemDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r resaleOrderItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleOrderItemDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r resaleOrderItemDo) Unscoped() IResaleOrderItemDo {
	return r.withDO(r.DO.Unscoped())
}

func (r resaleOrderItemDo) Create(values ...*model.ResaleOrderItem) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r resaleOrderItemDo) CreateInBatches(values []*model.ResaleOrderItem, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r resaleOrderItemDo) Save(values ...*model.ResaleOrderItem) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r resaleOrderItemDo) First() (*model.ResaleOrderItem, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrderItem), nil
	}
}

func (r resaleOrderItemDo) Take() (*model.ResaleOrderItem, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrderItem), nil
	}
}

func (r resaleOrderItemDo) Last() (*model.ResaleOrderItem, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrderItem), nil
	}
}

func (r resaleOrderItemDo) Find() ([]*model.ResaleOrderItem, error) {
	result, err := r.DO.Find()
	return result.([]*model.ResaleOrderItem), err
}

func (r resaleOrderItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleOrderItem, err error) {
	buf := make([]*model.ResaleOrderItem, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r resaleOrderItemDo) FindInBatches(result *[]*model.ResaleOrderItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r resaleOrderItemDo) Attrs(attrs ...field.AssignExpr) IResaleOrderItemDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r resaleOrderItemDo) Assign(attrs ...field.AssignExpr) IResaleOrderItemDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r resaleOrderItemDo) Joins(fields ...field.RelationField) IResaleOrderItemDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r resaleOrderItemDo) Preload(fields ...field.RelationField) IResaleOrderItemDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r resaleOrderItemDo) FirstOrInit() (*model.ResaleOrderItem, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrderItem), nil
	}
}

func (r resaleOrderItemDo) FirstOrCreate() (*model.ResaleOrderItem, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleOrderItem), nil
	}
}

func (r resaleOrderItemDo) FindByPage(offset int, limit int) (result []*model.ResaleOrderItem, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r resaleOrderItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r resaleOrderItemDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r resaleOrderItemDo) Delete(models ...*model.ResaleOrderItem) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *resaleOrderItemDo) withDO(do gen.Dao) *resaleOrderItemDo {
	r.DO = *do.(*gen.DO)
	return r
}
