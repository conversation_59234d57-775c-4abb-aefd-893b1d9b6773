package open

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// ReleaseUserItems
// @Summary 释放锁单失败订单的背包物品
// @Description 释放锁单失败订单的背包物品
// @Tags open端-转卖订单
// @x-apifox-folder "open端/转卖订单"
// @Param data body define.ReleaseUserItemsReq true "获取参数"
// @Success 200 {object} response.Data{data=define.ReleaseUserItemsResp}
// @Router  /open/v1/resale_order/release_user_items [post]
// @Security Bearer
// @produce  json
func ReleaseUserItems(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ReleaseUserItemsReq{}, s.ReleaseUserItems)
}

// LockedHandler
// @Summary 物品异常锁定订单处理
// @Description 物品异常锁定订单处理
// @Tags open端-转卖订单
// @x-apifox-folder "open端/转卖订单"
// @Param data body define.LockedHandlerReq true "获取参数"
// @Success 200 {object} response.Data{data=define.LockedHandlerResp}
// @Router  /open/v1/resale_order/locked_handler [post]
// @Security Bearer
// @produce  json
func LockedHandler(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.LockedHandlerReq{}, s.LockedHandler)
}

// ResaleItemTransferHandler
// @Summary 物品发放异常订单处理
// @Description 物品发放异常订单处理
// @Tags open端-转卖订单
// @x-apifox-folder "open端/转卖订单"
// @Param data body define.ResaleItemTransferHandlerReq true "获取参数"
// @Success 200 {object} response.Data{data=define.ResaleItemTransferHandlerResp}
// @Router  /open/v1/resale_order/resale_item_transfer_handler [post]
// @Security Bearer
// @produce  json
func ResaleItemTransferHandler(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ResaleItemTransferHandlerReq{}, s.ResaleItemTransferHandler)
}
