package global

import (
	"go-micro.dev/v4/broker"
	"net/http"
	"sync"

	"e.coding.net/g-dtay0385/common/go-redis"
	"gorm.io/gorm"
)

var (
	DB      *gorm.DB
	DBList  map[string]*gorm.DB
	REDIS   *redis.Client
	BROKER  broker.Broker
	lock    sync.RWMutex
	Engine  http.Handler
	Routers []func()
)

// GetGlobalDBByDBName 通过名称获取db list中的db
func GetGlobalDBByDBName(dbname string) *gorm.DB {
	lock.RLock()
	defer lock.RUnlock()
	return DBList[dbname]
}

// MustGetGlobalDBByDBName 通过名称获取db 如果不存在则panic
func MustGetGlobalDBByDBName(dbname string) *gorm.DB {
	lock.RLock()
	defer lock.RUnlock()
	db, ok := DBList[dbname]
	if !ok || db == nil {
		panic("db no init")
	}
	return db
}

// SetEngine 设置路由引擎
func SetEngine(engine http.Handler) {
	Engine = engine
}

// GetEngine 获取路由引擎
func GetEngine() http.Handler {
	return Engine
}
