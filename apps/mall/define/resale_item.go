package define

import (
	"marketplace_service/pkg/pagination"
	"time"
)

type (
	AddResaleItemReq struct {
		ItemID   string `json:"item_id" binding:"required"`                  // 商品ID
		SkuID    string `json:"sku_id" binding:"required"`                   // 商品 sku_id
		Priority int32  `json:"priority" binding:"omitempty,min=0,max=9999"` // 优先级
	}
	AddResaleItemResp struct {
		ID int64 `json:"id,string"` // 转卖商品ID
	}
)

type (
	EditResaleItemReq struct {
		ID                 int64  `json:"id,string" binding:"required"`             // ID
		ResaleStatus       *int32 `json:"resale_status" binding:"required"`         // 转卖状态（0-关闭, 1-开启）
		TradeFrequencyType int32  `json:"trade_frequency_type" binding:"required"`  // 交易频率类型（1-同步全局标准, 2-特定交易频次）
		IntervalMinutes    *int32 `json:"trade_interval"`                           // 交易间隔（分）
		IsCustomPriceLimit *int32 `json:"is_custom_price_limit" binding:"required"` // 是否使用特定限价（0-关闭, 1-开启）
		MinPriceRatio      *int32 `json:"min_resale_ratio"`                         // 最低售价百分比（基于市场公允进价）
		MaxPriceRatio      *int32 `json:"max_resale_ratio"`                         // 最高售价比例（基于历史最高成交价）
	}

	EditResaleItemResp struct {
		ID int64 `json:"id,string"` // 转卖商品ID
	}
)

type (
	EditResaleItemPriorityReq struct {
		ID       int64  `json:"id,string" binding:"required"`                // ID
		Priority *int32 `json:"priority" binding:"omitempty,min=1,max=9999"` // 优先级
	}

	EditResaleItemPriorityResp struct {
		ID int64 `json:"id,string"` // 转卖商品ID
	}
)

type (
	EditResaleItemStatusReq struct {
		ID     int64 `json:"id,string" binding:"required"`    // ID
		Status int32 `json:"status" binding:"required,gte=0"` // 状态（0=待上架，1=已上架，2=已下架）
	}

	EditResaleItemStatusResp struct {
		ID int64 `json:"id,string"` // 转卖商品ID
	}
)

type (
	ResaleItemPageReq struct {
		pagination.Pagination
		ID                 string   `form:"id,string" json:"id,string" search:"type:eq;column:id;table:resale_item"`                                            // 转卖商品ID
		ItemID             string   `form:"item_id" json:"item_id" search:"type:eq;column:item_id;table:resale_item"`                                           // 商品ID
		ItemName           string   `form:"item_name" json:"item_name" search:"type:like;column:item_name;table:resale_item"`                                   // 商品名称
		SkuID              string   `form:"sku_id" json:"sku_id" search:"type:eq;column:sku_id;table:resale_item"`                                              // 商品 sku_id
		CategoryIDs        []string `form:"category_ids" json:"category_ids" search:"type:in;column:category_id;table:resale_item"`                             // 商品分类
		IPIDs              []string `form:"ip_ids" json:"ip_ids" search:"type:in;column:ip_id;table:resale_item"`                                               // 商品ip
		TrademarkIDs       []string `form:"trademark_ids" json:"trademark_ids" search:"type:in;column:trademark_id;table:resale_item"`                          // 商品品牌
		Status             *int32   `form:"status" json:"status" search:"type:eq;column:status;table:resale_item"`                                              // 状态（0=待上架，1=已上架，2=已下架）
		ResaleStatus       *int32   `form:"resale_status" json:"resale_status" search:"type:eq;column:resale_status;table:resale_item"`                         // 转卖状态（0=关闭，1=开启）
		IsCustomPriceLimit *int32   `form:"is_custom_price_limit" json:"is_custom_price_limit" search:"type:eq;column:is_custom_price_limit;table:resale_item"` // 是否使用特定限价（0-关闭, 1-开启）
		UpdatedBy          string   `form:"updated_by" json:"updated_by" search:"type:eq;column:updated_by;table:resale_item"`                                  // 操作人
	}

	ResaleItemPageResp struct {
		Total int64                 `json:"total"` // 总数
		List  []*ResaleItemPageData `json:"list"`  // 列表
	}

	ResaleItemPageData struct {
		ID                 int64     `json:"id,string"`             // 主键
		Status             int32     `json:"status"`                // 状态（0=待上架，1=已上架，2=已下架）
		ItemID             string    `json:"item_id"`               // 商品 ID
		SkuID              string    `json:"sku_id"`                // 商品 sku_id
		ItemName           string    `json:"item_name"`             // 商品名称
		IPID               string    `json:"ip_id"`                 // 商品IP ID
		IPName             string    `json:"ip_name"`               // 商品IP名称
		ItemIconURL        string    `json:"item_icon_url"`         // 商品主图
		ItemSpecs          string    `json:"item_specs"`            // 商品规格
		CategoryID         string    `json:"category_id"`           // 商品分类ID
		CategoryName       string    `json:"category_name"`         // 商品分类名称
		TrademarkID        string    `json:"trademark_id"`          // 商品品牌ID
		TrademarkName      string    `json:"trademark_name"`        // 商品品牌名称
		MarketPrice        int64     `json:"market_price"`          // 市场公允价（分）
		MaxLimitPrice      int64     `json:"max_limit_price"`       // 最高限价（分）
		MinLimitPrice      int64     `json:"min_limit_price"`       // 最低限价（分）
		ResaleStatus       int32     `json:"resale_status"`         // 转卖状态（0-关闭, 1-开启）
		TradeFrequencyType int32     `json:"trade_frequency_type"`  // 交易频次类型（1-同步全局标准, 2-特定交易频次）
		IsCustomPriceLimit int32     `json:"is_custom_price_limit"` // 是否使用特定限价（0-关闭, 1-开启）
		Priority           int32     `json:"priority"`              // 优先级
		UpdatedBy          string    `json:"updated_by"`            // 最后操作人
		UpdatedAt          time.Time `json:"updated_at"`            // 最后操作时间
	}
)

type (
	ResaleItemBuyReq struct {
		Password               string                    `json:"password" binding:"required"`                  // 密码
		ResaleListingsItemInfo []*ResaleListingsItemInfo `json:"resale_listings_item_info" binding:"required"` // 挂单列表信息
		Quantity               int32                     `json:"quantity"  binding:"required,min=1"`           // 购买数量
		TotalAmount            int64                     `json:"total_amount" binding:"required,min=1"`        // 总金额
		BatchId                int64                     `json:"-"`
	}

	ResaleListingsItemInfo struct {
		ResaleListingsId      string   `json:"resale_listings_id"`       // 转卖挂单id列表
		ResaleListingsItemIds []string `json:"resale_listings_item_ids"` // 转卖挂单物品id列表
	}

	ResaleItemBuyResp struct {
		PaySuccessOrderIds []int64 `json:"pay_success_order_ids"`
	}
)

type (
	ResaleItemDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"` // 转卖商品ID
	}

	ResaleItemDetailResp struct {
		ID                 int64    `json:"id,string"`             // 转卖商品ID
		ItemID             string   `json:"item_id"`               // 商品 ID
		SkuID              string   `json:"sku_id"`                // SKU ID
		ItemIconURL        string   `json:"item_icon_url"`         // 商品主图
		ItemName           string   `json:"item_name"`             // 商品名称
		ItemSpecs          string   `json:"item_specs"`            // 规格
		IPID               string   `json:"ip_id"`                 // IP ID
		IPName             string   `json:"ip_name"`               // IP名称
		CategoryID         string   `json:"category_id"`           // 商品分类ID
		CategoryName       string   `json:"category_name"`         // 商品分类名称
		TrademarkID        string   `json:"trademark_id"`          // 商品品牌ID
		TrademarkName      string   `json:"trademark_name"`        // 商品品牌名称
		MarketPrice        int64    `json:"market_price"`          // 市场公允价（分）
		ResaleStatus       int32    `json:"resale_status"`         // 转卖状态（0-关闭, 1-开启）
		TradeFrequencyType int32    `json:"trade_frequency_type"`  // 交易频率类型（1-同步全局标准, 2-特定交易频次）
		IntervalMinutes    *int32   `json:"trade_interval"`        // 交易间隔（分）
		IsCustomPriceLimit int32    `json:"is_custom_price_limit"` // 是否使用特定限价（0-关闭, 1-开启）
		MinPriceRatio      *int32   `json:"min_resale_ratio"`      // 最低售价百分比（基于市场公允进价）
		MaxPriceRatio      *int32   `json:"max_resale_ratio"`      // 最高售价比例（基于历史最高成交价）
		DetailH5           string   `json:"detail_h5"`             // 商品详情
		SellListings       int32    `json:"sell_listings"`         // 商品库存
		ImageInfos         []string `json:"image_infos"`           // 商品详情图片
	}
)

type (
	// ResaleItemTradeConfig 转卖商品交易配置（已考虑全局配置）
	ResaleItemTradeConfig struct {
		ResaleStatus    int32 `json:"resale_status"`    // 转卖状态
		IntervalMinutes int32 `json:"trade_interval"`   // 交易间隔（分）
		MinPriceRatio   int32 `json:"min_resale_ratio"` // 最低售价百分比（基于市场公允进价）
		MaxPriceRatio   int32 `json:"max_resale_ratio"` // 最高售价比例（基于历史最高成交价）
		MinLimitPrice   int64 `json:"min_limit_price"`  // 最低限价（分）
		MaxLimitPrice   int64 `json:"max_limit_price"`  // 最高限价（分）
	}
)
