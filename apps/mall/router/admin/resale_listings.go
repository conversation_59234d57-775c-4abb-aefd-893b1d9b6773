package admin

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/admin"
)

// ResaleListings 转卖挂单管理端相关
func ResaleListings(router *gin.RouterGroup) {
	group := router.Group("/resale_listings")
	{
		// 获取转卖挂单列表
		group.GET("/list", admin.GetAdminResaleListingsList)
		// 获取转卖挂单详情
		group.GET("/detail", admin.GetAdminResaleListingsDetail)
		// 导出转卖挂单列表
		group.GET("/export", admin.ExportResaleListingsList)
		// 更新转卖挂单状态
		group.POST("/update_status", admin.UpdateResaleListingsStatus)
	}
}
