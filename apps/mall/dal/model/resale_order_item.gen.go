// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameResaleOrderItem = "resale_order_item"

// ResaleOrderItem 转卖订单详情表
type ResaleOrderItem struct {
	ID                   int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                    // 主键
	Status               int32                 `gorm:"column:status;type:tinyint;not null;comment:状态（0=待支付，1=物品锁定成功，10=已支付，20=转移中，30=已完成，40=已取消）" json:"status"` // 状态（0=待支付，1=物品锁定成功，10=已支付，20=转移中，30=已完成，40=已取消）
	ResaleOrderID        int64                 `gorm:"column:resale_order_id;type:bigint;not null;comment:转卖订单ID" json:"resale_order_id"`                        // 转卖订单ID
	ResaleListingsID     int64                 `gorm:"column:resale_listings_id;type:bigint;not null;comment:出售单ID" json:"resale_listings_id"`                   // 出售单ID
	ResaleListingsItemID int64                 `gorm:"column:resale_listings_item_id;type:bigint;not null;comment:出售子单ID" json:"resale_listings_item_id"`        // 出售子单ID
	SellerID             string                `gorm:"column:seller_id;type:varchar(32);not null;comment:出售用户ID" json:"seller_id"`                               // 出售用户ID
	ItemID               string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                     // 商品ID
	UserItemID           string                `gorm:"column:user_item_id;type:varchar(32);not null;comment:用户物品ID" json:"user_item_id"`                         // 用户物品ID
	SalePrice            int64                 `gorm:"column:sale_price;type:bigint;not null;comment:单价(分）" json:"sale_price"`                                   // 单价(分）
	NewUserItemID        string                `gorm:"column:new_user_item_id;type:varchar(32);comment:新的用户物品ID" json:"new_user_item_id"`                        // 新的用户物品ID
	Fee                  int64                 `gorm:"column:fee;type:bigint;not null;comment:手续费" json:"fee"`                                                   // 手续费
	CreatedAt            time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`  // 创建时间
	UpdatedAt            time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`  // 更新时间
	IsDel                soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`         // 是否删除【0->未删除; 1->删除】
}

// TableName ResaleOrderItem's table name
func (*ResaleOrderItem) TableName() string {
	return TableNameResaleOrderItem
}
