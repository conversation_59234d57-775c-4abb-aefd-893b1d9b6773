package define

import "marketplace_service/pkg/pagination"

type (
	AddHomeFeedReq struct {
		ItemID   string `json:"item_id" binding:"required"`
		Priority int32  `json:"priority" binding:"omitempty,min=0,max=9999"`
		SkuID    string `json:"sku_id" binding:"required"`
	}
)

type (
	WebGetHomeFeedListReq struct {
		pagination.Pagination
		IPID      string `form:"ip_id" json:"ip_id"`           //商品 ip_id
		OrderBy   int32  `form:"order_by" json:"order_by"`     // 排序类型 1=综合，2=销量，3=价格
		SortOrder string `form:"sort_order" json:"sort_order"` // 排序方式  asc-升序 desc-降序
	}
	WebGetHomeFeedListResp struct {
		List    []*WebHomeFeedListData `json:"list"`
		HasMore bool                   `json:"has_more"` // 判断当前页是否为最后一页
	}
	WebHomeFeedListData struct {
		ID             int64  `json:"id,string"`        // 首页主键
		Status         int32  `json:"status"`           // 状态
		ResaleStatus   int32  `json:"resale_status"`    // 商品转卖状态
		ItemMallStatus int32  `json:"item_mall_status"` // 商品直购状态
		ItemID         string `json:"item_id"`          // 商品ID
		ItemName       string `json:"item_name"`        // 商品名称
		ItemSpecs      string `json:"item_specs"`       // 商品规格
		ItemIconURL    string `json:"item_icon_url"`    // 商品主图
		Priority       int32  `json:"priority"`         // 优先级
		SalePrice      int64  `json:"sale_price"`       // 售价
		OriginalPrice  int64  `json:"original_price"`   // 闪购原价
		ResaleCount    int32  `json:"resale_count"`     // 转卖数量
		SalesVolume    int32  `json:"sales_volume"`     // 销量
		WishCount      int32  `json:"wish_count"`       // 想要数量
	}
)

type (
	WebGetHomeFeedDetailReq struct {
		ID     int64  `form:"id,string" json:"id,string"` // 首页ID
		ItemID string `form:"item_id" json:"item_id"`     // 商品ID
	}
	WebGetHomeFeedDetailResp struct {
		ID                 int64              `json:"id,string"`            // 首页主键
		Status             int32              `json:"status"`               // 状态
		ResaleStatus       int32              `json:"resale_status"`        // 商品转卖状态
		ItemMallStatus     int32              `json:"item_mall_status"`     // 商品直购状态
		ItemID             string             `json:"item_id"`              // 商品ID
		ItemName           string             `json:"item_name"`            // 商品名称
		ItemSpecs          string             `json:"item_specs"`           // 商品规格
		ItemIconURL        string             `json:"item_icon_url"`        // 商品主图
		ImageInfos         []string           `json:"image_infos"`          // 商品图片
		ResaleCount        int32              `json:"resale_count"`         // 转卖数量
		SalesVolume        int32              `json:"sales_volume"`         // 销量
		WishCount          int32              `json:"wish_count"`           // 想要数量
		ViewCount          int32              `json:"view_count"`           // 浏览数量
		TrademarkID        string             `json:"trademark_id"`         // 商品品牌id
		IPID               string             `json:"ip_id"`                // 商品 ip_id
		TrademarkName      string             `json:"trademark_name"`       // 商品品牌名称
		IPName             string             `json:"ip_name"`              // 商品IP名称
		IPIconURL          string             `json:"ip_icon_url"`          // 商品IP图片
		Detail             string             `json:"detail"`               // 商品详情
		SalePrice          int64              `json:"sale_price"`           // 出售价格
		UserWishlistStatus int32              `json:"user_wishlist_status"` // 用户想要状态
		ResaleInstruction  *ResaleInstruction `json:"resale_instruction"`   // 转卖说明配置
	}
	ResaleInstruction struct {
		BannerUrl string `json:"banner_url"`
		Content   string `json:"content"`
	}
)

type (
	WebAddHomeFeedViewCountReq struct {
		ItemID string `form:"item_id" json:"item_id" binding:"required"` // 商品ID
	}
	WebAddHomeFeedViewCountResp struct {
	}
)

type (
	RefreshHomeFeedReq struct {
		ItemIds []string `form:"item_ids" json:"item_ids" binding:"required"`
	}
	RefreshHomeFeedResp struct {
	}
)

type (
	OpenSyncViewCountToDBReq struct {
	}

	OpenSyncViewCountToDBResp struct {
	}
)
