package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/apps/mall/dal/model"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/define/enums"
	"marketplace_service/apps/mall/repo"
	"marketplace_service/apps/mall/service/logic/config"
	"marketplace_service/apps/mall/service/warn"
	"marketplace_service/global"
	"marketplace_service/pkg/middleware"
	"marketplace_service/pkg/search"
	"marketplace_service/pkg/utils"
	"marketplace_service/pkg/utils/kafka_util"
	"marketplace_service/pkg/utils/snowflakeutl"
	"marketplace_service/third_party/tmt"
	"marketplace_service/third_party/wat"
	"marketplace_service/third_party/yc_open"
	"math"
	"time"
)

// CreateResaleOrderAndLock 创建转卖订单锁定出售单
func CreateResaleOrderAndLock(ctx context.Context, userId string, req *define.ResaleItemBuyReq, resaleListingsItems []*model.ResaleListingsItem) ([]*model.ResaleOrder, []*model.ResaleOrderItem, error) {
	resaleOrderList, resaleOrderItemList, err := buildResaleOrderInfo(ctx, userId, req, resaleListingsItems)
	if err != nil {
		log.Ctx(ctx).Errorf("创建转卖订单,构建订单信息失败, userId:%+v, req:%+v", userId, req)
		return nil, nil, err
	}
	resaleListingsItemIds := make([]int64, 0)
	for _, item := range resaleListingsItems {
		resaleListingsItemIds = append(resaleListingsItemIds, item.ID)
	}
	err = repo.ExecGenTx(ctx, func(tx context.Context) error {
		// 锁定挂单
		updateResaleListingsItem := &model.ResaleListingsItem{
			Status: enums.ResaleListingsItemStatusTrading.Val(),
		}
		rli := repo.Query(tx).ResaleListingsItem
		rliRowsAffected, err := repo.NewResaleListingsItemRepo(rli.WithContext(tx)).BatchUpdate(updateResaleListingsItem,
			search.NewWrapper().Where(rli.ID.In(resaleListingsItemIds...)).Where(rli.Status.Eq(enums.ResaleListingsItemStatusOnSale.Val())))
		if err != nil {
			log.Ctx(ctx).Errorf("转卖挂单购买,批量更新挂单物品失败, resaleListingsItemIds:%+v,err:%+v", utils.Obj2JsonStr(resaleListingsItemIds), err)
			return err
		}
		if rliRowsAffected != int64(len(resaleListingsItemIds)) {
			log.Ctx(ctx).Errorf("转卖挂单购买,批量更新挂单物品数量不一致, resaleListingsItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleListingsItemIds), rliRowsAffected)
			return repo.UpdateFail
		}
		err = repo.NewResaleOrderRepo(repo.Query(tx).ResaleOrder.WithContext(tx)).BatchSave(resaleOrderList, 100)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Ctx(ctx).Errorf("创建转卖订单失败, userId:%+v, req:%+v", userId, req)
		return nil, nil, global.CommonErr
	}
	return resaleOrderList, resaleOrderItemList, nil
}

// ResaleLockUserItem 锁定用户物品
func ResaleLockUserItem(ctx context.Context, items []*model.ResaleListingsItem, resaleOrders []*model.ResaleOrder, resaleOrderItems []*model.ResaleOrderItem) error {
	batchId := resaleOrders[0].BatchID
	var userItemIds []string
	for _, item := range items {
		userItemIds = append(userItemIds, item.UserItemID)
	}
	res, err := yc_open.ResaleUpdateUserItemStatus(ctx, batchId, yc_open.ResaleUpdateUserItemStatusLock, userItemIds)
	if err != nil {
		log.Ctx(ctx).Errorf("ResaleUpdateUserItemStatusFusion err:%+v, res:%+v", err, res)
	}
	switch res {
	case yc_open.ResaleUpdateUserItemStatusSuccess:
		log.Ctx(ctx).Infof("ResaleUpdateUserItemStatus Fusion Success, batchId:%v", batchId)
		// 3.2: 成功，修改转卖订单 (事务)
		err = ResaleLockUserItemSuccess(ctx, items, resaleOrders, resaleOrderItems)
		if err != nil {
			// 3.2.1: 失败，告警，取消订单，定时5分钟后释放锁定物品-云仓
			log.Ctx(ctx).Errorf("ResaleItemBuy,转卖商品购买,锁定用户物品成功，修改转卖订单失败,err:%+v", err)
			return err
		}
		return nil
	case yc_open.ResaleUpdateUserItemStatusFail:
		// 3.3: 失败，取消订单
		log.Ctx(ctx).Errorf("ResaleUpdateUserItemStatus Fusion Fail, batchId:%v, 错误信息: %v", batchId, err)
		resaleOrderIds := make([]int64, 0)
		for _, resaleOrder := range resaleOrders {
			resaleOrderIds = append(resaleOrderIds, resaleOrder.ID)
		}
		resaleOrderItemIds := make([]int64, 0)
		resaleListingsItemIds := make([]int64, 0)
		for _, resaleOrderItem := range resaleOrderItems {
			resaleOrderItemIds = append(resaleOrderItemIds, resaleOrderItem.ID)
			resaleListingsItemIds = append(resaleListingsItemIds, resaleOrderItem.ResaleListingsItemID)
		}
		err = CancelResaleOrder(ctx, resaleListingsItemIds, resaleOrderIds, resaleOrderItemIds)
		if err != nil {
			// 3.3.1: 失败，告警，取消订单失败
			warn.SendDefaultWarnMsg(ctx, "【转卖商品购买,锁定用户物品失败，取消订单失败】", fmt.Sprintf("转卖订单Ids: %+v, 错误信息: %v", utils.Obj2JsonStr(resaleOrderItemIds), err))
			log.Ctx(ctx).Errorf("ResaleItemBuy,转卖商品购买,锁定用户物品失败，取消订单失败,转卖订单Ids%+v, err:%+v", utils.Obj2JsonStr(resaleOrderItemIds), err)
			return err
		}
		return global.CommonErr
	default:
		// 3.4: 未知，告警，取消订单，定时5分钟后释放锁定物品-云仓
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("ResaleUpdateUserItemStatus Fusion Unknown Exception, batchId:%v, 错误信息: %v", batchId, err)
		return global.CommonErr
	}
}

// buildResaleOrderInfo 构建订单信息
func buildResaleOrderInfo(ctx context.Context, userId string, req *define.ResaleItemBuyReq, resaleListingsItems []*model.ResaleListingsItem) ([]*model.ResaleOrder, []*model.ResaleOrderItem, error) {
	resaleOrderList := make([]*model.ResaleOrder, 0)
	resaleOrderItemList := make([]*model.ResaleOrderItem, 0)
	userIDs := make([]string, 0)
	resaleListingsId := make([]int64, 0)
	resaleListingsMap := make(map[int64]*model.ResaleListings)
	resaleListingsItemMap := make(map[int64]*model.ResaleListingsItem)
	for _, info := range req.ResaleListingsItemInfo {
		resaleListingsIdInt64, _ := utils.Str2Int64(info.ResaleListingsId)
		resaleListingsId = append(resaleListingsId, resaleListingsIdInt64)
	}

	to := repo.GetQuery().ResaleListings
	resaleListingList, err := repo.NewResaleListingsRepo(to.WithContext(ctx)).SelectList(search.NewWrapper().Where(to.ID.In(resaleListingsId...)))
	if err != nil {
		log.Ctx(ctx).Errorf("buildResaleOrder,查询挂单列表失败, resaleListingsId:%+v,err:%+v", utils.Obj2JsonStr(resaleListingsId), err)
		return nil, nil, global.CommonErr
	}

	for _, item := range resaleListingList {
		resaleListingsMap[item.ID] = item
		userIDs = append(userIDs, item.SellerID)
	}
	for _, item := range resaleListingsItems {
		resaleListingsItemMap[item.ID] = item
	}
	userIDs = append(userIDs, userId)
	userDetailMap, err := GetUserDetailMap(ctx, userIDs...)
	if err != nil {
		log.Ctx(ctx).Errorf("buildResaleOrder,查询最近成交记录失败,获取用户信息失败, req:%+v,err:%+v", utils.Obj2JsonStr(req), err)
		return nil, nil, global.CommonErr
	}
	batchId := req.BatchId
	for _, item := range req.ResaleListingsItemInfo {
		resaleListingsIdInt64, _ := utils.Str2Int64(item.ResaleListingsId)
		resaleListings := resaleListingsMap[resaleListingsIdInt64]
		quantity := int32(len(item.ResaleListingsItemIds))
		resaleOrder := &model.ResaleOrder{
			ID:               snowflakeutl.GenerateID(),
			Status:           enums.ResaleOrderStatusUnPaid.Val(),
			BuyerID:          userId,
			SellerID:         resaleListings.SellerID,
			SkuID:            resaleListings.SkuID,
			ItemID:           resaleListings.ItemID,
			ItemName:         resaleListings.ItemName,
			ItemIconURL:      resaleListings.ItemIconURL,
			ItemSpecs:        resaleListings.ItemSpecs,
			ResaleListingsID: resaleListings.ID,
			PaymentStatus:    enums.ResaleOrderStatusUnPaid.Val(),
			SalePrice:        resaleListings.SalePrice,
			Quantity:         quantity,
			Terminal:         ctx.Value(middleware.ClientType).(string),
			AppVersion:       ctx.Value(middleware.AppVersion).(string),
			BatchID:          batchId,
		}
		if userDetail, ok := userDetailMap[userId]; ok {
			resaleOrder.BuyerPhone = userDetail.MobilePhone
		}
		if userDetail, ok := userDetailMap[resaleListings.SellerID]; ok {
			resaleOrder.SellerPhone = userDetail.MobilePhone
		}
		resaleOrder.TotalAmount = resaleOrder.SalePrice * int64(quantity)
		feeRatio, _ := config.GetInt(ctx, constant.ResaleFee, 3)
		// 使用浮点数计算并向上取整
		fee := int64(math.Ceil(float64(resaleOrder.TotalAmount*int64(feeRatio)) / 100))
		if fee == 0 {
			fee = int64(1 * len(item.ResaleListingsItemIds))
		}
		resaleOrder.TotalFee = fee
		resaleOrderList = append(resaleOrderList, resaleOrder)
		resaleOrderItems := make([]*model.ResaleOrderItem, 0)
		for _, resaleListingsItemId := range item.ResaleListingsItemIds {
			resaleListingsItemIdInt64, _ := utils.Str2Int64(resaleListingsItemId)
			resaleListingsItem := resaleListingsItemMap[resaleListingsItemIdInt64]
			resaleOrderItem := &model.ResaleOrderItem{
				ID:                   snowflakeutl.GenerateID(),
				Status:               enums.ResaleOrderStatusUnPaid.Val(),
				ResaleOrderID:        resaleOrder.ID,
				ResaleListingsID:     resaleListings.ID,
				ResaleListingsItemID: resaleListingsItem.ID,
				SellerID:             resaleListings.SellerID,
				ItemID:               resaleListings.ItemID,
				UserItemID:           resaleListingsItem.UserItemID,
				SalePrice:            resaleListingsItem.SalePrice,
			}
			// 使用 math.Ceil 实现向上取整
			orderItemFee := int64(math.Ceil(float64(resaleListingsItem.SalePrice*int64(feeRatio)) / 100))
			if orderItemFee == 0 {
				orderItemFee = 1
			}
			resaleOrderItem.Fee = orderItemFee
			resaleOrderItems = append(resaleOrderItems, resaleOrderItem)
			resaleOrderItemList = append(resaleOrderItemList, resaleOrderItem)
		}
		resaleOrder.ResaleOrderItem = resaleOrderItems
	}
	return resaleOrderList, resaleOrderItemList, nil
}

// ResaleLockUserItemSuccess 锁定用户物品成功
func ResaleLockUserItemSuccess(ctx context.Context, resaleListingsItems []*model.ResaleListingsItem, resaleOrders []*model.ResaleOrder, resaleOrderItems []*model.ResaleOrderItem) error {
	resaleListingsItemIds := make([]int64, 0)
	resaleOrderIds := make([]int64, 0)
	resaleOrderItemIds := make([]int64, 0)
	for _, item := range resaleListingsItems {
		resaleListingsItemIds = append(resaleListingsItemIds, item.ID)
	}
	for _, item := range resaleOrders {
		resaleOrderIds = append(resaleOrderIds, item.ID)
	}
	for _, item := range resaleOrderItems {
		resaleOrderItemIds = append(resaleOrderItemIds, item.ID)
	}
	err := repo.ExecGenTx(ctx, func(tx context.Context) error {
		updateResaleOrder := &model.ResaleOrder{
			Status: enums.ResaleOrderStatusItemLocked.Val(),
		}
		ro := repo.Query(tx).ResaleOrder
		roRowsAffected, err := repo.NewResaleOrderRepo(ro.WithContext(tx)).BatchUpdate(updateResaleOrder,
			search.NewWrapper().Where(ro.ID.In(resaleOrderIds...), ro.Status.Eq(enums.ResaleOrderStatusUnPaid.Val())))
		if err != nil {
			log.Ctx(ctx).Errorf("转卖挂单购买,批量更新订单失败, resaleOrderIds:%+v,err:%+v", utils.Obj2JsonStr(resaleOrderIds), err)
			return err
		}
		if roRowsAffected != int64(len(resaleOrderIds)) {
			log.Ctx(ctx).Errorf("转卖挂单购买,批量更新订单失败, resaleOrderIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderIds), roRowsAffected)
			return repo.UpdateFail
		}

		updateResaleOrderItem := &model.ResaleOrderItem{
			Status: enums.ResaleOrderStatusItemLocked.Val(),
		}
		roi := repo.Query(tx).ResaleOrderItem
		roiRowsAffected, err := repo.NewResaleOrderItemRepo(roi.WithContext(tx)).BatchUpdate(updateResaleOrderItem,
			search.NewWrapper().Where(roi.ID.In(resaleOrderItemIds...), roi.Status.Eq(enums.ResaleOrderStatusUnPaid.Val())))
		if err != nil {
			log.Ctx(ctx).Errorf("转卖挂单购买,批量更新订单子项失败, resaleOrderItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderItemIds), roiRowsAffected)
			return err
		}
		if roiRowsAffected != int64(len(resaleOrderItemIds)) {
			log.Ctx(ctx).Errorf("转卖挂单购买,批量更新订单失败, resaleOrderItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderItemIds), roRowsAffected)
			return repo.UpdateFail
		}
		return nil
	})
	return err
}

// CancelResaleOrder 取消订单
func CancelResaleOrder(tx context.Context, resaleListingsItemIds []int64, resaleOrderIds []int64, resaleOrderItemIds []int64) error {
	if resaleListingsItemIds != nil && len(resaleListingsItemIds) > 0 {
		updateResaleListingsItem := &model.ResaleListingsItem{
			Status: enums.ResaleListingsItemStatusOnSale.Val(),
		}
		rli := repo.Query(tx).ResaleListingsItem
		rliRowsAffected, err := repo.NewResaleListingsItemRepo(rli.WithContext(tx)).BatchUpdate(updateResaleListingsItem,
			search.NewWrapper().Where(rli.ID.In(resaleListingsItemIds...)).Where(rli.Status.Eq(enums.ResaleListingsItemStatusTrading.Val())))
		if err != nil {
			log.Ctx(tx).Errorf("取消订单,批量更新挂单物品失败, resaleListingsItemIds:%+v,err:%+v", utils.Obj2JsonStr(resaleListingsItemIds), err)
		}
		if rliRowsAffected != int64(len(resaleListingsItemIds)) {
			log.Ctx(tx).Errorf("取消订单,批量更新挂单物品数量不一致, resaleListingsItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleListingsItemIds), rliRowsAffected)
		}
	}

	updateResaleOrder := &model.ResaleOrder{
		Status: enums.ResaleOrderStatusCanceled.Val(),
	}
	ro := repo.Query(tx).ResaleOrder
	roRowsAffected, err := repo.NewResaleOrderRepo(ro.WithContext(tx)).BatchUpdate(updateResaleOrder,
		search.NewWrapper().Where(ro.ID.In(resaleOrderIds...), ro.Status.In(enums.ResaleOrderStatusUnPaid.Val(), enums.ResaleOrderStatusItemLocked.Val())))
	if err != nil {
		log.Ctx(tx).Errorf("取消订单,批量更新订单失败, resaleOrderIds:%+v,err:%+v", utils.Obj2JsonStr(resaleOrderIds), err)
	}
	if roRowsAffected != int64(len(resaleOrderIds)) {
		log.Ctx(tx).Errorf("取消订单,批量更新订单失败, resaleOrderIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderIds), roRowsAffected)
	}

	updateResaleOrderItem := &model.ResaleOrderItem{
		Status: enums.ResaleOrderStatusCanceled.Val(),
	}
	roi := repo.Query(tx).ResaleOrderItem
	roiRowsAffected, err := repo.NewResaleOrderItemRepo(roi.WithContext(tx)).BatchUpdate(updateResaleOrderItem,
		search.NewWrapper().Where(roi.ID.In(resaleOrderItemIds...), roi.Status.In(enums.ResaleOrderStatusUnPaid.Val(), enums.ResaleOrderStatusItemLocked.Val())))
	if err != nil {
		log.Ctx(tx).Errorf("取消订单,批量更新订单子项失败, resaleOrderItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderItemIds), roiRowsAffected)
	}
	if roiRowsAffected != int64(len(resaleOrderItemIds)) {
		log.Ctx(tx).Errorf("取消订单,批量更新订单失败, resaleOrderItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderItemIds), roiRowsAffected)
	}
	return nil
}

// ResaleOrderPay 订单支付
func ResaleOrderPay(ctx context.Context, order *model.ResaleOrder) error {
	balanceFrom := "spdb"
	tradePool := constant.TradePool
	feePool := constant.FeePool
	spdbPool := constant.SpdbPool
	transferType := int32(211)
	payer := int32(4)
	// 按订单维度支付
	buyerID := order.BuyerID
	saleUserID := order.SellerID
	orderId := utils.StrVal(order.ID)
	income := order.TotalAmount - order.TotalFee
	extends := &wat.SpdbC2cBalancePayFormExtends{
		FromUserId:    order.BuyerID,
		BalanceFrom:   balanceFrom,
		ResaleOrderId: orderId,
	}
	dataFrom := make([]*wat.SpdbC2cBalancePayFormData, 4)
	dataFrom[0] = &wat.SpdbC2cBalancePayFormData{
		PoolWalletId: &tradePool,
		FromUserId:   &buyerID,
		ToWalletId:   &tradePool,
		Amount:       &order.TotalAmount,
		Extends:      extends,
	}
	dataFrom[1] = &wat.SpdbC2cBalancePayFormData{
		PoolWalletId: &tradePool,
		FromWalletId: &tradePool,
		ToWalletId:   &feePool,
		Amount:       &order.TotalFee,
		Extends:      extends,
	}
	dataFrom[2] = &wat.SpdbC2cBalancePayFormData{
		PoolWalletId: &tradePool,
		FromWalletId: &tradePool,
		ToUserId:     &saleUserID,
		Amount:       &income,
		Extends:      extends,
	}
	dataFrom[3] = &wat.SpdbC2cBalancePayFormData{
		PoolWalletId: &tradePool,
		FromUserId:   &saleUserID,
		ToWalletId:   &spdbPool,
		Amount:       &income,
		Extends:      extends,
		TransferType: &transferType,
		Payer:        &payer,
	}
	form := &wat.SpdbC2cBalancePayForm{
		BuyUserId:  order.BuyerID,
		SaleUserId: order.SellerID,
		Amount:     order.TotalAmount,
		Extends:    extends,
		Fee:        order.TotalFee,
		DataFrom:   dataFrom,
	}
	log.Ctx(ctx).Infof("ResaleOrderPay SpdbC2cBalancePay form:%+v", utils.Obj2JsonStr(form))
	res, err := wat.SpdbC2cBalancePay(ctx, form)
	if err != nil {
		log.Ctx(ctx).Errorf("ResaleOrderPay SpdbC2cBalancePay form:%+v, err:%+v", utils.Obj2JsonStr(form), err)
	}
	switch res {
	case wat.SpdbC2cBalancePaySuccess:
		log.Ctx(ctx).Infof("ResaleOrderPay SpdbC2cBalancePay Success, form:%+v", utils.Obj2JsonStr(form))
		// 4.1: 支付成功，转卖订单修改
		err = ResaleOrderPaySuccess(ctx, order)
		if err != nil {
			// 4.1.1: 失败，告警，支付成功，转卖订单修改失败
			warn.SendDefaultWarnMsg(ctx, "【转卖订单支付成功,转卖订单修改失败】", fmt.Sprintf("orderId: %+v, 错误信息: %v", order.ID, err))
			log.Ctx(ctx).Errorf("ResaleItemBuy,支付成功，转卖订单修改失败 form:%+v, err:%+v", utils.Obj2JsonStr(form), err)
			return err
		}
		return nil
	case wat.SpdbC2cBalancePayFail:
		// 4.2: 支付失败，释放锁定物品-云仓，取消订单
		log.Ctx(ctx).Errorf("ResaleOrderPay SpdbC2cBalancePay Fail, form:%+v", utils.Obj2JsonStr(form))
		resaleOrderItemIds := make([]int64, 0)
		resaleListingsItemIds := make([]int64, 0)
		var userItemIds []string
		for _, item := range order.ResaleOrderItem {
			resaleOrderItemIds = append(resaleOrderItemIds, item.ID)
			resaleListingsItemIds = append(resaleListingsItemIds, item.ResaleListingsItemID)
			userItemIds = append(userItemIds, item.UserItemID)
		}
		resaleUpdateUserItemStatusRes, err := yc_open.ResaleUpdateUserItemStatus(ctx, order.BatchID, yc_open.ResaleUpdateUserItemStatusRelease, userItemIds)
		if err != nil {
			log.Ctx(ctx).Errorf("ResaleOrderPay SpdbC2cBalancePay ResaleUpdateUserItemStatus Release orderId: %+v, err:%+v", orderId, err)
		}
		if resaleUpdateUserItemStatusRes != yc_open.ResaleUpdateUserItemStatusSuccess {
			log.Ctx(ctx).Errorf("ResaleOrderPay SpdbC2cBalancePay ResaleUpdateUserItemStatus Release  orderId: %+v, res:%+v", orderId, resaleUpdateUserItemStatusRes)
			// 4.2.1: 支付失败，云仓释放锁定物品失败
			warn.SendDefaultWarnMsg(ctx, "【转卖订单支付失败,云仓释放锁定物品失败】", fmt.Sprintf("orderId: %+v, userItemIds: %v", order.ID, utils.Obj2JsonStr(userItemIds)))
			return global.CommonErr
		}
		err = CancelResaleOrder(ctx, resaleListingsItemIds, []int64{order.ID}, resaleOrderItemIds)
		if err != nil {
			// 4.2.2: 支付失败，云仓释放锁定物品成功，取消订单失败
			log.Ctx(ctx).Errorf("ResaleItemBuy,CancelResaleOrder 转卖订单支付失败,云仓释放锁定物品成功，取消订单失败, orderId: %+v,err:%+v", orderId, err)
			warn.SendDefaultWarnMsg(ctx, "【转卖订单支付失败,云仓释放锁定物品成功，取消订单失败】", fmt.Sprintf("orderId: %+v, userItemIds: %v", order.ID, utils.Obj2JsonStr(userItemIds)))
			return err
		}
		return global.CommonErr
	default:
		// 4.3: 未知，定时查询处理正确的支付状态
		// wat响应超时，未知异常
		log.Ctx(ctx).Errorf("ResaleOrderPay SpdbC2cBalancePay Unknown Exception, orderId: %+v, form:%+v", orderId, utils.Obj2JsonStr(form))
		warn.SendDefaultWarnMsg(ctx, "【转卖订单支付未知响应】", fmt.Sprintf("orderId: %+v", order.ID))
		return global.CommonErr
	}
}

// ResaleOrderPaySuccess 订单支付成功
func ResaleOrderPaySuccess(ctx context.Context, order *model.ResaleOrder) error {
	resaleOrderItemIds := make([]int64, 0)
	for _, orderItem := range order.ResaleOrderItem {
		resaleOrderItemIds = append(resaleOrderItemIds, orderItem.ID)
	}
	err := repo.ExecGenTx(ctx, func(tx context.Context) error {
		now := time.Now()
		updateResaleOrder := &model.ResaleOrder{
			Status:        enums.ResaleOrderStatusPaid.Val(),
			PaymentStatus: enums.PaymentStatusPaid.Val(),
			PayAmount:     order.TotalAmount,
			PaymentAt:     &now,
		}
		ro := repo.Query(tx).ResaleOrder
		err := repo.NewResaleOrderRepo(ro.WithContext(tx)).Update(updateResaleOrder,
			search.NewWrapper().Where(ro.ID.In(order.ID), ro.Status.Eq(enums.ResaleOrderStatusItemLocked.Val())))
		if err != nil {
			log.Ctx(ctx).Errorf("订单支付成功,更新ResaleOrder失败, orderId:%+v,err:%+v", order.ID, err)
			return err
		}

		updateResaleOrderItem := &model.ResaleOrderItem{
			Status: enums.ResaleOrderStatusPaid.Val(),
		}
		roi := repo.Query(tx).ResaleOrderItem
		roiRowsAffected, err := repo.NewResaleOrderItemRepo(roi.WithContext(tx)).BatchUpdate(updateResaleOrderItem,
			search.NewWrapper().Where(roi.ID.In(resaleOrderItemIds...), roi.Status.Eq(enums.ResaleOrderStatusItemLocked.Val())))
		if err != nil {
			log.Ctx(ctx).Errorf("订单支付成功,批量更新ResaleOrderItem失败, resaleOrderItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderItemIds), roiRowsAffected)
			return err
		}
		if roiRowsAffected != int64(len(resaleOrderItemIds)) {
			log.Ctx(ctx).Errorf("订单支付成功,批量更新ResaleOrderItem数量不一致, resaleOrderItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderItemIds), roiRowsAffected)
			return errors.New("update fail,RowsAffected Unequal")
		}
		return nil
	})
	return err
}

// ResaleOrderUserItemTransfer 订单物品转移
func ResaleOrderUserItemTransfer(ctx context.Context, orderId int64) error {
	ro := repo.GetQuery().ResaleOrder
	order, err := repo.NewResaleOrderRepo(ro.WithContext(ctx).Preload(ro.ResaleOrderItem)).SelectOne(search.NewWrapper().Where(ro.ID.Eq(orderId), ro.Status.Eq(enums.ResaleOrderStatusPaid.Val())))
	if err != nil {
		log.Ctx(ctx).Errorf("ResaleOrderUserItemTransfer ResaleOrder 查询转卖订单失败, orderId：%+v, err: %+v", orderId, err)
		return errors.Wrap(err, "查询转卖订单失败")
	}
	resaleItems := order.ResaleOrderItem
	if len(resaleItems) == 0 {
		log.Ctx(ctx).Error("ResaleOrderUserItemTransfer ResaleOrderItem 未查询到转卖订单详情")
		return errors.New("未查询到转卖订单详情")
	}
	var userItemIds []string
	for _, item := range resaleItems {
		userItemIds = append(userItemIds, item.UserItemID)
	}
	openUserInfo, err := tmt.QueryUserOpenById(ctx, order.BuyerID)
	if err != nil || openUserInfo.OpenInfo == nil {
		log.Ctx(ctx).Error("ResaleOrderUserItemTransfer 未查询到用户openUserId")
		return errors.New("未查询到用户openUserId")
	}
	// 开始转移
	err = ResaleOrderUserItemTransferBegin(ctx, order, resaleItems)
	if err != nil {
		log.Ctx(ctx).Errorf("ResaleOrderUserItemTransfer ResaleOrderUserItemTransferBegin,orderId：%+v, err: %+v", orderId, err)
		return err
	}
	// 转移处理
	err = ResaleOrderUserItemTransferHandler(ctx, order, openUserInfo.OpenInfo.OpenUserId, userItemIds)
	if err != nil {
		log.Ctx(ctx).Errorf("ResaleOrderUserItemTransfer ResaleOrderUserItemTransferHandler,orderId：%+v, err: %+v", orderId, err)
		return err
	}
	return nil
}

// ResaleOrderUserItemTransferHandler 订单物品转移处理
func ResaleOrderUserItemTransferHandler(ctx context.Context, order *model.ResaleOrder, openUserID string, userItemIds []string) error {
	orderId := order.ID
	resaleItems := order.ResaleOrderItem
	form := &yc_open.UserItemTransferForm{
		UserItemIds:  userItemIds,
		ToOpenUserId: openUserID,
		BuyPrice:     order.SalePrice,
	}
	if order.PaymentAt != nil {
		utcPaymentAt := order.PaymentAt.Add(time.Hour * -8)
		form.BuyTime = utils.GetDateTimeMsFormatStr(utcPaymentAt)
	} else {
		now := time.Now().Add(time.Hour * -8)
		form.BuyTime = utils.GetDateTimeMsFormatStr(now)
	}
	res, err := yc_open.UserItemTransfer(ctx, form)
	if err != nil {
		log.Ctx(ctx).Errorf("ResaleOrderUserItemTransfer UserItemTransfer orderId:%+v,err:%+v, res:%+v", orderId, err, res)
	}
	switch res.Status {
	case yc_open.UserItemTransferSuccess:
		log.Ctx(ctx).Infof("ResaleOrderUserItemTransfer UserItemTransfer Success orderId:%+v, res:%v", orderId, res)
		// 5.1: 成功，出售单修改，转卖订单修改 (事务)
		err = ResaleOrderUserItemTransferSuccess(ctx, order, resaleItems, res.Data)
		if err != nil {
			// 5.1.1: 告警物品转移成功，转卖订单修改失败
			log.Ctx(ctx).Errorf("ResaleOrderUserItemTransfer,物品转移成功，转卖订单修改失败,orderId:%+v,err:%+v", orderId, err)
			warn.SendDefaultWarnMsg(ctx, "【转卖订单物品转移成功,订单修改失败】", fmt.Sprintf("orderId: %+v, userItemIds: %v", order.ID, utils.Obj2JsonStr(userItemIds)))
			return err
		}
		return nil
	case yc_open.UserItemTransferFail:
		// 5.2: 物品转移失败，告警，定时重试
		log.Ctx(ctx).Errorf("ResaleOrderUserItemTransfer Fail,物品转移失败,orderId:%+v,err:%+v", orderId, err)
		warn.SendDefaultWarnMsg(ctx, "【转卖订单物品转移失败】", fmt.Sprintf("orderId: %+v, userItemIds: %v", order.ID, utils.Obj2JsonStr(userItemIds)))
		return global.CommonErr
	default:
		// 5.3: 未知，告警，定时重试
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("ResaleOrderUserItemTransfer Unknown Exception,orderId:%+v, 错误信息: %v", orderId, err)
		warn.SendDefaultWarnMsg(ctx, "【转卖订单物品转移未知响应】", fmt.Sprintf("orderId: %+v", order.ID))
		return global.CommonErr
	}
}

// ResaleOrderUserItemTransferBegin 开始订单物品转移
func ResaleOrderUserItemTransferBegin(ctx context.Context, order *model.ResaleOrder, orderItems []*model.ResaleOrderItem) error {
	resaleOrderItemIds := make([]int64, 0)
	for _, orderItem := range orderItems {
		resaleOrderItemIds = append(resaleOrderItemIds, orderItem.ID)
	}
	now := time.Now()
	err := repo.ExecGenTx(ctx, func(tx context.Context) error {
		updateResaleOrder := &model.ResaleOrder{
			Status:     enums.ResaleOrderStatusTransfer.Val(),
			TransferAt: &now,
		}
		ro := repo.Query(tx).ResaleOrder
		err := repo.NewResaleOrderRepo(ro.WithContext(tx)).Update(updateResaleOrder,
			search.NewWrapper().Where(ro.ID.In(order.ID), ro.Status.Eq(enums.ResaleOrderStatusPaid.Val())))
		if err != nil {
			log.Ctx(ctx).Errorf("订单支付成功,开始订单物品转移失败, orderId:%+v,err:%+v", order.ID, err)
			return err
		}

		updateResaleOrderItem := &model.ResaleOrderItem{
			Status: enums.ResaleOrderStatusTransfer.Val(),
		}
		roi := repo.Query(tx).ResaleOrderItem
		roiRowsAffected, err := repo.NewResaleOrderItemRepo(roi.WithContext(tx)).BatchUpdate(updateResaleOrderItem,
			search.NewWrapper().Where(roi.ID.In(resaleOrderItemIds...), roi.Status.Eq(enums.ResaleOrderStatusPaid.Val())))
		if err != nil {
			log.Ctx(ctx).Errorf("订单支付成功,开始订单物品转移,批量更新ResaleOrderItem失败, resaleOrderItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderItemIds), roiRowsAffected)
			return err
		}
		if roiRowsAffected != int64(len(resaleOrderItemIds)) {
			log.Ctx(ctx).Errorf("订单支付成功,开始订单物品转移,批量更新ResaleOrderItem数量不一致, resaleOrderItemIds:%+v,rowsAffected:%+v", utils.Obj2JsonStr(resaleOrderItemIds), roiRowsAffected)
			return errors.New("update fail,RowsAffected Unequal")
		}
		return nil
	})
	return err
}

// ResaleOrderUserItemTransferSuccess 订单物品转移成功
func ResaleOrderUserItemTransferSuccess(ctx context.Context, order *model.ResaleOrder, orderItems []*model.ResaleOrderItem, userItemTransferRes []*yc_open.UserItemTransferData) error {
	resaleOrderItemIds := make([]int64, 0)
	for _, orderItem := range orderItems {
		resaleOrderItemIds = append(resaleOrderItemIds, orderItem.ID)
	}
	originUserItemId2New := make(map[string]string)
	for _, res := range userItemTransferRes {
		originUserItemId2New[res.OriginUserItemId] = res.UserItemId
	}
	err := repo.ExecGenTx(ctx, func(tx context.Context) error {
		now := time.Now()
		updateResaleOrder := &model.ResaleOrder{
			Status:     enums.ResaleOrderStatusCompleted.Val(),
			FinishedAt: &now,
		}
		ro := repo.Query(tx).ResaleOrder
		err := repo.NewResaleOrderRepo(ro.WithContext(tx)).Update(updateResaleOrder,
			search.NewWrapper().Where(ro.ID.In(order.ID), ro.Status.Eq(enums.ResaleOrderStatusTransfer.Val())))
		if err != nil {
			log.Ctx(ctx).Errorf("订单支付成功,开始订单物品转移失败, orderId:%+v,err:%+v", order.ID, err)
			return err
		}

		for _, orderItem := range orderItems {
			ok := originUserItemId2New[orderItem.UserItemID]
			if ok == "" {
				return errors.New(fmt.Sprintf("未查询到用户物品ID:%+v", orderItem.UserItemID))
			}
			updateResaleOrderItem := &model.ResaleOrderItem{
				Status:        enums.ResaleOrderStatusCompleted.Val(),
				NewUserItemID: originUserItemId2New[orderItem.UserItemID],
			}
			roi := repo.Query(tx).ResaleOrderItem
			err = repo.NewResaleOrderItemRepo(roi.WithContext(tx)).Update(updateResaleOrderItem,
				search.NewWrapper().Where(roi.ID.Eq(orderItem.ID), roi.Status.Eq(enums.ResaleOrderStatusTransfer.Val())))
			if err != nil {
				log.Ctx(ctx).Errorf("订单支付成功,开始订单物品转移,更新ResaleOrderItem失败, orderItemId:%+v,err:%+v", orderItem.ID, err)
				return err
			}

			updateResaleListingsItem := &model.ResaleListingsItem{
				Status:            enums.ResaleListingsItemStatusSold.Val(),
				BuyerID:           order.BuyerID,
				ResaleOrderID:     &orderItem.ResaleOrderID,
				ResaleOrderItemID: &orderItem.ID,
				TradeAt:           &now,
				Fee:               orderItem.Fee,
			}
			rli := repo.Query(tx).ResaleListingsItem
			err = repo.NewResaleListingsItemRepo(rli.WithContext(tx)).Update(updateResaleListingsItem,
				search.NewWrapper().Where(rli.ID.Eq(orderItem.ResaleListingsItemID), rli.Status.Eq(enums.ResaleListingsItemStatusTrading.Val())))
			if err != nil {
				log.Ctx(ctx).Errorf("订单支付成功,开始订单物品转移,更新ResaleListingsItem失败, orderItemId:%+v,err:%+v", orderItem.ID, err)
				return err
			}
		}

		rl := repo.Query(tx).ResaleListings
		err = repo.NewResaleListingsRepo(rl.WithContext(tx)).UpdateSoldQuantityWithTx(ctx, order.ResaleListingsID, order.Quantity, order.TotalAmount-order.TotalFee, order.TotalFee)
		if err != nil {
			log.Ctx(ctx).Errorf("订单支付成功,开始订单物品转移,更新ResaleListings失败, resaleListingsId:%+v,err:%+v", order.ResaleListingsID, err)
			return err
		}
		return nil
	})
	return err
}

// GetOrderMaxSalePriceMap 获取历史最高成交价 map（如果没有交易则兜底为转卖商品的市场公允价）
func GetOrderMaxSalePriceMap(ctx context.Context, itemIDs []string) (map[string]int64, error) {
	resultMap := make(map[string]int64)
	cacheKeyPrefix := "marketplace_service:resale_item:max_sale_price:"
	noCacheItemIDs := make([]string, 0)
	for _, itemID := range itemIDs {
		cacheVal, err := global.REDIS.Get(ctx, cacheKeyPrefix+itemID).Int64()
		if err == nil && cacheVal > 0 {
			resultMap[itemID] = cacheVal
		} else {
			noCacheItemIDs = append(noCacheItemIDs, itemID)
		}
	}
	if len(noCacheItemIDs) == 0 {
		return resultMap, nil
	}

	type itemMaxPrice struct {
		ItemID   string `gorm:"column:item_id"`
		MaxPrice int64  `gorm:"column:sale_price"` // 单位为分
	}
	var orderResults []*itemMaxPrice
	roSchema := repo.GetQuery().ResaleOrder
	db := roSchema.WithContext(ctx).UnderlyingDB()
	err := db.Where("item_id IN ?", noCacheItemIDs).
		Select("item_id, MAX(sale_price) AS sale_price").
		Where("status = ?", enums.ResaleOrderStatusCompleted.Val()).
		Group("item_id").
		Find(&orderResults).
		Error
	if err != nil {
		return nil, err
	}

	for _, orderResult := range orderResults {
		resultMap[orderResult.ItemID] = orderResult.MaxPrice
	}
	var noOrderItemIDs []string
	for _, itemID := range noCacheItemIDs {
		if _, ok := resultMap[itemID]; !ok {
			noOrderItemIDs = append(noOrderItemIDs, itemID)
		}
	}

	if len(noOrderItemIDs) > 0 {
		// 用公允价兜底
		riSchema := repo.GetQuery().ResaleItem
		riWrapper := search.NewWrapper().
			Select(riSchema.ItemID, riSchema.MarketPrice).
			Where(riSchema.ItemID.In(noOrderItemIDs...))
		resaleItems, err := repo.NewResaleItemRepo(riSchema.WithContext(ctx)).SelectList(riWrapper)
		if err != nil {
			return nil, err
		}
		for _, item := range resaleItems {
			resultMap[item.ItemID] = item.MarketPrice
		}
	}

	// 缓存
	for _, itemID := range noCacheItemIDs {
		maxSalePrice := resultMap[itemID]
		cacheKey := cacheKeyPrefix + itemID
		global.REDIS.Set(ctx, cacheKey, maxSalePrice, time.Second*10)
	}

	return resultMap, nil
}

// ReleaseUserItems 释放用户物品并更新订单状态
func ReleaseUserItems(ctx context.Context, resaleOrders []*model.ResaleOrder) error {
	for _, order := range resaleOrders {
		// 收集所有需要释放的用户物品ID
		resaleOrderUserItemIds := make([]string, 0, len(order.ResaleOrderItem))
		resaleOrderItemIds := make([]int64, 0, len(order.ResaleOrderItem))
		resaleListingsItemIds := make([]int64, 0, len(order.ResaleOrderItem))
		for _, orderItem := range order.ResaleOrderItem {
			resaleOrderUserItemIds = append(resaleOrderUserItemIds, orderItem.UserItemID)
			resaleOrderItemIds = append(resaleOrderItemIds, orderItem.ID)
			resaleListingsItemIds = append(resaleListingsItemIds, orderItem.ResaleListingsItemID)
		}

		// 1:订单创建成功，锁定物品失败，订单卡在待支付，直接释放物品
		// 2:锁定物品成功，响应未知，订单卡在待支付，直接释放物品
		// 尝试释放物品
		resaleUpdateUserItemStatusRes, err := yc_open.ResaleUpdateUserItemStatus(ctx, order.BatchID, yc_open.ResaleUpdateUserItemStatusRelease, resaleOrderUserItemIds)
		if err != nil {
			log.Ctx(ctx).Errorf("ReleaseUserItems ResaleUpdateUserItemStatus Release err:%+v", err)
		}
		log.Ctx(ctx).Infof("ReleaseUserItems ResaleUpdateUserItemStatus Release res:%+v", resaleUpdateUserItemStatusRes)

		err = repo.ExecGenTx(ctx, func(tx context.Context) error {
			err = CancelResaleOrder(tx, resaleListingsItemIds, []int64{order.ID}, resaleOrderItemIds)
			if err != nil {
				// 云仓释放锁定物品成功，取消订单失败
				log.Ctx(ctx).Errorf("云仓释放锁定物品成功，取消订单失败, orderId: %+v,err:%+v", order.ID, err)
				warn.SendDefaultWarnMsg(ctx, "【查询待支付订单,云仓释放锁定物品成功，取消订单失败】", fmt.Sprintf("orderId: %+v, userItemIds: %v", order.ID, utils.Obj2JsonStr(resaleOrderUserItemIds)))
				return err
			}
			return nil
		})
		if err != nil {
			log.Ctx(ctx).Errorf("释放用户物品更新订单失败, orderId:%+v, err:%+v", order.ID, err)
			continue
		}
	}
	return nil
}

// LockedHandler 物品异常锁定订单处理
func LockedHandler(ctx context.Context, resaleOrders []*model.ResaleOrder) error {
	for _, order := range resaleOrders {
		// 收集所有需要释放的用户物品ID
		resaleOrderUserItemIds := make([]string, 0, len(order.ResaleOrderItem))
		resaleOrderItemIds := make([]int64, 0, len(order.ResaleOrderItem))
		resaleListingsItemIds := make([]int64, 0, len(order.ResaleOrderItem))
		for _, orderItem := range order.ResaleOrderItem {
			resaleOrderUserItemIds = append(resaleOrderUserItemIds, orderItem.UserItemID)
			resaleOrderItemIds = append(resaleOrderItemIds, orderItem.ID)
			resaleListingsItemIds = append(resaleListingsItemIds, orderItem.ResaleListingsItemID)
		}

		// 1:锁定物品成功，付款失败，订单卡在物品锁定成功，直接释放物品
		// 2:锁定物品成功，付款成功，订单修改已支付失败，卡在释放成功，查询真实支付状态，付款后流程/直接释放物品
		// 调用wat查询真实支付状态
		payStatusFlag, err := wat.GetSpdbPayStatus(ctx, utils.StrVal(order.ID))
		if err != nil {
			log.Ctx(ctx).Errorf("LockedHandler wat.GetPayStatus err:%+v", err)
			continue
		}
		if payStatusFlag {
			// 付款成功
			err = ResaleOrderPaySuccess(ctx, order)
			if err != nil {
				// 失败，告警，支付成功，转卖订单修改失败
				log.Ctx(ctx).Errorf("ResaleItemBuy,支付成功，转卖订单修改失败 order:%+v, err:%+v", order.ID, err)
				warn.SendDefaultWarnMsg(ctx, "【转卖订单支付成功,转卖订单修改失败(定时补偿)】", fmt.Sprintf("orderId: %+v, 错误信息: %v", order.ID, err))
				continue
			}
			//发送Kafka，物品转移
			resaleItemTransfer := &define.ResaleItemTransfer{ID: order.ID}
			_ = kafka_util.SendMsg(ctx, constant.ResaleItemTransfer, resaleItemTransfer)
		} else {
			// 付款失败
			resaleUpdateUserItemStatusRes, err := yc_open.ResaleUpdateUserItemStatus(ctx, order.BatchID, yc_open.ResaleUpdateUserItemStatusRelease, resaleOrderUserItemIds)
			if err != nil {
				log.Ctx(ctx).Errorf("ReleaseUserItems ResaleUpdateUserItemStatus Release err:%+v", err)
				continue
			}
			log.Ctx(ctx).Infof("ReleaseUserItems ResaleUpdateUserItemStatus Release res:%+v", resaleUpdateUserItemStatusRes)
			if resaleUpdateUserItemStatusRes == yc_open.ResaleUpdateUserItemStatusSuccess {
				err = repo.ExecGenTx(ctx, func(tx context.Context) error {
					err = CancelResaleOrder(tx, resaleListingsItemIds, []int64{order.ID}, resaleOrderItemIds)
					if err != nil {
						// 云仓释放锁定物品成功，取消订单失败
						log.Ctx(ctx).Errorf("查询锁定成功未支付订单,云仓释放锁定物品成功，取消订单失败, orderId: %+v,err:%+v", order.ID, err)
						warn.SendDefaultWarnMsg(ctx, "【查询锁定成功未支付订单,云仓释放锁定物品成功，取消订单失败】", fmt.Sprintf("orderId: %+v, userItemIds: %v", order.ID, utils.Obj2JsonStr(resaleOrderUserItemIds)))
						return err
					}
					return nil
				})
				if err != nil {
					log.Ctx(ctx).Errorf("物品异常锁定订单处理失败, orderId:%+v, err:%+v", order.ID, err)
					continue
				}
			}
		}
	}
	return nil
}

// ResaleItemTransferHandler 物品发放异常订单处理
func ResaleItemTransferHandler(ctx context.Context, resaleOrders []*model.ResaleOrder) error {
	for _, order := range resaleOrders {
		var userItemIds []string
		for _, item := range order.ResaleOrderItem {
			userItemIds = append(userItemIds, item.UserItemID)
		}
		openUserInfo, err := tmt.QueryUserOpenById(ctx, order.BuyerID)
		if err != nil || openUserInfo.OpenInfo == nil {
			log.Ctx(ctx).Error("ResaleItemTransferHandler 未查询到用户openUserId")
			return errors.New("未查询到用户openUserId")
		}
		err = ResaleOrderUserItemTransferHandler(ctx, order, openUserInfo.OpenInfo.OpenUserId, userItemIds)
		if err != nil {
			log.Ctx(ctx).Errorf("ResaleItemTransferHandler ResaleOrderUserItemTransferHandler,orderId:%+v,err:%+v", order.ID, err)
			continue
		}
		err = ResaleTradeCompleted(ctx, order.ID)
		if err != nil {
			log.Ctx(ctx).Errorf("转卖订单完成失败 orderId:%+v, err:%v", order.ID, err)
		}
	}
	return nil
}

// GetSellerSoldTotalQtyMap 获取卖家已售商品总数量（不区分商品）
func GetSellerSoldTotalQtyMap(ctx context.Context, sellerIDs []string) (map[string]int64, error) {
	logPrefix := "GetSellerSoldTotalQtyMap"
	cacheKeyPrefix := "marketplace_service:resale:seller_sold_total_qty:"
	resultMap := make(map[string]int64)
	noCacheSellerIDs := make([]string, 0)
	for _, sellerID := range sellerIDs {
		qty, err := global.REDIS.Get(ctx, cacheKeyPrefix+sellerID).Int64()
		if err == nil && qty > 0 {
			resultMap[sellerID] = qty
		} else {
			noCacheSellerIDs = append(noCacheSellerIDs, sellerID)
		}
	}
	if len(noCacheSellerIDs) > 0 {
		orderDB := repo.GetQuery().ResaleOrder.UnderlyingDB()
		type orderCountResult struct {
			SellerID string `gorm:"column:seller_id" json:"seller_id"`
			TotalQty int64  `gorm:"column:total_qty" json:"total_qty"`
		}
		var orderCountResults []*orderCountResult
		err := orderDB.Select("seller_id, SUM(quantity) AS total_qty").
			Where("status = ?", enums.OrderStatusCompleted.Val()).
			Where("seller_id IN ?", noCacheSellerIDs).
			Group("seller_id").
			Find(&orderCountResults).Error
		if err != nil {
			return nil, err
		}
		for _, item := range orderCountResults {
			resultMap[item.SellerID] = item.TotalQty
			// 缓存
			cacheKey := cacheKeyPrefix + item.SellerID
			_, err = global.REDIS.Set(ctx, cacheKey, item.TotalQty, time.Second*30).Result()
			if err != nil {
				log.Ctx(ctx).Errorf("%s redis set failed. seller_id:%+v, err:%+v", logPrefix, item.SellerID, err)
			}
		}
	}

	return resultMap, nil
}

// ResaleTradeCompleted 处理转卖交易完成
func ResaleTradeCompleted(ctx context.Context, orderId int64) error {
	// 订单完成
	ro := repo.GetQuery().ResaleOrder
	order, _ := repo.NewResaleOrderRepo(ro.WithContext(ctx)).SelectOne(search.NewWrapper().Where(ro.ID.Eq(orderId)))
	if order.FinishedAt != nil {
		err := HandleResaleTradeCompleted(ctx, order.ItemID, order.Quantity, *order.FinishedAt)
		if err != nil {
			log.Ctx(ctx).Errorf("ResaleTradeCompletedHandleResaleTradeCompleted,处理转卖交易完成失败 orderId:%+v, err:%v", order.ID, err)
			warn.SendDefaultWarnMsg(ctx, "【处理转卖交易完成失败】", fmt.Sprintf("orderId: %+v, err:%v", order.ID, err))
		}
	}
	return nil
}
