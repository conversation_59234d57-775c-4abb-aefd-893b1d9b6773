package enums

// HomeFeedStatus 状态
type HomeFeedStatus int32

func (r HomeFeedStatus) Val() int32 {
	return int32(r)
}

const (
	// HomeFeedStatusDel 已删除
	HomeFeedStatusDel HomeFeedStatus = -1
	// HomeFeedStatusDown 已下架
	HomeFeedStatusDown HomeFeedStatus = 0
	// HomeFeedStatusUp 已上架
	HomeFeedStatusUp HomeFeedStatus = 1
)

// HomeFeedOrderByType 首页列表排序类型
type HomeFeedOrderByType int32

func (r HomeFeedOrderByType) Val() int32 {
	return int32(r)
}

const (
	// HomeFeedOrderByTypeDefault 默认排序
	HomeFeedOrderByTypeDefault HomeFeedOrderByType = 1
	// HomeFeedOrderByTypeSaleVolume 销量排序
	HomeFeedOrderByTypeSaleVolume HomeFeedOrderByType = 2
	// HomeFeedOrderByTypePrice 价格排序
	HomeFeedOrderByTypePrice HomeFeedOrderByType = 3
)
