package yc_open

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"fmt"
	"marketplace_service/global"
	"marketplace_service/pkg/utils"
	"net"
	"time"
)

type ResaleUpdateUserItemStatusResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data interface{} `json:"data" form:"data"`
}

const (
	ResaleUpdateUserItemStatusSuccess = 1
	ResaleUpdateUserItemStatusUnknown = 0
	ResaleUpdateUserItemStatusFail    = -1
	ResaleUpdateUserItemStatusLock    = 1
	ResaleUpdateUserItemStatusRelease = 2
)

// ResaleUpdateUserItemStatus 售卖锁定/解锁物品
func ResaleUpdateUserItemStatus(ctx context.Context, resaleBatchId int64, operationType int32, userItemIds []string) (int32, error) {
	log.Ctx(ctx).Infof("ResaleUpdateUserItemStatusFusion resaleBatchId:%+v, operationType:%+v, userItemIds:%+v", resaleBatchId, operationType, userItemIds)
	rsp := &ResaleUpdateUserItemStatusResp{}

	params := map[string]interface{}{
		"resale_batch_id": fmt.Sprintf("%d", resaleBatchId),
		"operation_type":  operationType,
		"user_item_ids":   userItemIds,
		"sign":            "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("ResaleUpdateUserItemStatusFusion sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("ResaleUpdateUserItemStatusFusion GetAppAccess err，返回数据：%v", err)
		return ResaleUpdateUserItemStatusFail, err
	}

	url := "/open/user_item/resale_update_user_item_status"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(), utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)

	log.Ctx(ctx).Infof("ResaleUpdateUserItemStatus SynthesisResaleUpdateUserItemStatus params:%+v", params)
	var operationDesc = "售卖锁定"
	if operationType == 2 {
		operationDesc = "售卖解锁"
	}
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 未知异常
			log.Ctx(ctx).Errorf("%s物品超时异常，返回数据：%v", operationDesc, err)
			return ResaleUpdateUserItemStatusUnknown, response.Fail.SetMsg(fmt.Sprintf("%s物品未知异常", operationDesc))
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("%s物品超时异常，返回数据：%v", operationDesc, err)
			return ResaleUpdateUserItemStatusUnknown, response.Fail.SetMsg(fmt.Sprintf("%s物品请求超时", operationDesc))
		}
		// 明确异常
		log.Ctx(ctx).Errorf("%s物品异常，返回数据：%v", operationDesc, err)
		return ResaleUpdateUserItemStatusFail, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("%s物品异常，返回数据：%v", operationDesc, utils.Obj2JsonStr(rsp))

		return ResaleUpdateUserItemStatusFail, response.Fail.SetMsg(fmt.Sprintf("%s物品失败", operationDesc))
	}
	log.Ctx(ctx).Infof("%s物品成功 params:%+v,rsp:%+v", operationDesc, params, rsp)
	return ResaleUpdateUserItemStatusSuccess, nil
}
