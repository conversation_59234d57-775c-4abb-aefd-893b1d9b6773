package auth

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type Web struct {
	// 不需要鉴权的地址
	NoAuthUrl []string
}

func (c *Web) CheckToken(token string, ctx context.Context) (any, error) {
	// 远程校验token 获取用户信息
	info, err := pat.CheckUserJwt(ctx, token)
	if err != nil {
		return nil, err
	}

	if info == nil {
		return nil, response.TokenErr.SetMsg("鉴权失败")
	}
	return info, nil
}

func (c *Web) GetNoAuthUrl() []string {
	return c.NoAuthUrl
}

// GetUserFromCtx 获取用户信息
func GetUserFromCtx(ctx context.Context) (*pat.CheckUserJwtUserInfo, bool) {
	acc, ok := ctx.Value(userInfo).(*pat.CheckUserJwtUserInfo)
	return acc, ok
}
