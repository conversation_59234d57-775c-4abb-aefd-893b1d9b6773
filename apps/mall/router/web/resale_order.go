package web

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/web"
)

func ResaleOrder(router *gin.RouterGroup) {
	group := router.Group("/resale_order")
	{
		// 查询我购买的转卖订单列表
		group.GET("/buy/list", web.GetWebResaleOrderBuyList)
		// 查询我出售的转卖订单列表
		group.GET("/sale/list", web.GetWebResaleOrderSaleList)
		// 查询我购买的转卖订单详情
		group.GET("/buy/detail", web.GetWebResaleOrderBuyDetail)
		// 查询我出售的转卖订单详情
		group.GET("/sale/detail", web.GetWebResaleOrderSaleDetail)
		// 查询最近成交记录
		group.GET("/recent_list", web.GetWebResaleOrderRecentList)
	}
}
