package auth

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"
	log "e.coding.net/g-dtay0385/common/go-logger"
)

const userInfo = "UserInfo"

// UserService 用户服务
type UserService struct {
	ctx context.Context
}

// NewUserService 创建用户服务
func NewUserService(ctx context.Context) *UserService {
	return &UserService{
		ctx: ctx,
	}
}

// GetAdminId 获取当前登录用户id
func (s *UserService) GetAdminId() string {
	info, ok := GetAdminFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}

// GetAdmin 获取当前登录用户
func (s *UserService) GetAdmin() *pat.CheckAdmJwtUserInfo {
	info, ok := GetAdminFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return nil
	}
	return info
}

// GetUserId 获取当前登录用户id
func (s *UserService) GetUserId() string {
	info, ok := GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}

// GetUserFromCtx 获取当前登录用户
func (s *UserService) GetUserFromCtx() *pat.CheckUserJwtUserInfo {
	info, ok := GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return nil
	}
	return info
}
