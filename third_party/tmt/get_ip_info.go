package tmt

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type GetIpInfo struct {
	Id       string  `json:"_id" form:"id"`
	Name     string  `json:"name" form:"name"`
	Pid      *string `json:"pid" form:"pid"`
	Priority int64   `json:"priority" form:"priority"`
	Remark   string  `json:"remark" form:"remark"`
	Status   int32   `json:"status" form:"status"`
	Level    int32   `json:"level" form:"level"`
	Icon     string  `json:"icon" form:"icon"`
}

type GetIpInfosRsp struct {
	Code int32        `json:"code" form:"code"`
	Desc string       `json:"desc" form:"desc"`
	Data []*GetIpInfo `json:"data" form:"data"`
}

func GetIpInfos(ctx context.Context, ids []string) ([]*GetIpInfo, error) {
	rsp := &GetIpInfosRsp{}
	req, err := request.Tmt()
	if err != nil {
		return nil, err
	}
	err = req.Call(
		ctx,
		"open/ip_classify/v1/list",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"ip_ids": ids}),
		utilRequest.WithMethodPost(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data, err
}
