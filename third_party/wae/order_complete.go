package wae

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/pkg/errors"
)

type OrderCompleteRequest struct {
	CNo string `json:"c_no"`
}

// OrderCompleteResponse 定义接口返回结构
type OrderCompleteResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

// OrderComplete 订单完成
func OrderComplete(ctx context.Context, request *OrderCompleteRequest) (bool, error) {
	var rsp OrderCompleteResponse
	if err := CommonRequest(
		ctx,
		"/open/v1/order/complete",
		map[string]interface{}{"c_no": request.CNo},
		&rsp,
	); err != nil {
		log.Ctx(ctx).Errorf("OrderComplete 订单完成失败 err: %v", err)
		return false, err
	}
	if rsp.Code == 1000 {
		return true, nil
	}
	return false, errors.New(rsp.Message)
}
