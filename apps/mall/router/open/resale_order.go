package open

import (
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/api/open"
)

// ResaleOrder 转卖订单相关路由
func ResaleOrder(router *gin.RouterGroup) {
	group := router.Group("/resale_order")
	{
		// 释放锁单失败订单的背包物品
		group.POST("/release_user_items", open.ReleaseUserItems)
		// 物品异常锁定订单处理
		group.POST("/locked_handler", open.LockedHandler)
		// 物品发放异常订单处理
		group.POST("/resale_item_transfer_handler", open.ResaleItemTransferHandler)
	}
}
