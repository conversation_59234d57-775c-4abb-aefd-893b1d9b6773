package open

import (
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
	"net/http"
)

// SupplyChainNotice
// @Summary 供应链通知
// @Description 供应链通知
// @Tags open端-通知管理
// @x-apifox-folder "open端/通知管理"
// @Param data body define.SupplyChainNoticeReq true "请求参数"
// @Success 200 {object} response.Data{data=define.SupplyChainNoticeResp}
// @Router /open/v1/notice/supply_chain [post]
// @Security Bearer
// @produce  json
func SupplyChainNotice(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.SupplyChainNoticeOriginalReq{}
	if err := ctx.ShouldBind(req); err != nil {
		log.Ctx(ctx).Errorf("供应链通知 err %+v", err)
		ctx.JSON(http.StatusOK, map[string]interface{}{
			"data": "参数错误",
		})
		return
	}
	params, err := s.SupplyChainNoticeCCheckSign(req)
	if err != nil {
		log.Ctx(ctx).Errorf("验签失败, err:%+v", err)
		ctx.JSON(http.StatusOK, map[string]interface{}{
			"data": "验签失败",
		})
		return
	}
	resp, err := s.SupplyChainNotice(params)
	if err != nil {
		log.Ctx(ctx).Errorf("供应链通知 err %+v", err)
	}
	ctx.JSON(http.StatusOK, resp)
}
