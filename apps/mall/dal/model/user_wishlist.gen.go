// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameUserWishlist = "user_wishlist"

// UserWishlist 商城直购白名单表
type UserWishlist struct {
	ID        int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	UserID    string                `gorm:"column:user_id;type:varchar(32);not null;comment:用户ID" json:"user_id"`                                    // 用户ID
	SkuID     string                `gorm:"column:sku_id;type:varchar(32);not null;comment:商品 sku_id" json:"sku_id"`                                 // 商品 sku_id
	ItemID    string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                    // 商品ID
	Status    int32                 `gorm:"column:status;type:tinyint;not null;default:1;comment:状态（1=想要，0=已取消）" json:"status"`                      // 状态（1=想要，0=已取消）
	CreatedAt time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel     soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除（0=未删除，1=删除）;softDelete:flag" json:"is_del"`           // 是否删除（0=未删除，1=删除）
}

// TableName UserWishlist's table name
func (*UserWishlist) TableName() string {
	return TableNameUserWishlist
}
