// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newTradeOrderFreight(db *gorm.DB, opts ...gen.DOOption) tradeOrderFreight {
	_tradeOrderFreight := tradeOrderFreight{}

	_tradeOrderFreight.tradeOrderFreightDo.UseDB(db, opts...)
	_tradeOrderFreight.tradeOrderFreightDo.UseModel(&model.TradeOrderFreight{})

	tableName := _tradeOrderFreight.tradeOrderFreightDo.TableName()
	_tradeOrderFreight.ALL = field.NewAsterisk(tableName)
	_tradeOrderFreight.ID = field.NewInt64(tableName, "id")
	_tradeOrderFreight.OrderID = field.NewInt64(tableName, "order_id")
	_tradeOrderFreight.OrderItemID = field.NewInt64(tableName, "order_item_id")
	_tradeOrderFreight.DeliveryNumber = field.NewString(tableName, "delivery_number")
	_tradeOrderFreight.DeliveryCompany = field.NewString(tableName, "delivery_company")
	_tradeOrderFreight.Status = field.NewInt32(tableName, "status")
	_tradeOrderFreight.Checked = field.NewInt32(tableName, "checked")
	_tradeOrderFreight.DeliveredAt = field.NewTime(tableName, "delivered_at")
	_tradeOrderFreight.Records = field.NewField(tableName, "records")
	_tradeOrderFreight.CreatedBy = field.NewString(tableName, "created_by")
	_tradeOrderFreight.CreatedAt = field.NewTime(tableName, "created_at")
	_tradeOrderFreight.UpdatedBy = field.NewString(tableName, "updated_by")
	_tradeOrderFreight.UpdatedAt = field.NewTime(tableName, "updated_at")
	_tradeOrderFreight.IsDel = field.NewField(tableName, "is_del")

	_tradeOrderFreight.fillFieldMap()

	return _tradeOrderFreight
}

// tradeOrderFreight 商城直购订单物流表
type tradeOrderFreight struct {
	tradeOrderFreightDo

	ALL             field.Asterisk
	ID              field.Int64  // 主键
	OrderID         field.Int64  // 订单ID
	OrderItemID     field.Int64  // 订单详情ID
	DeliveryNumber  field.String // 快递单号
	DeliveryCompany field.String // 快递公司
	Status          field.Int32  // 状态（-1=无效，0=途中，1=揽收，2=疑难，3=签收，4=退签，5=派件，6=退回，7=转单，10=待清关，11=清关中，12=已清关，13=清关异常，14=收件人拒签）
	Checked         field.Int32  // 状态（0=未签收，1=已签收）
	DeliveredAt     field.Time   // 订单发货时间
	Records         field.Field  // 物流信息
	CreatedBy       field.String // 创建人
	CreatedAt       field.Time   // 创建时间
	UpdatedBy       field.String // 更新人
	UpdatedAt       field.Time   // 更新时间
	IsDel           field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (t tradeOrderFreight) Table(newTableName string) *tradeOrderFreight {
	t.tradeOrderFreightDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tradeOrderFreight) As(alias string) *tradeOrderFreight {
	t.tradeOrderFreightDo.DO = *(t.tradeOrderFreightDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tradeOrderFreight) updateTableName(table string) *tradeOrderFreight {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.OrderID = field.NewInt64(table, "order_id")
	t.OrderItemID = field.NewInt64(table, "order_item_id")
	t.DeliveryNumber = field.NewString(table, "delivery_number")
	t.DeliveryCompany = field.NewString(table, "delivery_company")
	t.Status = field.NewInt32(table, "status")
	t.Checked = field.NewInt32(table, "checked")
	t.DeliveredAt = field.NewTime(table, "delivered_at")
	t.Records = field.NewField(table, "records")
	t.CreatedBy = field.NewString(table, "created_by")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedBy = field.NewString(table, "updated_by")
	t.UpdatedAt = field.NewTime(table, "updated_at")
	t.IsDel = field.NewField(table, "is_del")

	t.fillFieldMap()

	return t
}

func (t *tradeOrderFreight) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tradeOrderFreight) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 14)
	t.fieldMap["id"] = t.ID
	t.fieldMap["order_id"] = t.OrderID
	t.fieldMap["order_item_id"] = t.OrderItemID
	t.fieldMap["delivery_number"] = t.DeliveryNumber
	t.fieldMap["delivery_company"] = t.DeliveryCompany
	t.fieldMap["status"] = t.Status
	t.fieldMap["checked"] = t.Checked
	t.fieldMap["delivered_at"] = t.DeliveredAt
	t.fieldMap["records"] = t.Records
	t.fieldMap["created_by"] = t.CreatedBy
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_by"] = t.UpdatedBy
	t.fieldMap["updated_at"] = t.UpdatedAt
	t.fieldMap["is_del"] = t.IsDel
}

func (t tradeOrderFreight) clone(db *gorm.DB) tradeOrderFreight {
	t.tradeOrderFreightDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tradeOrderFreight) replaceDB(db *gorm.DB) tradeOrderFreight {
	t.tradeOrderFreightDo.ReplaceDB(db)
	return t
}

type tradeOrderFreightDo struct{ gen.DO }

type ITradeOrderFreightDo interface {
	gen.SubQuery
	Debug() ITradeOrderFreightDo
	WithContext(ctx context.Context) ITradeOrderFreightDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITradeOrderFreightDo
	WriteDB() ITradeOrderFreightDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITradeOrderFreightDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITradeOrderFreightDo
	Not(conds ...gen.Condition) ITradeOrderFreightDo
	Or(conds ...gen.Condition) ITradeOrderFreightDo
	Select(conds ...field.Expr) ITradeOrderFreightDo
	Where(conds ...gen.Condition) ITradeOrderFreightDo
	Order(conds ...field.Expr) ITradeOrderFreightDo
	Distinct(cols ...field.Expr) ITradeOrderFreightDo
	Omit(cols ...field.Expr) ITradeOrderFreightDo
	Join(table schema.Tabler, on ...field.Expr) ITradeOrderFreightDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITradeOrderFreightDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITradeOrderFreightDo
	Group(cols ...field.Expr) ITradeOrderFreightDo
	Having(conds ...gen.Condition) ITradeOrderFreightDo
	Limit(limit int) ITradeOrderFreightDo
	Offset(offset int) ITradeOrderFreightDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITradeOrderFreightDo
	Unscoped() ITradeOrderFreightDo
	Create(values ...*model.TradeOrderFreight) error
	CreateInBatches(values []*model.TradeOrderFreight, batchSize int) error
	Save(values ...*model.TradeOrderFreight) error
	First() (*model.TradeOrderFreight, error)
	Take() (*model.TradeOrderFreight, error)
	Last() (*model.TradeOrderFreight, error)
	Find() ([]*model.TradeOrderFreight, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradeOrderFreight, err error)
	FindInBatches(result *[]*model.TradeOrderFreight, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.TradeOrderFreight) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITradeOrderFreightDo
	Assign(attrs ...field.AssignExpr) ITradeOrderFreightDo
	Joins(fields ...field.RelationField) ITradeOrderFreightDo
	Preload(fields ...field.RelationField) ITradeOrderFreightDo
	FirstOrInit() (*model.TradeOrderFreight, error)
	FirstOrCreate() (*model.TradeOrderFreight, error)
	FindByPage(offset int, limit int) (result []*model.TradeOrderFreight, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITradeOrderFreightDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t tradeOrderFreightDo) Debug() ITradeOrderFreightDo {
	return t.withDO(t.DO.Debug())
}

func (t tradeOrderFreightDo) WithContext(ctx context.Context) ITradeOrderFreightDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tradeOrderFreightDo) ReadDB() ITradeOrderFreightDo {
	return t.Clauses(dbresolver.Read)
}

func (t tradeOrderFreightDo) WriteDB() ITradeOrderFreightDo {
	return t.Clauses(dbresolver.Write)
}

func (t tradeOrderFreightDo) Session(config *gorm.Session) ITradeOrderFreightDo {
	return t.withDO(t.DO.Session(config))
}

func (t tradeOrderFreightDo) Clauses(conds ...clause.Expression) ITradeOrderFreightDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tradeOrderFreightDo) Returning(value interface{}, columns ...string) ITradeOrderFreightDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tradeOrderFreightDo) Not(conds ...gen.Condition) ITradeOrderFreightDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tradeOrderFreightDo) Or(conds ...gen.Condition) ITradeOrderFreightDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tradeOrderFreightDo) Select(conds ...field.Expr) ITradeOrderFreightDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tradeOrderFreightDo) Where(conds ...gen.Condition) ITradeOrderFreightDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tradeOrderFreightDo) Order(conds ...field.Expr) ITradeOrderFreightDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tradeOrderFreightDo) Distinct(cols ...field.Expr) ITradeOrderFreightDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tradeOrderFreightDo) Omit(cols ...field.Expr) ITradeOrderFreightDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tradeOrderFreightDo) Join(table schema.Tabler, on ...field.Expr) ITradeOrderFreightDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tradeOrderFreightDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITradeOrderFreightDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tradeOrderFreightDo) RightJoin(table schema.Tabler, on ...field.Expr) ITradeOrderFreightDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tradeOrderFreightDo) Group(cols ...field.Expr) ITradeOrderFreightDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tradeOrderFreightDo) Having(conds ...gen.Condition) ITradeOrderFreightDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tradeOrderFreightDo) Limit(limit int) ITradeOrderFreightDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tradeOrderFreightDo) Offset(offset int) ITradeOrderFreightDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tradeOrderFreightDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITradeOrderFreightDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tradeOrderFreightDo) Unscoped() ITradeOrderFreightDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tradeOrderFreightDo) Create(values ...*model.TradeOrderFreight) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tradeOrderFreightDo) CreateInBatches(values []*model.TradeOrderFreight, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tradeOrderFreightDo) Save(values ...*model.TradeOrderFreight) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tradeOrderFreightDo) First() (*model.TradeOrderFreight, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderFreight), nil
	}
}

func (t tradeOrderFreightDo) Take() (*model.TradeOrderFreight, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderFreight), nil
	}
}

func (t tradeOrderFreightDo) Last() (*model.TradeOrderFreight, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderFreight), nil
	}
}

func (t tradeOrderFreightDo) Find() ([]*model.TradeOrderFreight, error) {
	result, err := t.DO.Find()
	return result.([]*model.TradeOrderFreight), err
}

func (t tradeOrderFreightDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradeOrderFreight, err error) {
	buf := make([]*model.TradeOrderFreight, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tradeOrderFreightDo) FindInBatches(result *[]*model.TradeOrderFreight, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tradeOrderFreightDo) Attrs(attrs ...field.AssignExpr) ITradeOrderFreightDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tradeOrderFreightDo) Assign(attrs ...field.AssignExpr) ITradeOrderFreightDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tradeOrderFreightDo) Joins(fields ...field.RelationField) ITradeOrderFreightDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tradeOrderFreightDo) Preload(fields ...field.RelationField) ITradeOrderFreightDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tradeOrderFreightDo) FirstOrInit() (*model.TradeOrderFreight, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderFreight), nil
	}
}

func (t tradeOrderFreightDo) FirstOrCreate() (*model.TradeOrderFreight, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradeOrderFreight), nil
	}
}

func (t tradeOrderFreightDo) FindByPage(offset int, limit int) (result []*model.TradeOrderFreight, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tradeOrderFreightDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tradeOrderFreightDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tradeOrderFreightDo) Delete(models ...*model.TradeOrderFreight) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tradeOrderFreightDo) withDO(do gen.Dao) *tradeOrderFreightDo {
	t.DO = *do.(*gen.DO)
	return t
}
