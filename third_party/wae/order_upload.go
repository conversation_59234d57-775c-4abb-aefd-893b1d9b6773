package wae

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
)

// OrderUploadRequest 请求参数结构体
type OrderUploadRequest struct {
	Orders []OrderItem `json:"orders"`
}

// OrderItem 订单主体信息
type OrderItem struct {
	OID          string       `json:"o_id"`          // 外部订单ID，必传
	BuyerID      string       `json:"buyer_id"`      // 买家ID，必传
	BuyerNick    string       `json:"buyer_nick"`    // 买家昵称
	BuyerComment string       `json:"buyer_comment"` // 买家备注信息
	PayAmount    int64        `json:"pay_amount"`    // 实际支付金额，必传
	RecvName     string       `json:"recv_name"`     // 收货人名称，必传
	RecvPhone    string       `json:"recv_phone"`    // 收货人电话|手机号，必传
	RecvZip      string       `json:"recv_zip"`      // 收货人邮编，必传
	RecvProvince string       `json:"recv_province"` // 收货人省|自治区|直辖市，必传
	RecvCity     string       `json:"recv_city"`     // 收货人市|州|盟|，必传
	RecvDistrict string       `json:"recv_district"` // 收货人区|县，必传
	RecvAddress  string       `json:"recv_address"`  // 收货人详细地址，必传
	UserAccount  string       `json:"user_account"`  // 用户直充账户（虚拟商品订单选用字段）
	OCreatedAt   string       `json:"o_created_at"`  // 外部订单创建时间，必传
	OUpdatedAt   string       `json:"o_updated_at"`  // 外部订单更新时间
	OrderItems   []OrderGoods `json:"order_items"`   // 子单列表
}

// OrderGoods 子单商品信息
type OrderGoods struct {
	SKUNo      string `json:"sku_no"`      // SKU编号，必传
	OChildID   string `json:"o_child_id"`  // 外部订单子单唯一ID，必传
	GoodsTitle string `json:"goods_title"` // 商品名称，必传
	GoodsNum   int32  `json:"goods_num"`   // 商品数量，必传
	GoodsSpec  string `json:"goods_spec"`  // 规格信息拼接，没有则传“默认”，必传
	GoodsPrice int64  `json:"goods_price"` // 商品售价，必传
}

// OrderUploadResponse 定义接口返回结构
type OrderUploadResponse struct {
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Data    OrderData `json:"data"`
}
type OrderData struct {
	SuccessOrders []Order `json:"success_order"`
	FailedOrders  []Order `json:"failed_order"`
}

// Order 订单结构
type Order struct {
	OID     string `json:"o_id"`    // 接入方订单ID
	CNo     string `json:"c_no"`    // 供应链订单编号
	Message string `json:"message"` // 消息描述
}

func OrderUpload(ctx context.Context, request *OrderUploadRequest) (*OrderData, error) {
	var rsp OrderUploadResponse
	if err := CommonRequest(
		ctx,
		"/open/v1/order/upload",
		request.Orders,
		&rsp,
	); err != nil {
		log.Ctx(ctx).Errorf("OrderUpload 订单上传失败 err: %v", err)
		return nil, err
	}
	return &rsp.Data, nil
}
