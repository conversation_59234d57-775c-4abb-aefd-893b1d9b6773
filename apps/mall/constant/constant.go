package constant

import "time"

var MallItemDecrStockLuaSha string

// MallItemDecrStockLua 扣减库存lua脚本
const MallItemDecrStockLua = `
	local stock = redis.call("GET", KEYS[1])
	if not stock or tonumber(stock) < tonumber(ARGV[2]) then
		return 0
	end
	
	-- 检查用户限购次数
	local userBuyCountKey = KEYS[2]
	local buyCount = redis.call("GET", userBuyCountKey)
	if tonumber(ARGV[1]) > 0 then
		if buyCount and tonumber(buyCount) + tonumber(ARGV[2]) > tonumber(ARGV[1]) then
			return -1
		end
	end

	
	-- 扣减库存
	redis.call("DECRBY", KEYS[1], tonumber(ARGV[2]))
	
	-- 更新用户购买次数
	if buyCount then
		redis.call("INCRBY", userBuyCountKey, tonumber(ARGV[2]))
	else
		local keyExpire = tonumber(ARGV[3])
		redis.call("SET", userBuyCountKey, tonumber(ARGV[2]))
    	redis.call('EXPIRE', userBuyCountKey, keyExpire)
	end
	
	return 1
`

var MallItemRollbackStockLuaSha string

// MallItemRollbackStockLua 回滚库存lua脚本
const MallItemRollbackStockLua = `
	-- 获取库存
	local stock = redis.call("GET", KEYS[1])
	if stock then
		-- 回滚库存
		redis.call("INCRBY", KEYS[1], tonumber(ARGV[1]))
	end
	
	-- 获取用户购买次数
	local userBuyCount = redis.call("GET", KEYS[2])
	if userBuyCount then
		-- 回滚用户购买次数
		redis.call("DECRBY", KEYS[2], tonumber(ARGV[1]))
	end
	
	return 1
`

const (
	DateTimeFormat = "2006-01-02 15:04:05"
	DateFormat     = "2006-01-02"
)

// 逻辑删除【0->未删除；1->已删除】
const (
	NORMAL  = 0 // 未删除
	DELETED = 1 //已删除
)

const (
	IntFalse = int32(0)
	IntTrue  = int32(1)
)

const (
	Countdown      = time.Second * 60 * 10
	OrderFinishDay = time.Hour * 24 * 7
)

const (
	SortOrderAsc  = "asc"
	SortOrderDesc = "desc"
)

const (
	SteamItemInfoTTL    = 10 * time.Minute
	UserDetailTTL       = 24 * time.Hour
	IpInfoTTL           = 10 * time.Minute
	ItemClassifyInfoTTL = 10 * time.Minute
	TrademarkInfoTTL    = 10 * time.Minute
)

const (
	LevelTwo   = 2
	LevelThree = 3
)

const (
	RecentListMax = 200
)

const (
	TradePool = "fffffffffffffffb85501001"
	FeePool   = "fffffffffffffffb85501003"
	SpdbPool  = "fffffffffffffffb80201022"
)

const (
	ResaleStandardConfigKey    = "resale_standard"
	ResaleInstructionConfigKey = "resale_instruction"
	MallInstructionConfigKey   = "mall_instruction"
)

const CommonGroup = "marketplace_service"

// ResaleItemTransfer 转卖物品转移-消息Topic
const (
	ResaleItemTransfer = "resale_item_transfer"
)

const ResaleFee = "resale_fee"
const ResaleBuyMaxQty = "resale_buy_max_qty"
const ResaleSaleMaxQty = "resale_sale_max_qty"
