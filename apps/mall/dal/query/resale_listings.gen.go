// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newResaleListings(db *gorm.DB, opts ...gen.DOOption) resaleListings {
	_resaleListings := resaleListings{}

	_resaleListings.resaleListingsDo.UseDB(db, opts...)
	_resaleListings.resaleListingsDo.UseModel(&model.ResaleListings{})

	tableName := _resaleListings.resaleListingsDo.TableName()
	_resaleListings.ALL = field.NewAsterisk(tableName)
	_resaleListings.ID = field.NewInt64(tableName, "id")
	_resaleListings.Status = field.NewInt32(tableName, "status")
	_resaleListings.SellerID = field.NewString(tableName, "seller_id")
	_resaleListings.SellerPhone = field.NewString(tableName, "seller_phone")
	_resaleListings.ItemID = field.NewString(tableName, "item_id")
	_resaleListings.SkuID = field.NewString(tableName, "sku_id")
	_resaleListings.ItemName = field.NewString(tableName, "item_name")
	_resaleListings.ItemSpecs = field.NewString(tableName, "item_specs")
	_resaleListings.ItemIconURL = field.NewString(tableName, "item_icon_url")
	_resaleListings.TotalAmount = field.NewInt64(tableName, "total_amount")
	_resaleListings.SalePrice = field.NewInt64(tableName, "sale_price")
	_resaleListings.ListingQuantity = field.NewInt32(tableName, "listing_quantity")
	_resaleListings.SoldQuantity = field.NewInt32(tableName, "sold_quantity")
	_resaleListings.TotalIncome = field.NewInt64(tableName, "total_income")
	_resaleListings.TotalFee = field.NewInt64(tableName, "total_fee")
	_resaleListings.LatestTradeAt = field.NewTime(tableName, "latest_trade_at")
	_resaleListings.Terminal = field.NewString(tableName, "terminal")
	_resaleListings.AppVersion = field.NewString(tableName, "app_version")
	_resaleListings.Operator = field.NewString(tableName, "operator")
	_resaleListings.CreatedAt = field.NewTime(tableName, "created_at")
	_resaleListings.UpdatedAt = field.NewTime(tableName, "updated_at")
	_resaleListings.IsDel = field.NewField(tableName, "is_del")
	_resaleListings.ResaleListingsItem = resaleListingsHasManyResaleListingsItem{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ResaleListingsItem", "model.ResaleListingsItem"),
	}

	_resaleListings.fillFieldMap()

	return _resaleListings
}

// resaleListings 转卖挂单表
type resaleListings struct {
	resaleListingsDo

	ALL                field.Asterisk
	ID                 field.Int64  // 主键
	Status             field.Int32  // 状态（0-已下架，1-出售中，2-已出售，3-已取消）
	SellerID           field.String // 卖家ID
	SellerPhone        field.String // 卖家手机号码
	ItemID             field.String // 商品ID
	SkuID              field.String // 商品 sku_id
	ItemName           field.String // 商品名称
	ItemSpecs          field.String // 商品规格
	ItemIconURL        field.String // 商品主图
	TotalAmount        field.Int64  // 挂单总额(分)
	SalePrice          field.Int64  // 出售单价(分)
	ListingQuantity    field.Int32  // 出售总量
	SoldQuantity       field.Int32  // 已出售数量
	TotalIncome        field.Int64  // 总收入
	TotalFee           field.Int64  // 总手续费
	LatestTradeAt      field.Time   // 最新订单成交时间
	Terminal           field.String // 终端
	AppVersion         field.String // 版本号
	Operator           field.String // 最后操作人
	CreatedAt          field.Time   // 创建时间
	UpdatedAt          field.Time   // 更新时间
	IsDel              field.Field  // 是否删除【0->未删除; 1->删除】
	ResaleListingsItem resaleListingsHasManyResaleListingsItem

	fieldMap map[string]field.Expr
}

func (r resaleListings) Table(newTableName string) *resaleListings {
	r.resaleListingsDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r resaleListings) As(alias string) *resaleListings {
	r.resaleListingsDo.DO = *(r.resaleListingsDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *resaleListings) updateTableName(table string) *resaleListings {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewInt64(table, "id")
	r.Status = field.NewInt32(table, "status")
	r.SellerID = field.NewString(table, "seller_id")
	r.SellerPhone = field.NewString(table, "seller_phone")
	r.ItemID = field.NewString(table, "item_id")
	r.SkuID = field.NewString(table, "sku_id")
	r.ItemName = field.NewString(table, "item_name")
	r.ItemSpecs = field.NewString(table, "item_specs")
	r.ItemIconURL = field.NewString(table, "item_icon_url")
	r.TotalAmount = field.NewInt64(table, "total_amount")
	r.SalePrice = field.NewInt64(table, "sale_price")
	r.ListingQuantity = field.NewInt32(table, "listing_quantity")
	r.SoldQuantity = field.NewInt32(table, "sold_quantity")
	r.TotalIncome = field.NewInt64(table, "total_income")
	r.TotalFee = field.NewInt64(table, "total_fee")
	r.LatestTradeAt = field.NewTime(table, "latest_trade_at")
	r.Terminal = field.NewString(table, "terminal")
	r.AppVersion = field.NewString(table, "app_version")
	r.Operator = field.NewString(table, "operator")
	r.CreatedAt = field.NewTime(table, "created_at")
	r.UpdatedAt = field.NewTime(table, "updated_at")
	r.IsDel = field.NewField(table, "is_del")

	r.fillFieldMap()

	return r
}

func (r *resaleListings) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *resaleListings) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 23)
	r.fieldMap["id"] = r.ID
	r.fieldMap["status"] = r.Status
	r.fieldMap["seller_id"] = r.SellerID
	r.fieldMap["seller_phone"] = r.SellerPhone
	r.fieldMap["item_id"] = r.ItemID
	r.fieldMap["sku_id"] = r.SkuID
	r.fieldMap["item_name"] = r.ItemName
	r.fieldMap["item_specs"] = r.ItemSpecs
	r.fieldMap["item_icon_url"] = r.ItemIconURL
	r.fieldMap["total_amount"] = r.TotalAmount
	r.fieldMap["sale_price"] = r.SalePrice
	r.fieldMap["listing_quantity"] = r.ListingQuantity
	r.fieldMap["sold_quantity"] = r.SoldQuantity
	r.fieldMap["total_income"] = r.TotalIncome
	r.fieldMap["total_fee"] = r.TotalFee
	r.fieldMap["latest_trade_at"] = r.LatestTradeAt
	r.fieldMap["terminal"] = r.Terminal
	r.fieldMap["app_version"] = r.AppVersion
	r.fieldMap["operator"] = r.Operator
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["is_del"] = r.IsDel

}

func (r resaleListings) clone(db *gorm.DB) resaleListings {
	r.resaleListingsDo.ReplaceConnPool(db.Statement.ConnPool)
	return r
}

func (r resaleListings) replaceDB(db *gorm.DB) resaleListings {
	r.resaleListingsDo.ReplaceDB(db)
	return r
}

type resaleListingsHasManyResaleListingsItem struct {
	db *gorm.DB

	field.RelationField
}

func (a resaleListingsHasManyResaleListingsItem) Where(conds ...field.Expr) *resaleListingsHasManyResaleListingsItem {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a resaleListingsHasManyResaleListingsItem) WithContext(ctx context.Context) *resaleListingsHasManyResaleListingsItem {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a resaleListingsHasManyResaleListingsItem) Session(session *gorm.Session) *resaleListingsHasManyResaleListingsItem {
	a.db = a.db.Session(session)
	return &a
}

func (a resaleListingsHasManyResaleListingsItem) Model(m *model.ResaleListings) *resaleListingsHasManyResaleListingsItemTx {
	return &resaleListingsHasManyResaleListingsItemTx{a.db.Model(m).Association(a.Name())}
}

type resaleListingsHasManyResaleListingsItemTx struct{ tx *gorm.Association }

func (a resaleListingsHasManyResaleListingsItemTx) Find() (result []*model.ResaleListingsItem, err error) {
	return result, a.tx.Find(&result)
}

func (a resaleListingsHasManyResaleListingsItemTx) Append(values ...*model.ResaleListingsItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a resaleListingsHasManyResaleListingsItemTx) Replace(values ...*model.ResaleListingsItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a resaleListingsHasManyResaleListingsItemTx) Delete(values ...*model.ResaleListingsItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a resaleListingsHasManyResaleListingsItemTx) Clear() error {
	return a.tx.Clear()
}

func (a resaleListingsHasManyResaleListingsItemTx) Count() int64 {
	return a.tx.Count()
}

type resaleListingsDo struct{ gen.DO }

type IResaleListingsDo interface {
	gen.SubQuery
	Debug() IResaleListingsDo
	WithContext(ctx context.Context) IResaleListingsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IResaleListingsDo
	WriteDB() IResaleListingsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IResaleListingsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IResaleListingsDo
	Not(conds ...gen.Condition) IResaleListingsDo
	Or(conds ...gen.Condition) IResaleListingsDo
	Select(conds ...field.Expr) IResaleListingsDo
	Where(conds ...gen.Condition) IResaleListingsDo
	Order(conds ...field.Expr) IResaleListingsDo
	Distinct(cols ...field.Expr) IResaleListingsDo
	Omit(cols ...field.Expr) IResaleListingsDo
	Join(table schema.Tabler, on ...field.Expr) IResaleListingsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IResaleListingsDo
	RightJoin(table schema.Tabler, on ...field.Expr) IResaleListingsDo
	Group(cols ...field.Expr) IResaleListingsDo
	Having(conds ...gen.Condition) IResaleListingsDo
	Limit(limit int) IResaleListingsDo
	Offset(offset int) IResaleListingsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleListingsDo
	Unscoped() IResaleListingsDo
	Create(values ...*model.ResaleListings) error
	CreateInBatches(values []*model.ResaleListings, batchSize int) error
	Save(values ...*model.ResaleListings) error
	First() (*model.ResaleListings, error)
	Take() (*model.ResaleListings, error)
	Last() (*model.ResaleListings, error)
	Find() ([]*model.ResaleListings, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleListings, err error)
	FindInBatches(result *[]*model.ResaleListings, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ResaleListings) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IResaleListingsDo
	Assign(attrs ...field.AssignExpr) IResaleListingsDo
	Joins(fields ...field.RelationField) IResaleListingsDo
	Preload(fields ...field.RelationField) IResaleListingsDo
	FirstOrInit() (*model.ResaleListings, error)
	FirstOrCreate() (*model.ResaleListings, error)
	FindByPage(offset int, limit int) (result []*model.ResaleListings, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IResaleListingsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (r resaleListingsDo) Debug() IResaleListingsDo {
	return r.withDO(r.DO.Debug())
}

func (r resaleListingsDo) WithContext(ctx context.Context) IResaleListingsDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r resaleListingsDo) ReadDB() IResaleListingsDo {
	return r.Clauses(dbresolver.Read)
}

func (r resaleListingsDo) WriteDB() IResaleListingsDo {
	return r.Clauses(dbresolver.Write)
}

func (r resaleListingsDo) Session(config *gorm.Session) IResaleListingsDo {
	return r.withDO(r.DO.Session(config))
}

func (r resaleListingsDo) Clauses(conds ...clause.Expression) IResaleListingsDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r resaleListingsDo) Returning(value interface{}, columns ...string) IResaleListingsDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r resaleListingsDo) Not(conds ...gen.Condition) IResaleListingsDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r resaleListingsDo) Or(conds ...gen.Condition) IResaleListingsDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r resaleListingsDo) Select(conds ...field.Expr) IResaleListingsDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r resaleListingsDo) Where(conds ...gen.Condition) IResaleListingsDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r resaleListingsDo) Order(conds ...field.Expr) IResaleListingsDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r resaleListingsDo) Distinct(cols ...field.Expr) IResaleListingsDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r resaleListingsDo) Omit(cols ...field.Expr) IResaleListingsDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r resaleListingsDo) Join(table schema.Tabler, on ...field.Expr) IResaleListingsDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r resaleListingsDo) LeftJoin(table schema.Tabler, on ...field.Expr) IResaleListingsDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r resaleListingsDo) RightJoin(table schema.Tabler, on ...field.Expr) IResaleListingsDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r resaleListingsDo) Group(cols ...field.Expr) IResaleListingsDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r resaleListingsDo) Having(conds ...gen.Condition) IResaleListingsDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r resaleListingsDo) Limit(limit int) IResaleListingsDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r resaleListingsDo) Offset(offset int) IResaleListingsDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r resaleListingsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IResaleListingsDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r resaleListingsDo) Unscoped() IResaleListingsDo {
	return r.withDO(r.DO.Unscoped())
}

func (r resaleListingsDo) Create(values ...*model.ResaleListings) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r resaleListingsDo) CreateInBatches(values []*model.ResaleListings, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r resaleListingsDo) Save(values ...*model.ResaleListings) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r resaleListingsDo) First() (*model.ResaleListings, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListings), nil
	}
}

func (r resaleListingsDo) Take() (*model.ResaleListings, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListings), nil
	}
}

func (r resaleListingsDo) Last() (*model.ResaleListings, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListings), nil
	}
}

func (r resaleListingsDo) Find() ([]*model.ResaleListings, error) {
	result, err := r.DO.Find()
	return result.([]*model.ResaleListings), err
}

func (r resaleListingsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ResaleListings, err error) {
	buf := make([]*model.ResaleListings, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r resaleListingsDo) FindInBatches(result *[]*model.ResaleListings, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r resaleListingsDo) Attrs(attrs ...field.AssignExpr) IResaleListingsDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r resaleListingsDo) Assign(attrs ...field.AssignExpr) IResaleListingsDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r resaleListingsDo) Joins(fields ...field.RelationField) IResaleListingsDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r resaleListingsDo) Preload(fields ...field.RelationField) IResaleListingsDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r resaleListingsDo) FirstOrInit() (*model.ResaleListings, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListings), nil
	}
}

func (r resaleListingsDo) FirstOrCreate() (*model.ResaleListings, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ResaleListings), nil
	}
}

func (r resaleListingsDo) FindByPage(offset int, limit int) (result []*model.ResaleListings, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r resaleListingsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r resaleListingsDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r resaleListingsDo) Delete(models ...*model.ResaleListings) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *resaleListingsDo) withDO(do gen.Dao) *resaleListingsDo {
	r.DO = *do.(*gen.DO)
	return r
}
