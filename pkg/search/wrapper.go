package search

import (
	"gorm.io/gen"
	"gorm.io/gen/field"
)

type Wrapper struct {
	scopes []func(dao gen.Dao) gen.Dao
}

// NewWrapper 创建Wrapper
func NewWrapper() *Wrapper {
	return &Wrapper{
		scopes: make([]func(dao gen.Dao) gen.Dao, 0),
	}
}

// Where 方法：直接调用 gorm-gen 的 Where
func (w *Wrapper) Where(condition ...gen.Condition) *Wrapper {
	if w.scopes == nil {
		w.scopes = make([]func(dao gen.Dao) gen.Dao, 0)
	}
	w.scopes = append(w.scopes, func(dao gen.Dao) gen.Dao {
		return dao.Where(condition...)
	})
	return w
}

// Select 方法：直接调用 gorm-gen 的 Select
func (w *Wrapper) Select(columns ...field.Expr) *Wrapper {
	if w.scopes == nil {
		w.scopes = make([]func(dao gen.Dao) gen.Dao, 0)
	}
	w.scopes = append(w.scopes, func(dao gen.Dao) gen.Dao {
		return dao.Select(columns...)
	})
	return w
}

// OrderBy 方法：直接调用 gorm-gen 的 OrderBy
func (w *Wrapper) OrderBy(columns ...field.Expr) *Wrapper {
	if w.scopes == nil {
		w.scopes = make([]func(dao gen.Dao) gen.Dao, 0)
	}
	w.scopes = append(w.scopes, func(dao gen.Dao) gen.Dao {
		return dao.Order(columns...)
	})
	return w
}

// Preload 方法：直接调用 gorm-gen 的 Preload
func (w *Wrapper) Preload(field field.RelationField) *Wrapper {
	if w.scopes == nil {
		w.scopes = make([]func(dao gen.Dao) gen.Dao, 0)
	}
	w.scopes = append(w.scopes, func(dao gen.Dao) gen.Dao {
		return dao.Preload(field)
	})
	return w
}

// Build 方法：返回最终的 gen.Dao 对象
func (w *Wrapper) Build() func(dao gen.Dao) gen.Dao {
	return func(dao gen.Dao) gen.Dao {
		for _, scope := range w.scopes {
			dao = scope(dao)
		}
		return dao
	}
}
