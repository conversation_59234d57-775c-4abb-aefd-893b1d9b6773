package define

import (
	"marketplace_service/pkg/pagination"
	"time"
)

type (
	GetAdminResaleListingsListReq struct {
		pagination.Pagination
		ID           int64      `form:"id" json:"id,string" search:"type:eq;column:id;table:resale_listings"`                           // 出售单号
		Status       *int32     `form:"status" json:"status" search:"type:eq;column:status;table:resale_listings"`                      // 挂单状态（0-已下架，1-出售中，2-已出售，3-已取消）
		ItemId       string     `form:"item_id" json:"item_id" search:"type:eq;column:item_id;table:resale_listings"`                   // 商品ID
		ItemName     string     `form:"item_name" json:"item_name" search:"type:like;column:item_name;table:resale_listings"`           // 商品名称
		SkuId        string     `form:"sku_id" json:"sku_id" search:"type:eq;column:sku_id;table:resale_listings"`                      // 商品sku_id
		SellerId     string     `form:"seller_id" json:"seller_id" search:"type:eq;column:seller_id;table:resale_listings"`             // 卖家 id
		SellerPhone  string     `form:"seller_phone" json:"seller_phone" search:"type:eq;column:seller_phone;table:resale_listings"`    // 卖家手机号
		Terminal     string     `form:"terminal" json:"terminal" search:"type:eq;column:terminal;table:resale_listings"`                // 终端
		CreatedAtGte *time.Time `form:"created_at_gte" json:"created_at_gte" search:"type:gte;column:created_at;table:resale_listings"` // 创建时间开始
		CreatedAtLte *time.Time `form:"created_at_lte" json:"created_at_lte" search:"type:lte;column:created_at;table:resale_listings"` // 创建时间结束
	}
	GetAdminResaleListingsListData struct {
		ID              int64     `json:"id,string"`        // 出售单号
		SellerId        string    `json:"seller_id"`        // 卖家 id
		SellerNickname  string    `json:"seller_nickname"`  // 卖家昵称
		SellerPhone     string    `json:"seller_phone"`     // 卖家手机号
		ItemId          string    `json:"item_id"`          // 商品 id
		ItemName        string    `json:"item_name"`        // 商品名称
		SkuId           string    `json:"sku_id"`           // SKUID
		ItemIconURL     string    `json:"item_icon_url"`    // 商品主图 URL
		ItemSpecs       string    `json:"item_specs"`       // 商品规格
		SalePrice       int64     `json:"sale_price"`       // 出售单价（分）
		SoldQuantity    int32     `json:"sold_quantity"`    // 成交数量
		ListingQuantity int32     `json:"listing_quantity"` // 挂单数量
		Status          int32     `json:"status"`           // 出售状态状态（0-已下架，1-出售中，2-已出售，3-已取消）
		Terminal        string    `json:"terminal"`         // 终端
		TotalFee        int64     `json:"total_fee"`        // 手续费
		TotalIncome     int64     `json:"total_income"`     // 卖家收入（分）
		CreatedAt       time.Time `json:"created_at"`       // 创建时间/上架时间
	}
	GetAdminResaleListingsListResp struct {
		List  []*GetAdminResaleListingsListData `json:"list"`
		Total int64                             `json:"total"`
	}
)

type (
	GetAdminResaleListingsDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"`
	}
	GetAdminResaleListingsDetailData struct {
		SkuId           string `json:"sku_id"`           // 商品sku_id
		ItemId          string `json:"item_id"`          // 商品ID
		ItemName        string `json:"item_name"`        // 商品名称
		ItemIconURL     string `json:"item_icon_url"`    // 商品主图
		ItemSpecs       string `json:"item_specs"`       // 商品规格
		SalePrice       int64  `json:"sale_price"`       // 售价
		ListingQuantity int32  `json:"listing_quantity"` // 挂单数量
		SoldQuantity    int32  `json:"sold_quantity"`    // 已成交数量
		TotalIncome     int64  `json:"total_income"`     // 卖家收入（分）
		TotalFee        int64  `json:"total_fee"`        // 手续费
	}
	GetAdminResaleListingsDetailResp struct {
		ID             int64                               `json:"id,string"`       // 出售单号
		SellerId       string                              `json:"seller_id"`       // 卖家 ID
		SellerNickname string                              `json:"seller_nickname"` // 卖家昵称
		Status         int32                               `json:"status"`          // 挂单状态（0-已下架，1-出售中，2-已出售，3-已取消）
		Terminal       string                              `json:"terminal"`        // 终端
		AppVersion     string                              `json:"app_version"`     // 版本号
		CreatedAt      time.Time                           `json:"created_at"`      // 创建时间/上架时间
		Operator       string                              `json:"operator"`        // 最后操作人 id
		List           []*GetAdminResaleListingsDetailData `json:"list"`            // 商品信息
	}
)

type (
	UpdateResaleListingsStatusReq struct {
		ID     int64  `json:"id,string" binding:"required"`
		Status *int32 `json:"status" binding:"required"`
	}
	UpdateResaleListingsStatusResp struct {
		ID        int64  `json:"id,string"`
		Status    int32  `json:"status"`
		UpdatedAt string `json:"updated_at"`
	}
)

type (
	GetWebPublishedResaleListingsListReq struct {
		pagination.Pagination
	}

	GetWebPublishedResaleListingsListResp struct {
		List  []*GetWebPublishedResaleListingsListData `json:"list"`
		Total int64                                    `json:"total"`
	}

	GetWebPublishedResaleListingsListData struct {
		ID            int64  `json:"id,string"`      // 主键
		ItemId        string `json:"item_id"`        // 商品ID
		ItemName      string `json:"item_name"`      // 商品名称
		ItemIconUrl   string `json:"item_icon_url"`  // 商品主图
		ItemSpecs     string `json:"item_specs"`     // 商品规格
		SellPrice     int64  `json:"sell_price"`     // 出售单价（分）
		StockQuantity int32  `json:"stock_quantity"` // 预卖出数量
		MinPrice      int64  `json:"min_price"`      // 最低在售
	}
)

type (
	TakeDownResaleListingsFromWebReq struct {
		ID int64 `json:"id,string" binding:"required"` // 主键
	}
	TakeDownResaleListingsFromWebResp struct {
		ID       int64     `json:"id,string"` // 主键
		DownTime time.Time `json:"down_time"` // 下架时间
	}
)

type (
	GetWebResaleListingsItemOnSaleListReq struct {
		pagination.Pagination
		ItemID    string `form:"item_id" json:"item_id" binding:"required"` // 商品 id
		OrderBy   string `form:"order_by" json:"order_by"`                  // 排序字段，sale_price: 按照出售单价排序，created_at: 按照创建时间/发布时间排序
		SortOrder string `form:"sort_order" json:"sort_order"`              // 排序方式，asc：升序，desc：降序
	}

	GetWebResaleListingsItemOnSaleListResp struct {
		List           []*GetWebResaleListingsItemOnSaleListData `json:"list"`              // 列表数据
		Total          int64                                     `json:"total"`             // 列表总数
		TotalOnSaleQty int64                                     `json:"total_on_sale_qty"` // 当前商品所有在售数量
	}

	GetWebResaleListingsItemOnSaleListData struct {
		ID                 int64  `json:"id,string"`             // 主键
		ItemId             string `json:"item_id"`               // 商品ID
		ItemName           string `json:"item_name"`             // 商品名称
		ItemIconUrl        string `json:"item_icon_url"`         // 商品主图
		ItemSpecs          string `json:"item_specs"`            // 商品规格
		SellPrice          int64  `json:"sell_price"`            // 出售单价（分）
		OnSaleQty          int64  `json:"on_sale_qty"`           // 在售数量
		SellerId           string `json:"seller_id"`             // 卖家 id
		SellerNickname     string `json:"seller_nickname"`       // 卖家昵称
		SellerAvatar       string `json:"seller_avatar"`         // 卖家头像
		SellerTotalSoldQty int64  `json:"seller_total_sold_qty"` // 卖家总共售出数量（不限当前商品）
	}
)

type (
	GetWebResaleListingsItemSettlementListReq struct {
		ItemID    string `form:"item_id" json:"item_id" binding:"required"`         // 商品 id
		Quantity  int64  `form:"quantity" json:"quantity" binding:"required,min=1"` // 购买数量
		SellerId  string `form:"seller_id" json:"seller_id"`                        // 卖家 id
		SalePrice int64  `form:"sale_price" json:"sale_price"`                      // 出售单价（分）
	}
	GetWebResaleListingsItemSettlementListSelectedItem struct {
		ResaleListingsID      int64    `json:"resale_listings_id,string"` // 转卖挂单主单 id
		ResaleListingsItemIDs []string `json:"resale_listings_item_ids"`  // 挂单物品 id 列表
	}
	GetWebResaleListingsItemSettlementListInfo struct {
		SellerId                   string                                                `json:"seller_id"`                     // 卖家 id
		SellerNickname             string                                                `json:"seller_nickname"`               // 卖家昵称
		SellerAvatar               string                                                `json:"seller_avatar"`                 // 卖家头像
		SalePrice                  int64                                                 `json:"sale_price"`                    // 出售单价（分）
		OnSaleQty                  int64                                                 `json:"on_sale_qty"`                   // 在售数量
		SelectedQty                int64                                                 `json:"selected_qty"`                  // 选择中数量
		SelectedResaleListingsList []*GetWebResaleListingsItemSettlementListSelectedItem `json:"selected_resale_listings_list"` // 选中的挂单列表
	}
	GetWebResaleListingsItemSettlementListResp struct {
		List        []*GetWebResaleListingsItemSettlementListInfo `json:"list"`         // 列表数据
		TotalAmount int64                                         `json:"total_amount"` // 合计价格（分）
		BuyMaxQty   int64                                         `json:"buy_max_qty"`  // 购买最大数量限制
	}
)

type (
	AddResaleListingsReq struct {
		ItemID       string `json:"item_id" binding:"required"`          // 商品 id
		UserItemID   string `json:"user_item_id"`                        // 持仓 id/背包 id，如果传了表示单个出售
		Quantity     int32  `json:"quantity" binding:"required,min=1"`   // 出售数量
		SalePrice    int64  `json:"sale_price" binding:"required,min=2"` // 卖出价格（分）
		SellPassword string `json:"sell_password"`                       // 出售密码（MD5 32位纯小写）
	}
	AddResaleListingsResp struct {
		ID        int64     `json:"id,string"`  // 主键
		Quantity  int32     `json:"quantity"`   // 出售数量
		CreatedAt time.Time `json:"created_at"` // 创建时间
	}
)
