// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newUserWishlist(db *gorm.DB, opts ...gen.DOOption) userWishlist {
	_userWishlist := userWishlist{}

	_userWishlist.userWishlistDo.UseDB(db, opts...)
	_userWishlist.userWishlistDo.UseModel(&model.UserWishlist{})

	tableName := _userWishlist.userWishlistDo.TableName()
	_userWishlist.ALL = field.NewAsterisk(tableName)
	_userWishlist.ID = field.NewInt64(tableName, "id")
	_userWishlist.UserID = field.NewString(tableName, "user_id")
	_userWishlist.SkuID = field.NewString(tableName, "sku_id")
	_userWishlist.ItemID = field.NewString(tableName, "item_id")
	_userWishlist.Status = field.NewInt32(tableName, "status")
	_userWishlist.CreatedAt = field.NewTime(tableName, "created_at")
	_userWishlist.UpdatedAt = field.NewTime(tableName, "updated_at")
	_userWishlist.IsDel = field.NewField(tableName, "is_del")

	_userWishlist.fillFieldMap()

	return _userWishlist
}

// userWishlist 商城直购白名单表
type userWishlist struct {
	userWishlistDo

	ALL       field.Asterisk
	ID        field.Int64  // 主键
	UserID    field.String // 用户ID
	SkuID     field.String // 商品 sku_id
	ItemID    field.String // 商品ID
	Status    field.Int32  // 状态（1=想要，0=已取消）
	CreatedAt field.Time   // 创建时间
	UpdatedAt field.Time   // 更新时间
	IsDel     field.Field  // 是否删除（0=未删除，1=删除）

	fieldMap map[string]field.Expr
}

func (u userWishlist) Table(newTableName string) *userWishlist {
	u.userWishlistDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userWishlist) As(alias string) *userWishlist {
	u.userWishlistDo.DO = *(u.userWishlistDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userWishlist) updateTableName(table string) *userWishlist {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt64(table, "id")
	u.UserID = field.NewString(table, "user_id")
	u.SkuID = field.NewString(table, "sku_id")
	u.ItemID = field.NewString(table, "item_id")
	u.Status = field.NewInt32(table, "status")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")
	u.IsDel = field.NewField(table, "is_del")

	u.fillFieldMap()

	return u
}

func (u *userWishlist) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userWishlist) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 8)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["sku_id"] = u.SkuID
	u.fieldMap["item_id"] = u.ItemID
	u.fieldMap["status"] = u.Status
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["is_del"] = u.IsDel
}

func (u userWishlist) clone(db *gorm.DB) userWishlist {
	u.userWishlistDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userWishlist) replaceDB(db *gorm.DB) userWishlist {
	u.userWishlistDo.ReplaceDB(db)
	return u
}

type userWishlistDo struct{ gen.DO }

type IUserWishlistDo interface {
	gen.SubQuery
	Debug() IUserWishlistDo
	WithContext(ctx context.Context) IUserWishlistDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserWishlistDo
	WriteDB() IUserWishlistDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserWishlistDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserWishlistDo
	Not(conds ...gen.Condition) IUserWishlistDo
	Or(conds ...gen.Condition) IUserWishlistDo
	Select(conds ...field.Expr) IUserWishlistDo
	Where(conds ...gen.Condition) IUserWishlistDo
	Order(conds ...field.Expr) IUserWishlistDo
	Distinct(cols ...field.Expr) IUserWishlistDo
	Omit(cols ...field.Expr) IUserWishlistDo
	Join(table schema.Tabler, on ...field.Expr) IUserWishlistDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserWishlistDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserWishlistDo
	Group(cols ...field.Expr) IUserWishlistDo
	Having(conds ...gen.Condition) IUserWishlistDo
	Limit(limit int) IUserWishlistDo
	Offset(offset int) IUserWishlistDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserWishlistDo
	Unscoped() IUserWishlistDo
	Create(values ...*model.UserWishlist) error
	CreateInBatches(values []*model.UserWishlist, batchSize int) error
	Save(values ...*model.UserWishlist) error
	First() (*model.UserWishlist, error)
	Take() (*model.UserWishlist, error)
	Last() (*model.UserWishlist, error)
	Find() ([]*model.UserWishlist, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserWishlist, err error)
	FindInBatches(result *[]*model.UserWishlist, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserWishlist) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserWishlistDo
	Assign(attrs ...field.AssignExpr) IUserWishlistDo
	Joins(fields ...field.RelationField) IUserWishlistDo
	Preload(fields ...field.RelationField) IUserWishlistDo
	FirstOrInit() (*model.UserWishlist, error)
	FirstOrCreate() (*model.UserWishlist, error)
	FindByPage(offset int, limit int) (result []*model.UserWishlist, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserWishlistDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userWishlistDo) Debug() IUserWishlistDo {
	return u.withDO(u.DO.Debug())
}

func (u userWishlistDo) WithContext(ctx context.Context) IUserWishlistDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userWishlistDo) ReadDB() IUserWishlistDo {
	return u.Clauses(dbresolver.Read)
}

func (u userWishlistDo) WriteDB() IUserWishlistDo {
	return u.Clauses(dbresolver.Write)
}

func (u userWishlistDo) Session(config *gorm.Session) IUserWishlistDo {
	return u.withDO(u.DO.Session(config))
}

func (u userWishlistDo) Clauses(conds ...clause.Expression) IUserWishlistDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userWishlistDo) Returning(value interface{}, columns ...string) IUserWishlistDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userWishlistDo) Not(conds ...gen.Condition) IUserWishlistDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userWishlistDo) Or(conds ...gen.Condition) IUserWishlistDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userWishlistDo) Select(conds ...field.Expr) IUserWishlistDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userWishlistDo) Where(conds ...gen.Condition) IUserWishlistDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userWishlistDo) Order(conds ...field.Expr) IUserWishlistDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userWishlistDo) Distinct(cols ...field.Expr) IUserWishlistDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userWishlistDo) Omit(cols ...field.Expr) IUserWishlistDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userWishlistDo) Join(table schema.Tabler, on ...field.Expr) IUserWishlistDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userWishlistDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserWishlistDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userWishlistDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserWishlistDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userWishlistDo) Group(cols ...field.Expr) IUserWishlistDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userWishlistDo) Having(conds ...gen.Condition) IUserWishlistDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userWishlistDo) Limit(limit int) IUserWishlistDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userWishlistDo) Offset(offset int) IUserWishlistDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userWishlistDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserWishlistDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userWishlistDo) Unscoped() IUserWishlistDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userWishlistDo) Create(values ...*model.UserWishlist) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userWishlistDo) CreateInBatches(values []*model.UserWishlist, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userWishlistDo) Save(values ...*model.UserWishlist) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userWishlistDo) First() (*model.UserWishlist, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserWishlist), nil
	}
}

func (u userWishlistDo) Take() (*model.UserWishlist, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserWishlist), nil
	}
}

func (u userWishlistDo) Last() (*model.UserWishlist, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserWishlist), nil
	}
}

func (u userWishlistDo) Find() ([]*model.UserWishlist, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserWishlist), err
}

func (u userWishlistDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserWishlist, err error) {
	buf := make([]*model.UserWishlist, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userWishlistDo) FindInBatches(result *[]*model.UserWishlist, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userWishlistDo) Attrs(attrs ...field.AssignExpr) IUserWishlistDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userWishlistDo) Assign(attrs ...field.AssignExpr) IUserWishlistDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userWishlistDo) Joins(fields ...field.RelationField) IUserWishlistDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userWishlistDo) Preload(fields ...field.RelationField) IUserWishlistDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userWishlistDo) FirstOrInit() (*model.UserWishlist, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserWishlist), nil
	}
}

func (u userWishlistDo) FirstOrCreate() (*model.UserWishlist, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserWishlist), nil
	}
}

func (u userWishlistDo) FindByPage(offset int, limit int) (result []*model.UserWishlist, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userWishlistDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userWishlistDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userWishlistDo) Delete(models ...*model.UserWishlist) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userWishlistDo) withDO(do gen.Dao) *userWishlistDo {
	u.DO = *do.(*gen.DO)
	return u
}
