package wae

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/pkg/errors"
)

type ReceiverUpdateRequest struct {
	CNo          string `json:"c_no"`          //供应链内部订单号，必传
	RecvName     string `json:"recv_name"`     //收货人名称，必传
	RecvPhone    string `json:"recv_phone"`    //收货人电话，必传
	RecvZip      string `json:"recv_zip"`      //邮编，必传
	RecvProvince string `json:"recv_province"` //省，必传
	RecvCity     string `json:"recv_city"`     //市，必传
	RecvDistrict string `json:"recv_district"` //区，必传
	RecvAddress  string `json:"recv_address"`  //详细地址，必传
	BuyerComment string `json:"buyer_comment"` //买家备注，可选
}

// ReceiverUpdateResponse 定义接口返回结构
type ReceiverUpdateResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

// ReceiverUpdate 订单修改收货信息
func ReceiverUpdate(ctx context.Context, request *ReceiverUpdateRequest) (bool, error) {
	var rsp ReceiverUpdateResponse
	if err := CommonRequest(
		ctx,
		"/open/v1/order/receiver_info/update",
		request,
		&rsp,
	); err != nil {
		log.Ctx(ctx).Errorf("ReceiverUpdate 订单修改收货信息失败 err: %v", err)
		return false, err
	}
	if rsp.Code == 1000 {
		return true, nil
	}
	return false, errors.New(rsp.Message)
}
