package logic

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"marketplace_service/apps/mall/constant"
	"marketplace_service/global"
	"marketplace_service/pkg/cache"
	"marketplace_service/pkg/utils"
	"marketplace_service/third_party/tmt"
)

func BuildItemInfoMap(ctx context.Context, itemIDs []string) (map[string]*tmt.SteamItemInfo, error) {
	if len(itemIDs) == 0 {
		return map[string]*tmt.SteamItemInfo{}, nil
	}
	itemInfos, err := tmt.GetSteamItemInfoByIds(ctx, itemIDs)
	if err != nil {
		return nil, err
	}

	itemInfoMap := make(map[string]*tmt.SteamItemInfo, len(itemInfos))
	for _, info := range itemInfos {
		itemInfoMap[info.Id] = info
	}
	return itemInfoMap, nil
}

// GetItemInfo 获取商品信息
func GetItemInfo(ctx context.Context, id string) (*tmt.SteamItemInfo, error) {
	var itemInfo *tmt.SteamItemInfo

	cacheKey := constant.GetSteamItemInfoKey(id)
	err := cache.GetFromCache(ctx, cacheKey, &itemInfo)
	if errors.Is(err, redis.Nil) {
		itemInfo, err = GetItemInfoByRemote(ctx, id)
		if err != nil {
			log.Ctx(ctx).Errorf("redis获取商品信息失败 err:%+v", err)
			return nil, err
		}
	} else if err != nil {
		log.Ctx(ctx).Errorf("redis获取商品信息失败 err:%+v", err)
		return nil, err
	}
	return itemInfo, nil
}

// GetItemInfoByRemote 获取商品信息(不使用缓存,强制刷新缓存)
func GetItemInfoByRemote(ctx context.Context, id string) (*tmt.SteamItemInfo, error) {
	var itemInfo *tmt.SteamItemInfo

	itemInfos, err := tmt.GetSteamItemInfoByIds(ctx, []string{id})
	if err != nil {
		log.Ctx(ctx).Errorf("查询商品错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询商品错误")
	}
	if len(itemInfos) > 0 {
		itemInfo = itemInfos[0]
		cacheKey := constant.GetSteamItemInfoKey(id)
		if err := cache.SetToCache(ctx, cacheKey, itemInfo, constant.SteamItemInfoTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置商品信息缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置商品信息缓存成功 itemId:%v", id)
	}
	return itemInfo, err
}

// GetItemInfos 获取商品信息列表
func GetItemInfos(ctx context.Context, ids ...string) ([]*tmt.SteamItemInfo, error) {
	result := make([]*tmt.SteamItemInfo, 0)
	var queryItemIds []string
	var itemIdRedisKey []string
	for _, id := range ids {
		itemIdRedisKey = append(itemIdRedisKey, constant.GetSteamItemInfoKey(id))
	}
	itemInfoStrList, _ := global.REDIS.MGet(ctx, itemIdRedisKey...).Result()

	if itemInfoStrList != nil && len(itemInfoStrList) > 0 {
		for k, itemStr := range itemInfoStrList {
			if itemStr != nil && itemStr != "" {
				info := &tmt.SteamItemInfo{}
				err := utils.MsgpackUnmarshal([]byte(itemStr.(string)), info)
				if err != nil {
					log.Ctx(ctx).Errorf("redis商品信息转换结构体失败 err:%+v", err)
				} else {
					result = append(result, info)
				}
			} else {
				queryItemIds = append(queryItemIds, ids[k])
			}
		}
	}

	if len(queryItemIds) > 0 {
		itemInfos, err := tmt.GetSteamItemInfoByIds(ctx, queryItemIds)
		if err != nil {
			log.Ctx(ctx).Errorf("查询商品列表错误 err:%+v", err)
			return nil, response.ParamErr.SetMsg("查询商品列表错误")
		}

		for _, item := range itemInfos {
			result = append(result, item)
			cacheKey := constant.GetSteamItemInfoKey(item.Id)
			if err := cache.SetToCache(ctx, cacheKey, item, constant.SteamItemInfoTTL); err != nil {
				log.Ctx(ctx).Errorf("redis设置商品信息缓存失败 err:%+v", err)
			}
			log.Ctx(ctx).Infof("redis设置商品信息缓存成功 itemId:%v", item.Id)
		}
	}
	return result, nil
}

// GetItemInfoMap 获取商品信息切片
func GetItemInfoMap(ctx context.Context, ids ...string) (map[string]*tmt.SteamItemInfo, error) {
	itemInfos, err := GetItemInfos(ctx, ids...)
	if err != nil {
		return nil, err
	}
	itemInfoMap := make(map[string]*tmt.SteamItemInfo, len(itemInfos))
	for _, item := range itemInfos {
		itemInfoMap[item.Id] = item
	}
	return itemInfoMap, nil
}

// GetItemInfosByRemote 获取商品信息列表(不使用缓存,强制刷新缓存)
func GetItemInfosByRemote(ctx context.Context, ids ...string) ([]*tmt.SteamItemInfo, error) {
	result := make([]*tmt.SteamItemInfo, 0)
	itemInfos, err := tmt.GetSteamItemInfoByIds(ctx, ids)
	if err != nil {
		log.Ctx(ctx).Errorf("查询remote商品列表错误 err:%+v", err)
		return nil, response.ParamErr.SetMsg("查询remote商品列表错误")
	}

	for _, item := range itemInfos {
		result = append(result, item)
		cacheKey := constant.GetSteamItemInfoKey(item.Id)
		if err := cache.SetToCache(ctx, cacheKey, item, constant.SteamItemInfoTTL); err != nil {
			log.Ctx(ctx).Errorf("redis设置商品信息缓存失败 err:%+v", err)
		}
		log.Ctx(ctx).Infof("redis设置商品信息缓存成功 itemId:%v", item.Id)
	}
	return result, nil
}

// GetItemInfosByRemoteMap 获取商品信息切片(不使用缓存,强制刷新缓存)
func GetItemInfosByRemoteMap(ctx context.Context, ids ...string) (map[string]*tmt.SteamItemInfo, error) {
	itemInfos, err := GetItemInfosByRemote(ctx, ids...)
	if err != nil {
		return nil, err
	}
	itemInfoMap := make(map[string]*tmt.SteamItemInfo, len(itemInfos))
	for _, item := range itemInfos {
		itemInfoMap[item.Id] = item
	}
	return itemInfoMap, nil
}
