# 技术栈

## 核心技术

- **编程语言**: Go 1.19
- **Web 框架**: Gin (HTTP 路由和中间件)
- **微服务**: go-micro v4 配合 HTTP 服务器插件
- **数据库**: MySQL 配合 GORM ORM
- **缓存**: Redis
- **消息队列**: Kafka
- **文档**: Swagger/OpenAPI 配合 swaggo

## 关键库

- **数据库**: 
  - `gorm.io/gorm` - ORM 框架
  - `gorm.io/gen` - 类型安全查询代码生成
  - `gorm.io/driver/mysql` - MySQL 驱动
- **HTTP**: `github.com/gin-gonic/gin`
- **Redis**: `github.com/go-redis/redis/v8`
- **消息**: 通过公共库集成 Kafka
- **可观测性**: OpenTelemetry 链路追踪
- **工具库**: 
  - `github.com/shopspring/decimal` - 十进制运算
  - `github.com/bwmarrin/snowflake` - ID 生成
  - `github.com/vmihailenco/msgpack/v5` - 序列化

## 构建和开发命令

```bash
# 生成 Swagger 文档
go generate ./cmd

# 运行服务（开发环境）
ENV=dev go run cmd/main.go

# 构建服务
go build -o marketplace_service cmd/main.go

# 生成数据库模型和查询
go run cmd/codegen/mall/mall.go
```

## 配置

- 基于环境的配置文件位于 `configs/` 目录
- 支持 dev、sit 和 prod 环境
- YAML 配置格式
- 服务默认运行在 8801 端口

## 代码生成

- 使用 `gorm.io/gen` 生成数据库模型和查询
- Swagger 文档从代码注释自动生成
- 生成的文件带有 `.gen.go` 后缀，不应手动编辑