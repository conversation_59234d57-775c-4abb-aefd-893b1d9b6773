package admin

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// GetResaleOrderList 获取转卖订单列表
// @Summary 获取转卖订单列表
// @Description 管理端获取转卖订单分页列表
// @Tags 管理端-转卖订单管理
// @x-apifox-folder "管理端/转卖订单管理"
// @Param data query define.GetAdminResaleOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetAdminResaleOrderListResp}
// @Router /admin/v1/resale_order/list [get]
// @Security Bearer
// @produce  json
func GetResaleOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAdminResaleOrderListReq{}, s.GetAdminResaleOrderList)
}

// GetResaleOrderDetail 获取转卖订单详情
// @Summary 获取转卖订单详情
// @Description 管理端获取转卖订单详细信息
// @Tags 管理端-转卖订单管理
// @x-apifox-folder "管理端/转卖订单管理"
// @Param data query define.GetAdminResaleOrderDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAdminResaleOrderDetailResp}
// @Router /admin/v1/resale_order/detail [get]
// @Security Bearer
// @produce  json
func GetResaleOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAdminResaleOrderDetailReq{}, s.GetAdminResaleOrderDetail)
}

// ExportResaleOrderList
// @Summary 导出转卖订单列表
// @Description 导出转卖订单列表，下载文件
// @Tags 管理端-转卖订单管理
// @x-apifox-folder "管理端/转卖订单管理"
// @Param data query define.GetAdminResaleOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAdminResaleOrderListResp}
// @Router  /admin/v1/resale_order/export [get]
// @Security Bearer
// @produce  json
func ExportResaleOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.GetAdminResaleOrderListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		g.Fail(ctx, response.ParamErr.SetMsg(err.Error()))
		return
	}
	err := s.ExportResaleOrderList(req)
	if err != nil {
		g.Fail(ctx, err)
		return
	}
}
