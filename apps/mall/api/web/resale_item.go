package web

import (
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
	"marketplace_service/apps/mall/define"
	"marketplace_service/apps/mall/service"
)

// ResaleItemBuy
// @Summary 转卖商品购买
// @Description 转卖商品购买
// @Tags 用户端-转卖商品
// @x-apifox-folder "用户端/转卖商品"
// @Param data body define.ResaleItemBuyReq true "获取参数"
// @Success 200 {object} response.Data{data=define.ResaleItemBuyResp}
// @Router  /web/v1/resale_item/buy [post]
// @Security Bearer
// @produce  json
func ResaleItemBuy(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ResaleItemBuyReq{}, s.ResaleItemBuy)
}
