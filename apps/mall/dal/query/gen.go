// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                  = new(Query)
	CommonConfig       *commonConfig
	HomeFeed           *homeFeed
	HomeFeedStats      *homeFeedStats
	ItemStatistic      *itemStatistic
	MallItem           *mallItem
	ResaleItem         *resaleItem
	ResaleListings     *resaleListings
	ResaleListingsItem *resaleListingsItem
	ResaleOrder        *resaleOrder
	ResaleOrderItem    *resaleOrderItem
	TradeOrder         *tradeOrder
	TradeOrderFreight  *tradeOrderFreight
	TradeOrderItem     *tradeOrderItem
	UserWishlist       *userWishlist
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	CommonConfig = &Q.CommonConfig
	HomeFeed = &Q.HomeFeed
	HomeFeedStats = &Q.HomeFeedStats
	ItemStatistic = &Q.ItemStatistic
	MallItem = &Q.MallItem
	ResaleItem = &Q.ResaleItem
	ResaleListings = &Q.ResaleListings
	ResaleListingsItem = &Q.ResaleListingsItem
	ResaleOrder = &Q.ResaleOrder
	ResaleOrderItem = &Q.ResaleOrderItem
	TradeOrder = &Q.TradeOrder
	TradeOrderFreight = &Q.TradeOrderFreight
	TradeOrderItem = &Q.TradeOrderItem
	UserWishlist = &Q.UserWishlist
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                 db,
		CommonConfig:       newCommonConfig(db, opts...),
		HomeFeed:           newHomeFeed(db, opts...),
		HomeFeedStats:      newHomeFeedStats(db, opts...),
		ItemStatistic:      newItemStatistic(db, opts...),
		MallItem:           newMallItem(db, opts...),
		ResaleItem:         newResaleItem(db, opts...),
		ResaleListings:     newResaleListings(db, opts...),
		ResaleListingsItem: newResaleListingsItem(db, opts...),
		ResaleOrder:        newResaleOrder(db, opts...),
		ResaleOrderItem:    newResaleOrderItem(db, opts...),
		TradeOrder:         newTradeOrder(db, opts...),
		TradeOrderFreight:  newTradeOrderFreight(db, opts...),
		TradeOrderItem:     newTradeOrderItem(db, opts...),
		UserWishlist:       newUserWishlist(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	CommonConfig       commonConfig
	HomeFeed           homeFeed
	HomeFeedStats      homeFeedStats
	ItemStatistic      itemStatistic
	MallItem           mallItem
	ResaleItem         resaleItem
	ResaleListings     resaleListings
	ResaleListingsItem resaleListingsItem
	ResaleOrder        resaleOrder
	ResaleOrderItem    resaleOrderItem
	TradeOrder         tradeOrder
	TradeOrderFreight  tradeOrderFreight
	TradeOrderItem     tradeOrderItem
	UserWishlist       userWishlist
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                 db,
		CommonConfig:       q.CommonConfig.clone(db),
		HomeFeed:           q.HomeFeed.clone(db),
		HomeFeedStats:      q.HomeFeedStats.clone(db),
		ItemStatistic:      q.ItemStatistic.clone(db),
		MallItem:           q.MallItem.clone(db),
		ResaleItem:         q.ResaleItem.clone(db),
		ResaleListings:     q.ResaleListings.clone(db),
		ResaleListingsItem: q.ResaleListingsItem.clone(db),
		ResaleOrder:        q.ResaleOrder.clone(db),
		ResaleOrderItem:    q.ResaleOrderItem.clone(db),
		TradeOrder:         q.TradeOrder.clone(db),
		TradeOrderFreight:  q.TradeOrderFreight.clone(db),
		TradeOrderItem:     q.TradeOrderItem.clone(db),
		UserWishlist:       q.UserWishlist.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                 db,
		CommonConfig:       q.CommonConfig.replaceDB(db),
		HomeFeed:           q.HomeFeed.replaceDB(db),
		HomeFeedStats:      q.HomeFeedStats.replaceDB(db),
		ItemStatistic:      q.ItemStatistic.replaceDB(db),
		MallItem:           q.MallItem.replaceDB(db),
		ResaleItem:         q.ResaleItem.replaceDB(db),
		ResaleListings:     q.ResaleListings.replaceDB(db),
		ResaleListingsItem: q.ResaleListingsItem.replaceDB(db),
		ResaleOrder:        q.ResaleOrder.replaceDB(db),
		ResaleOrderItem:    q.ResaleOrderItem.replaceDB(db),
		TradeOrder:         q.TradeOrder.replaceDB(db),
		TradeOrderFreight:  q.TradeOrderFreight.replaceDB(db),
		TradeOrderItem:     q.TradeOrderItem.replaceDB(db),
		UserWishlist:       q.UserWishlist.replaceDB(db),
	}
}

type queryCtx struct {
	CommonConfig       ICommonConfigDo
	HomeFeed           IHomeFeedDo
	HomeFeedStats      IHomeFeedStatsDo
	ItemStatistic      IItemStatisticDo
	MallItem           IMallItemDo
	ResaleItem         IResaleItemDo
	ResaleListings     IResaleListingsDo
	ResaleListingsItem IResaleListingsItemDo
	ResaleOrder        IResaleOrderDo
	ResaleOrderItem    IResaleOrderItemDo
	TradeOrder         ITradeOrderDo
	TradeOrderFreight  ITradeOrderFreightDo
	TradeOrderItem     ITradeOrderItemDo
	UserWishlist       IUserWishlistDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		CommonConfig:       q.CommonConfig.WithContext(ctx),
		HomeFeed:           q.HomeFeed.WithContext(ctx),
		HomeFeedStats:      q.HomeFeedStats.WithContext(ctx),
		ItemStatistic:      q.ItemStatistic.WithContext(ctx),
		MallItem:           q.MallItem.WithContext(ctx),
		ResaleItem:         q.ResaleItem.WithContext(ctx),
		ResaleListings:     q.ResaleListings.WithContext(ctx),
		ResaleListingsItem: q.ResaleListingsItem.WithContext(ctx),
		ResaleOrder:        q.ResaleOrder.WithContext(ctx),
		ResaleOrderItem:    q.ResaleOrderItem.WithContext(ctx),
		TradeOrder:         q.TradeOrder.WithContext(ctx),
		TradeOrderFreight:  q.TradeOrderFreight.WithContext(ctx),
		TradeOrderItem:     q.TradeOrderItem.WithContext(ctx),
		UserWishlist:       q.UserWishlist.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
