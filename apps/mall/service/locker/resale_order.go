package locker

import (
	"fmt"
	"time"
)

// ResaleOrderAction 转卖订单相关行为枚举
type ResaleOrderAction string

const (
	ResaleBuy      ResaleOrderAction = "resale_buy" // 转卖购买
	ResaleUserItem ResaleOrderAction = "user_item"  // 转卖背包物品
)

type ResaleOrderLock struct {
	ac  ResaleOrderAction // 行为
	tag string            // 唯一标识
}

func (p *ResaleOrderLock) GetCacheKey() string {
	return fmt.Sprintf("marketplace_service:resale_order:locker:%s:%s", p.ac, p.tag)
}

func (p *ResaleOrderLock) LockTime() time.Duration {
	return time.Second * 30
}

func NewResaleOrderLock(tag string, ac ResaleOrderAction) *ResaleOrderLock {
	return &ResaleOrderLock{tag: tag, ac: ac}
}
