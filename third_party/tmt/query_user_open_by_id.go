package tmt

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type QueryUserOpenInfo struct {
	Id       string        `json:"_id" form:"_id"`
	OpenInfo *UserOpenInfo `json:"open_info" form:"open_info"`
}

type UserOpenInfo struct {
	OpenUserId  string `json:"open_user_id" form:"open_user_id"`
	IsCertified bool   `json:"is_certified" form:"is_certified"`
}

type QueryUserOpenByIdResp struct {
	Code int32              `json:"code" form:"code"`
	Desc string             `json:"desc" form:"desc"`
	Data *QueryUserOpenInfo `json:"data" form:"data"`
}

func QueryUserOpenById(ctx context.Context, userId string) (*QueryUserOpenInfo, error) {
	rsp := &QueryUserOpenByIdResp{}
	req, err := request.Tmt()
	if err != nil {
		return nil, err
	}
	err = req.Call(
		ctx,
		"open/user/v1/query_user_open/"+userId,
		&rsp,
		utilRequest.WithMethodGet(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data, err
}
