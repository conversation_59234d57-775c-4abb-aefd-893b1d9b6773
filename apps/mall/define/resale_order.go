package define

import (
	"marketplace_service/pkg/pagination"
	"time"
)

type (
	GetAdminResaleOrderListReq struct {
		pagination.Pagination
		ID               int64      `form:"id,string" json:"id,string" search:"type:eq;column:id;table:resale_order"`                                                 // 合同编号
		ResaleListingsId int64      `form:"resale_listings_id,string" json:"resale_listings_id,string" search:"type:eq;column:resale_listings_id;table:resale_order"` // 出售单号
		Status           *int32     `form:"status" json:"status" search:"type:eq;column:status;table:resale_order"`                                                   // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
		BuyerId          string     `form:"buyer_id" json:"buyer_id" search:"type:eq;column:buyer_id;table:resale_order"`                                             // 购买用户ID
		BuyerPhone       string     `form:"buyer_phone" json:"buyer_phone" search:"type:eq;column:buyer_phone;table:resale_order"`                                    // 购买用户手机号码
		SellerId         string     `form:"seller_id" json:"seller_id" search:"type:eq;column:seller_id;table:resale_order"`                                          // 出售用户ID
		SellerPhone      string     `form:"seller_phone" json:"seller_phone" search:"type:eq;column:seller_phone;table:resale_order"`                                 // 出售用户手机号码
		ItemId           string     `form:"item_id" json:"item_id" search:"type:eq;column:item_id;table:resale_order"`                                                // 商品ID
		ItemName         string     `form:"item_name" json:"item_name" search:"type:like;column:item_name;table:resale_order"`                                        // 商品名称
		SkuId            string     `form:"sku_id" json:"sku_id" search:"type:eq;column:sku_id;table:resale_order"`                                                   // 商品sku_id
		Terminal         string     `form:"terminal" json:"terminal" search:"type:eq;column:terminal;table:resale_order"`                                             // 终端
		CreatedAtGte     *time.Time `form:"created_at_gte" json:"created_at_gte" search:"type:gte;column:created_at;table:resale_order"`                              // 创建时间开始
		CreatedAtLte     *time.Time `form:"created_at_lte" json:"created_at_lte" search:"type:lte;column:created_at;table:resale_order"`                              // 创建时间结束
	}

	GetAdminResaleOrderListData struct {
		ID               int64     `json:"id,string"`                        // 合同编号
		ResaleListingsId int64     `json:"resale_listings_id,string"`        // 出售单号
		SkuId            string    `json:"sku_id"`                           // 商品sku_id
		ItemId           string    `json:"item_id"`                          // 商品ID
		ItemName         string    `json:"item_name"`                        // 商品名称
		ItemIconUrl      string    `json:"item_icon_url"`                    // 商品主图
		ItemSpecs        string    `json:"item_specs"`                       // 商品规格
		SalePrice        int64     `json:"sale_price"`                       // 售价
		TotalAmount      int64     `json:"total_amount"`                     // 总金额
		PayAmount        int64     `json:"pay_amount"`                       // 支付金额
		TotalFee         int64     `json:"total_fee"`                        // 手续费
		Quantity         int32     `json:"quantity"`                         // 购买数量
		BuyerId          string    `json:"buyer_id"`                         // 购买用户ID
		BuyerNickname    string    `json:"buyer_nickname"`                   // 购买用户昵称
		BuyerPhone       string    `json:"buyer_phone"`                      // 购买用户手机号码
		SellerId         string    `json:"seller_id"`                        // 出售用户ID
		SellerNickname   string    `json:"seller_nickname"`                  // 出售用户昵称
		SellerPhone      string    `form:"seller_phone" json:"seller_phone"` // 出售用户手机号码
		Status           int32     `json:"status"`                           // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
		Terminal         string    `json:"terminal"`                         // 终端
		CreatedAt        time.Time `json:"created_at"`                       // 创建时间
	}

	GetAdminResaleOrderListResp struct {
		List  []*GetAdminResaleOrderListData `json:"list"`
		Total int64                          `json:"total"`
	}
)

type (
	GetAdminResaleOrderDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"`
	}

	GetAdminResaleOrderDetailData struct {
		SkuId            string `json:"sku_id"`                    // 商品sku_id
		ItemId           string `json:"item_id"`                   // 商品ID
		ItemName         string `json:"item_name"`                 // 商品名称
		ItemIconUrl      string `json:"item_icon_url"`             // 商品主图
		ItemSpecs        string `json:"item_specs"`                // 商品规格
		SalePrice        int64  `json:"sale_price"`                // 售价
		TotalAmount      int64  `json:"total_amount"`              // 总金额
		PayAmount        int64  `json:"pay_amount"`                // 支付金额
		TotalFee         int64  `json:"total_fee"`                 // 手续费
		Quantity         int32  `json:"quantity"`                  // 购买数量
		ResaleListingsId int64  `json:"resale_listings_id,string"` // 出售单号
	}

	GetAdminResaleOrderDetailResp struct {
		ID             int64                          `json:"id,string"`       // 合同编号
		BuyerId        string                         `json:"buyer_id"`        // 购买用户ID
		BuyerNickname  string                         `json:"buyer_nickname"`  // 购买用户昵称
		SellerId       string                         `json:"seller_id"`       // 出售用户ID
		SellerNickname string                         `json:"seller_nickname"` // 出售用户昵称
		Status         int32                          `json:"status"`          // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
		Terminal       string                         `json:"terminal"`        // 终端
		AppVersion     string                         `json:"app_version"`     // 版本号
		PaymentAt      *time.Time                     `json:"payment_at"`      // 支付时间
		TransferAt     *time.Time                     `json:"transfer_at"`     // 转移时间
		FinishedAt     *time.Time                     `json:"finished_at"`     // 交易完成时间
		CreatedAt      time.Time                      `json:"created_at"`      // 创建时间
		ItemInfo       *GetAdminResaleOrderDetailData `json:"item_info"`       // 商品信息
	}
)

type (
	GetWebResaleOrderBuyListReq struct {
		pagination.Pagination
		Status *int32 `form:"status" json:"status"` // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
	}

	GetWebResaleOrderBuyListResp struct {
		List  []*GetWebResaleOrderBuyListData `json:"list"`
		Total int64                           `json:"total"`
	}

	GetWebResaleOrderBuyListData struct {
		ID             int64  `json:"id,string"`       // 主键
		SellerNickname string `json:"seller_nickname"` // 出售用户昵称
		SellerAvatar   string `json:"seller_avatar"`   // 出售用户头像
		ItemId         string `json:"item_id"`         // 商品ID
		ItemName       string `json:"item_name"`       // 商品名称
		ItemIconUrl    string `json:"item_icon_url"`   // 商品主图
		ItemSpecs      string `json:"item_specs"`      // 商品规格
		SalePrice      int64  `json:"sale_price"`      // 售价
		TotalAmount    int64  `json:"total_amount"`    // 总金额
		Quantity       int32  `json:"quantity"`        // 购买数量
		Status         int32  `json:"status"`          // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
		SoldOut        int32  `json:"sold_out"`        // 商品转卖和直购都下架状态 0=正常，1=下架
	}
)

type (
	GetWebResaleOrderSaleListReq struct {
		pagination.Pagination
		Status *int32 `form:"status" json:"status"` // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
	}

	GetWebResaleOrderSaleListResp struct {
		List  []*GetWebResaleOrderSaleListData `json:"list"`
		Total int64                            `json:"total"`
	}

	GetWebResaleOrderSaleListData struct {
		ID            int64  `json:"id,string"`      // 主键
		BuyerNickname string `json:"buyer_nickname"` // 购买用户昵称
		BuyerAvatar   string `json:"buyer_avatar"`   // 购买用户头像
		ItemId        string `json:"item_id"`        // 商品ID
		ItemName      string `json:"item_name"`      // 商品名称
		ItemIconUrl   string `json:"item_icon_url"`  // 商品主图
		ItemSpecs     string `json:"item_specs"`     // 商品规格
		SalePrice     int64  `json:"sale_price"`     // 售价
		TotalAmount   int64  `json:"total_amount"`   // 总金额
		Quantity      int32  `json:"quantity"`       // 购买数量
		Status        int32  `json:"status"`         // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
		SoldOut       int32  `json:"sold_out"`       // 商品转卖和直购都下架状态 0=正常，1=下架
	}
)

type (
	GetWebResaleOrderBuyDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"`
	}

	GetWebResaleOrderBuyDetailData struct {
		ItemId      string `json:"item_id"`       // 商品ID
		ItemName    string `json:"item_name"`     // 商品名称
		ItemIconUrl string `json:"item_icon_url"` // 商品主图
		ItemSpecs   string `json:"item_specs"`    // 商品规格
		SalePrice   int64  `json:"sale_price"`    // 售价
		TotalAmount int64  `json:"total_amount"`  // 总金额
		PayAmount   int64  `json:"pay_amount"`    // 支付金额
		Quantity    int32  `json:"quantity"`      // 购买数量
		SoldOut     int32  `json:"sold_out"`      // 商品转卖和直购都下架状态 0=正常，1=下架
	}

	GetWebResaleOrderBuyDetailResp struct {
		ID             int64                           `json:"id,string"`        // 合同编号
		Status         int32                           `json:"status"`           // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
		BuyerId        string                          `json:"buyer_id"`         // 购买用户ID
		BuyerNickname  string                          `json:"buyer_nickname"`   // 购买用户昵称
		BuyerRealName  string                          `json:"buyer_real_name"`  // 购买用户真实名称
		SellerId       string                          `json:"seller_id"`        // 出售用户ID
		SellerNickname string                          `json:"seller_nickname"`  // 出售用户昵称
		SellerRealName string                          `json:"seller_real_name"` // 出售用户真实名称
		ItemName       string                          `json:"item_name"`        // 商品名称
		SalePrice      int64                           `json:"sale_price"`       // 售价
		Quantity       int32                           `json:"quantity"`         // 购买数量
		TotalAmount    int64                           `json:"total_amount"`     // 总金额
		TotalFee       int64                           `json:"total_fee"`        // 手续费
		CreatedAt      time.Time                       `json:"created_at"`       // 创建时间
		PaymentAt      *time.Time                      `json:"payment_at"`       // 支付时间
		FinishedAt     *time.Time                      `json:"finished_at"`      // 交易完成时间
		ItemInfo       *GetWebResaleOrderBuyDetailData `json:"item_info"`        // 商品信息
	}
)

type (
	GetWebResaleOrderSaleDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"`
	}

	GetWebResaleOrderSaleDetailData struct {
		ItemId             string `json:"item_id"`              // 商品ID
		ItemName           string `json:"item_name"`            // 商品名称
		ItemIconUrl        string `json:"item_icon_url"`        // 商品主图
		ItemSpecs          string `json:"item_specs"`           // 商品规格
		SalePrice          int64  `json:"sale_price"`           // 售价
		TotalAmount        int64  `json:"total_amount"`         // 总金额
		SellerIncomeAmount int64  `json:"seller_income_amount"` // 卖方收入
		Quantity           int32  `json:"quantity"`             // 购买数量
		SoldOut            int32  `json:"sold_out"`             // 下架状态 0=正常，1=下架
	}

	GetWebResaleOrderSaleDetailResp struct {
		ID                 int64                            `json:"id,string"`            // 合同编号
		Status             int32                            `json:"status"`               // 订单状态（0=待支付，10=已支付，20=转移中，30=已完成，40=已取消）
		BuyerId            string                           `json:"buyer_id"`             // 购买用户ID
		BuyerNickname      string                           `json:"buyer_nickname"`       // 购买用户昵称
		BuyerRealName      string                           `json:"buyer_real_name"`      // 购买用户真实名称
		SellerId           string                           `json:"seller_id"`            // 出售用户ID
		SellerNickname     string                           `json:"seller_nickname"`      // 出售用户昵称
		SellerRealName     string                           `json:"seller_real_name"`     // 出售用户真实名称
		ItemName           string                           `json:"item_name"`            // 商品名称
		SalePrice          int64                            `json:"sale_price"`           // 售价
		Quantity           int32                            `json:"quantity"`             // 购买数量
		TotalAmount        int64                            `json:"total_amount"`         // 总金额
		TotalFee           int64                            `json:"total_fee"`            // 手续费
		SellerIncomeAmount int64                            `json:"seller_income_amount"` // 卖方收入
		CreatedAt          time.Time                        `json:"created_at"`           // 创建时间
		PaymentAt          *time.Time                       `json:"payment_at"`           // 支付时间
		FinishedAt         *time.Time                       `json:"finished_at"`          // 交易完成时间
		ItemInfo           *GetWebResaleOrderSaleDetailData `json:"item_info"`            // 商品信息
	}
)

type (
	GetWebResaleOrderRecentListReq struct {
		pagination.Pagination
		ItemId string `form:"item_id" json:"item_id" binding:"required"` // 商品ID
	}

	GetWebResaleOrderRecentListResp struct {
		List  []*GetWebResaleOrderRecentListData `json:"list"`
		Total int64                              `json:"total"`
	}

	GetWebResaleOrderRecentListData struct {
		BuyerNickname string    `json:"buyer_nickname"` // 购买用户昵称
		BuyerAvatar   string    `json:"buyer_avatar"`   // 购买用户头像
		ItemId        string    `json:"item_id"`        // 商品ID
		ItemName      string    `json:"item_name"`      // 商品名称
		ItemSpecs     string    `json:"item_specs"`     // 商品规格
		SalePrice     int64     `json:"sale_price"`     // 售价
		Quantity      int32     `json:"quantity"`       // 购买数量
		CreatedAt     time.Time `json:"created_at"`     // 创建时间
	}
)

type (
	ReleaseUserItemsReq struct {
		ID int64 `form:"id,string" json:"id,string"` //id
	}

	ReleaseUserItemsResp struct {
	}
)

type (
	LockedHandlerReq struct {
		ID int64 `form:"id,string" json:"id,string"` //id
	}

	LockedHandlerResp struct {
	}
)

type (
	ResaleItemTransferHandlerReq struct {
		ID int64 `form:"id,string" json:"id,string"` //id
	}

	ResaleItemTransferHandlerResp struct {
	}
)

type (
	ResaleItemTransfer struct {
		ID int64 `json:"id"` // 订单id
	}
)
