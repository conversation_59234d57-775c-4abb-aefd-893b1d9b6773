package wae

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/request"
	"encoding/json"
	"github.com/duke-git/lancet/v2/cryptor"
	"github.com/pkg/errors"
	"marketplace_service/global"
	"sort"
	"strings"
	"time"
)

const (
	NULL = ""
)

func convertToString(v interface{}) (str string) {
	if v == nil {
		return NULL
	}
	var (
		bs  []byte
		err error
	)
	if bs, err = json.Marshal(v); err != nil {
		return NULL
	}
	str = string(bs)
	return
}

// GetString 获取参数转换string
func GetString(params map[string]interface{}, key string) string {
	if params == nil {
		return NULL
	}
	value, ok := params[key]
	if !ok {
		return NULL
	}
	v, ok := value.(string)
	if !ok {
		return convertToString(value)
	}
	return v
}

func EncodeURLParams(params map[string]interface{}) string {
	if params == nil {
		return NULL
	}
	var (
		buf  strings.Builder
		keys []string
	)
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		if v := GetString(params, k); v != NULL {
			buf.WriteString(k)
			buf.WriteString(v)
		}
	}
	if buf.Len() <= 0 {
		return NULL
	}
	return buf.String()
}

func generateSign(data map[string]interface{}, secret string) string {
	params := secret
	params += EncodeURLParams(data)
	params += secret
	return cryptor.Md5String(params)
}

func buildBaseParams(appKey string) map[string]interface{} {
	timestamp := time.Now().UnixMilli()
	return map[string]interface{}{
		"app_key":   appKey,
		"format":    "json",
		"timestamp": timestamp,
		"version":   "2.0",
	}
}

func CommonRequest(ctx context.Context, url string, params any, rsp any) error {
	waeConfig := global.GlobalConfig.Wae
	if waeConfig == nil {
		log.Ctx(ctx).Error("未配置供应链配置")
		return errors.New("未配置供应链配置")
	}
	signParams := buildBaseParams(waeConfig.AppKey)
	// 判断参数是否是 map 或 slice/array 等结构
	switch v := params.(type) {
	case map[string]interface{}:
		// 如果是 map，则过滤空值字段
		for k, val := range v {
			if val == nil || val == "" {
				delete(v, k)
			}
		}
		if len(v) > 0 {
			bizContent, _ := json.Marshal(v)
			signParams["biz_content"] = string(bizContent)
		}
	case []interface{}:
		// 如果是数组，则直接序列化为字符串
		if len(v) > 0 {
			bizContent, _ := json.Marshal(v)
			signParams["biz_content"] = string(bizContent)
		}
	default:
		// 其他类型尝试用 json.Marshal 处理
		if params != nil {
			bizContent, _ := json.Marshal(params)
			signParams["biz_content"] = string(bizContent)
		}
	}
	sign := generateSign(signParams, waeConfig.AppSecret)
	// 打印签名
	signParams["sign"] = sign
	err := request.New(
		ctx,
		request.WithUrl(waeConfig.Host+url),
		request.WithMethodPost(),
		request.WithParams(signParams),
		request.WithTimeOut(time.Second*30),
	).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("供应链请求发送失败 err: %+v", err)
		return err
	}
	return nil
}
