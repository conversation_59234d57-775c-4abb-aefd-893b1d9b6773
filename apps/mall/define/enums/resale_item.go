package enums

// ResaleItemStatus 转卖商品状态
type ResaleItemStatus int32

func (r ResaleItemStatus) Val() int32 {
	return int32(r)
}

const (
	// ResaleItemStatusDel 已删除
	ResaleItemStatusDel ResaleItemStatus = -1
	// ResaleItemStatusWaiting 待上架
	ResaleItemStatusWaiting ResaleItemStatus = 0
	// ResaleItemStatusUp 已上架
	ResaleItemStatusUp ResaleItemStatus = 1
	// ResaleItemStatusDown 已下架
	ResaleItemStatusDown ResaleItemStatus = 2
)

// ResaleItemResaleStatus 转卖商品转卖状态
type ResaleItemResaleStatus int32

func (r ResaleItemResaleStatus) Val() int32 {
	return int32(r)
}

const (
	// ResaleItemResaleStatusClose 关闭
	ResaleItemResaleStatusClose ResaleItemResaleStatus = 0
	// ResaleItemResaleStatusOpen 开启
	ResaleItemResaleStatusOpen ResaleItemResaleStatus = 1
)

// ResaleItemTradeFrequencyType 转卖商品交易频次类型
type ResaleItemTradeFrequencyType int32

func (r ResaleItemTradeFrequencyType) Val() int32 {
	return int32(r)
}

const (
	// ResaleItemTradeFrequencyTypeGlobal 同步全局转卖标准
	ResaleItemTradeFrequencyTypeGlobal ResaleItemTradeFrequencyType = 1
	// ResaleItemTradeFrequencyTypeSpecific 特定交易频次
	ResaleItemTradeFrequencyTypeSpecific ResaleItemTradeFrequencyType = 2
)
