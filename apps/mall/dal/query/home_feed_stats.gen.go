// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"marketplace_service/apps/mall/dal/model"
)

func newHomeFeedStats(db *gorm.DB, opts ...gen.DOOption) homeFeedStats {
	_homeFeedStats := homeFeedStats{}

	_homeFeedStats.homeFeedStatsDo.UseDB(db, opts...)
	_homeFeedStats.homeFeedStatsDo.UseModel(&model.HomeFeedStats{})

	tableName := _homeFeedStats.homeFeedStatsDo.TableName()
	_homeFeedStats.ALL = field.NewAsterisk(tableName)
	_homeFeedStats.ID = field.NewInt64(tableName, "id")
	_homeFeedStats.HomeFeedID = field.NewInt64(tableName, "home_feed_id")
	_homeFeedStats.ItemID = field.NewString(tableName, "item_id")
	_homeFeedStats.WishCount = field.NewInt32(tableName, "wish_count")
	_homeFeedStats.ViewCount = field.NewInt32(tableName, "view_count")
	_homeFeedStats.UpdatedAt = field.NewTime(tableName, "updated_at")

	_homeFeedStats.fillFieldMap()

	return _homeFeedStats
}

// homeFeedStats 商城首页统计表
type homeFeedStats struct {
	homeFeedStatsDo

	ALL        field.Asterisk
	ID         field.Int64  // 主键
	HomeFeedID field.Int64  // 首页ID
	ItemID     field.String // 商品ID
	WishCount  field.Int32  // 想要数量
	ViewCount  field.Int32  // 浏览数量
	UpdatedAt  field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (h homeFeedStats) Table(newTableName string) *homeFeedStats {
	h.homeFeedStatsDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h homeFeedStats) As(alias string) *homeFeedStats {
	h.homeFeedStatsDo.DO = *(h.homeFeedStatsDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *homeFeedStats) updateTableName(table string) *homeFeedStats {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewInt64(table, "id")
	h.HomeFeedID = field.NewInt64(table, "home_feed_id")
	h.ItemID = field.NewString(table, "item_id")
	h.WishCount = field.NewInt32(table, "wish_count")
	h.ViewCount = field.NewInt32(table, "view_count")
	h.UpdatedAt = field.NewTime(table, "updated_at")

	h.fillFieldMap()

	return h
}

func (h *homeFeedStats) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *homeFeedStats) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 6)
	h.fieldMap["id"] = h.ID
	h.fieldMap["home_feed_id"] = h.HomeFeedID
	h.fieldMap["item_id"] = h.ItemID
	h.fieldMap["wish_count"] = h.WishCount
	h.fieldMap["view_count"] = h.ViewCount
	h.fieldMap["updated_at"] = h.UpdatedAt
}

func (h homeFeedStats) clone(db *gorm.DB) homeFeedStats {
	h.homeFeedStatsDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h homeFeedStats) replaceDB(db *gorm.DB) homeFeedStats {
	h.homeFeedStatsDo.ReplaceDB(db)
	return h
}

type homeFeedStatsDo struct{ gen.DO }

type IHomeFeedStatsDo interface {
	gen.SubQuery
	Debug() IHomeFeedStatsDo
	WithContext(ctx context.Context) IHomeFeedStatsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IHomeFeedStatsDo
	WriteDB() IHomeFeedStatsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IHomeFeedStatsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IHomeFeedStatsDo
	Not(conds ...gen.Condition) IHomeFeedStatsDo
	Or(conds ...gen.Condition) IHomeFeedStatsDo
	Select(conds ...field.Expr) IHomeFeedStatsDo
	Where(conds ...gen.Condition) IHomeFeedStatsDo
	Order(conds ...field.Expr) IHomeFeedStatsDo
	Distinct(cols ...field.Expr) IHomeFeedStatsDo
	Omit(cols ...field.Expr) IHomeFeedStatsDo
	Join(table schema.Tabler, on ...field.Expr) IHomeFeedStatsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IHomeFeedStatsDo
	RightJoin(table schema.Tabler, on ...field.Expr) IHomeFeedStatsDo
	Group(cols ...field.Expr) IHomeFeedStatsDo
	Having(conds ...gen.Condition) IHomeFeedStatsDo
	Limit(limit int) IHomeFeedStatsDo
	Offset(offset int) IHomeFeedStatsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IHomeFeedStatsDo
	Unscoped() IHomeFeedStatsDo
	Create(values ...*model.HomeFeedStats) error
	CreateInBatches(values []*model.HomeFeedStats, batchSize int) error
	Save(values ...*model.HomeFeedStats) error
	First() (*model.HomeFeedStats, error)
	Take() (*model.HomeFeedStats, error)
	Last() (*model.HomeFeedStats, error)
	Find() ([]*model.HomeFeedStats, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.HomeFeedStats, err error)
	FindInBatches(result *[]*model.HomeFeedStats, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.HomeFeedStats) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IHomeFeedStatsDo
	Assign(attrs ...field.AssignExpr) IHomeFeedStatsDo
	Joins(fields ...field.RelationField) IHomeFeedStatsDo
	Preload(fields ...field.RelationField) IHomeFeedStatsDo
	FirstOrInit() (*model.HomeFeedStats, error)
	FirstOrCreate() (*model.HomeFeedStats, error)
	FindByPage(offset int, limit int) (result []*model.HomeFeedStats, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IHomeFeedStatsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (h homeFeedStatsDo) Debug() IHomeFeedStatsDo {
	return h.withDO(h.DO.Debug())
}

func (h homeFeedStatsDo) WithContext(ctx context.Context) IHomeFeedStatsDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h homeFeedStatsDo) ReadDB() IHomeFeedStatsDo {
	return h.Clauses(dbresolver.Read)
}

func (h homeFeedStatsDo) WriteDB() IHomeFeedStatsDo {
	return h.Clauses(dbresolver.Write)
}

func (h homeFeedStatsDo) Session(config *gorm.Session) IHomeFeedStatsDo {
	return h.withDO(h.DO.Session(config))
}

func (h homeFeedStatsDo) Clauses(conds ...clause.Expression) IHomeFeedStatsDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h homeFeedStatsDo) Returning(value interface{}, columns ...string) IHomeFeedStatsDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h homeFeedStatsDo) Not(conds ...gen.Condition) IHomeFeedStatsDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h homeFeedStatsDo) Or(conds ...gen.Condition) IHomeFeedStatsDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h homeFeedStatsDo) Select(conds ...field.Expr) IHomeFeedStatsDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h homeFeedStatsDo) Where(conds ...gen.Condition) IHomeFeedStatsDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h homeFeedStatsDo) Order(conds ...field.Expr) IHomeFeedStatsDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h homeFeedStatsDo) Distinct(cols ...field.Expr) IHomeFeedStatsDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h homeFeedStatsDo) Omit(cols ...field.Expr) IHomeFeedStatsDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h homeFeedStatsDo) Join(table schema.Tabler, on ...field.Expr) IHomeFeedStatsDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h homeFeedStatsDo) LeftJoin(table schema.Tabler, on ...field.Expr) IHomeFeedStatsDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h homeFeedStatsDo) RightJoin(table schema.Tabler, on ...field.Expr) IHomeFeedStatsDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h homeFeedStatsDo) Group(cols ...field.Expr) IHomeFeedStatsDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h homeFeedStatsDo) Having(conds ...gen.Condition) IHomeFeedStatsDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h homeFeedStatsDo) Limit(limit int) IHomeFeedStatsDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h homeFeedStatsDo) Offset(offset int) IHomeFeedStatsDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h homeFeedStatsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IHomeFeedStatsDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h homeFeedStatsDo) Unscoped() IHomeFeedStatsDo {
	return h.withDO(h.DO.Unscoped())
}

func (h homeFeedStatsDo) Create(values ...*model.HomeFeedStats) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h homeFeedStatsDo) CreateInBatches(values []*model.HomeFeedStats, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h homeFeedStatsDo) Save(values ...*model.HomeFeedStats) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h homeFeedStatsDo) First() (*model.HomeFeedStats, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeedStats), nil
	}
}

func (h homeFeedStatsDo) Take() (*model.HomeFeedStats, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeedStats), nil
	}
}

func (h homeFeedStatsDo) Last() (*model.HomeFeedStats, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeedStats), nil
	}
}

func (h homeFeedStatsDo) Find() ([]*model.HomeFeedStats, error) {
	result, err := h.DO.Find()
	return result.([]*model.HomeFeedStats), err
}

func (h homeFeedStatsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.HomeFeedStats, err error) {
	buf := make([]*model.HomeFeedStats, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h homeFeedStatsDo) FindInBatches(result *[]*model.HomeFeedStats, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h homeFeedStatsDo) Attrs(attrs ...field.AssignExpr) IHomeFeedStatsDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h homeFeedStatsDo) Assign(attrs ...field.AssignExpr) IHomeFeedStatsDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h homeFeedStatsDo) Joins(fields ...field.RelationField) IHomeFeedStatsDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h homeFeedStatsDo) Preload(fields ...field.RelationField) IHomeFeedStatsDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h homeFeedStatsDo) FirstOrInit() (*model.HomeFeedStats, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeedStats), nil
	}
}

func (h homeFeedStatsDo) FirstOrCreate() (*model.HomeFeedStats, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.HomeFeedStats), nil
	}
}

func (h homeFeedStatsDo) FindByPage(offset int, limit int) (result []*model.HomeFeedStats, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h homeFeedStatsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h homeFeedStatsDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h homeFeedStatsDo) Delete(models ...*model.HomeFeedStats) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *homeFeedStatsDo) withDO(do gen.Dao) *homeFeedStatsDo {
	h.DO = *do.(*gen.DO)
	return h
}
