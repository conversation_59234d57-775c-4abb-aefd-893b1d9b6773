// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameResaleListingsItem = "resale_listings_item"

// ResaleListingsItem 转卖挂单物品表
type ResaleListingsItem struct {
	ID                int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	SellerID          string                `gorm:"column:seller_id;type:varchar(32);not null;comment:卖家ID" json:"seller_id"`                                // 卖家ID
	ResaleListingsID  int64                 `gorm:"column:resale_listings_id;type:bigint;not null;comment:挂单ID" json:"resale_listings_id"`                   // 挂单ID
	Status            int32                 `gorm:"column:status;type:tinyint;not null;comment:状态（0-已下架，1-出售中，2-交易中，3-已出售）" json:"status"`                   // 状态（0-已下架，1-出售中，2-交易中，3-已出售）
	UserItemID        string                `gorm:"column:user_item_id;type:varchar(32);not null;comment:出售物品ID" json:"user_item_id"`                        // 出售物品ID
	TradeInfo         *datatypes.JSON       `gorm:"column:trade_info;type:json;comment:交易信息（云仓 user_items.trade_info）" json:"trade_info"`                    // 交易信息（云仓 user_items.trade_info）
	SalePrice         int64                 `gorm:"column:sale_price;type:bigint;not null;comment:单价(分)" json:"sale_price"`                                  // 单价(分)
	ItemID            string                `gorm:"column:item_id;type:varchar(32);not null;comment:商品ID" json:"item_id"`                                    // 商品ID
	BuyerID           string                `gorm:"column:buyer_id;type:varchar(32);comment:购买用户ID" json:"buyer_id"`                                         // 购买用户ID
	ResaleOrderID     *int64                `gorm:"column:resale_order_id;type:bigint;comment:转卖订单ID" json:"resale_order_id"`                                // 转卖订单ID
	ResaleOrderItemID *int64                `gorm:"column:resale_order_item_id;type:bigint;comment:转卖订单详情ID" json:"resale_order_item_id"`                    // 转卖订单详情ID
	Fee               int64                 `gorm:"column:fee;type:bigint;not null;comment:手续费" json:"fee"`                                                  // 手续费
	TradeAt           *time.Time            `gorm:"column:trade_at;type:datetime(3);comment:成交时间" json:"trade_at"`                                           // 成交时间
	CreatedAt         time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt         time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel             soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName ResaleListingsItem's table name
func (*ResaleListingsItem) TableName() string {
	return TableNameResaleListingsItem
}
